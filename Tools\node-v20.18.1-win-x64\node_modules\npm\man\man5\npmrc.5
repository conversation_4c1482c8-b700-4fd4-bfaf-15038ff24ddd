.TH "NPMRC" "5" "July 2024" "NPM@10.8.2" ""
.SH "NAME"
\fBnpmrc\fR - The npm config files
.SS "Description"
.P
npm gets its config settings from the command line, environment variables, and \fBnpmrc\fR files.
.P
The \fBnpm config\fR command can be used to update and edit the contents of the user and global npmrc files.
.P
For a list of available configuration options, see npm help config.
.SS "Files"
.P
The four relevant files are:
.RS 0
.IP \(bu 4
per-project config file (\fB/path/to/my/project/.npmrc\fR)
.IP \(bu 4
per-user config file (\fB~/.npmrc\fR)
.IP \(bu 4
global config file (\fB$PREFIX/etc/npmrc\fR)
.IP \(bu 4
npm builtin config file (\fB/path/to/npm/npmrc\fR)
.RE 0

.P
All npm config files are an ini-formatted list of \fBkey = value\fR parameters. Environment variables can be replaced using \fB${VARIABLE_NAME}\fR. For example:
.P
.RS 2
.nf
prefix = ${HOME}/.npm-packages
.fi
.RE
.P
Each of these files is loaded, and config options are resolved in priority order. For example, a setting in the userconfig file would override the setting in the globalconfig file.
.P
Array values are specified by adding "\[lB]\[rB]" after the key name. For example:
.P
.RS 2
.nf
key\[lB]\[rB] = "first value"
key\[lB]\[rB] = "second value"
.fi
.RE
.SS "Comments"
.P
Lines in \fB.npmrc\fR files are interpreted as comments when they begin with a \fB;\fR or \fB#\fR character. \fB.npmrc\fR files are parsed by \fBnpm/ini\fR \fI\(lahttps://github.com/npm/ini\(ra\fR, which specifies this comment syntax.
.P
For example:
.P
.RS 2
.nf
# last modified: 01 Jan 2016
; Set a new registry for a scoped package
@myscope:registry=https://mycustomregistry.example.org
.fi
.RE
.SS "Per-project config file"
.P
When working locally in a project, a \fB.npmrc\fR file in the root of the project (ie, a sibling of \fBnode_modules\fR and \fBpackage.json\fR) will set config values specific to this project.
.P
Note that this only applies to the root of the project that you're running npm in. It has no effect when your module is published. For example, you can't publish a module that forces itself to install globally, or in a different location.
.P
Additionally, this file is not read in global mode, such as when running \fBnpm install -g\fR.
.SS "Per-user config file"
.P
\fB$HOME/.npmrc\fR (or the \fBuserconfig\fR param, if set in the environment or on the command line)
.SS "Global config file"
.P
\fB$PREFIX/etc/npmrc\fR (or the \fBglobalconfig\fR param, if set above): This file is an ini-file formatted list of \fBkey = value\fR parameters. Environment variables can be replaced as above.
.SS "Built-in config file"
.P
\fBpath/to/npm/itself/npmrc\fR
.P
This is an unchangeable "builtin" configuration file that npm keeps consistent across updates. Set fields in here using the \fB./configure\fR script that comes with npm. This is primarily for distribution maintainers to override default configs in a standard and consistent manner.
.SS "Auth related configuration"
.P
The settings \fB_auth\fR, \fB_authToken\fR, \fBusername\fR and \fB_password\fR must all be scoped to a specific registry. This ensures that \fBnpm\fR will never send credentials to the wrong host.
.P
The full list is:
.RS 0
.IP \(bu 4
\fB_auth\fR (base64 authentication string)
.IP \(bu 4
\fB_authToken\fR (authentication token)
.IP \(bu 4
\fBusername\fR
.IP \(bu 4
\fB_password\fR
.IP \(bu 4
\fBemail\fR
.IP \(bu 4
\fBcertfile\fR (path to certificate file)
.IP \(bu 4
\fBkeyfile\fR (path to key file)
.RE 0

.P
In order to scope these values, they must be prefixed by a URI fragment. If the credential is meant for any request to a registry on a single host, the scope may look like \fB//registry.npmjs.org/:\fR. If it must be scoped to a specific path on the host that path may also be provided, such as \fB//my-custom-registry.org/unique/path:\fR.
.P
.RS 2
.nf
; bad config
_authToken=MYTOKEN

; good config
@myorg:registry=https://somewhere-else.com/myorg
@another:registry=https://somewhere-else.com/another
//registry.npmjs.org/:_authToken=MYTOKEN
; would apply to both @myorg and @another
; //somewhere-else.com/:_authToken=MYTOKEN
; would apply only to @myorg
//somewhere-else.com/myorg/:_authToken=MYTOKEN1
; would apply only to @another
//somewhere-else.com/another/:_authToken=MYTOKEN2
.fi
.RE
.SS "See also"
.RS 0
.IP \(bu 4
npm help folders
.IP \(bu 4
npm help config
.IP \(bu 4
npm help config
.IP \(bu 4
\fBpackage.json\fR \fI\(la/configuring-npm/package-json\(ra\fR
.IP \(bu 4
npm help npm
.RE 0
