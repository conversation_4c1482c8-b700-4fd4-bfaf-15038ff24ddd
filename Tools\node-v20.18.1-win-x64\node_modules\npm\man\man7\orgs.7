.TH "ORGS" "7" "July 2024" "NPM@10.8.2" ""
.SH "NAME"
\fBorgs\fR - Working with Teams & Orgs
.SS "Description"
.P
There are three levels of org users:
.RS 0
.IP 1. 4
Super admin, controls billing & adding people to the org.
.IP 2. 4
Team admin, manages team membership & package access.
.IP 3. 4
Developer, works on packages they are given access to.
.RE 0

.P
The super admin is the only person who can add users to the org because it impacts the monthly bill. The super admin will use the website to manage membership. Every org has a \fBdevelopers\fR team that all users are automatically added to.
.P
The team admin is the person who manages team creation, team membership, and package access for teams. The team admin grants package access to teams, not individuals.
.P
The developer will be able to access packages based on the teams they are on. Access is either read-write or read-only.
.P
There are two main commands:
.RS 0
.IP 1. 4
\fBnpm team\fR see npm help team for more details
.IP 2. 4
\fBnpm access\fR see npm help access for more details
.RE 0

.SS "Team Admins create teams"
.RS 0
.IP \(bu 4
Check who you\[cq]ve added to your org:
.RE 0

.P
.RS 2
.nf
npm team ls <org>:developers
.fi
.RE
.RS 0
.IP \(bu 4
Each org is automatically given a \fBdevelopers\fR team, so you can see the whole list of team members in your org. This team automatically gets read-write access to all packages, but you can change that with the \fBaccess\fR command.
.IP \(bu 4
Create a new team:
.RE 0

.P
.RS 2
.nf
npm team create <org:team>
.fi
.RE
.RS 0
.IP \(bu 4
Add members to that team:
.RE 0

.P
.RS 2
.nf
npm team add <org:team> <user>
.fi
.RE
.SS "Publish a package and adjust package access"
.RS 0
.IP \(bu 4
In package directory, run
.RE 0

.P
.RS 2
.nf
npm init --scope=<org>
.fi
.RE
.P
to scope it for your org & publish as usual
.RS 0
.IP \(bu 4
Grant access:
.RE 0

.P
.RS 2
.nf
npm access grant <read-only|read-write> <org:team> \[lB]<package>\[rB]
.fi
.RE
.RS 0
.IP \(bu 4
Revoke access:
.RE 0

.P
.RS 2
.nf
npm access revoke <org:team> \[lB]<package>\[rB]
.fi
.RE
.SS "Monitor your package access"
.RS 0
.IP \(bu 4
See what org packages a team member can access:
.RE 0

.P
.RS 2
.nf
npm access ls-packages <org> <user>
.fi
.RE
.RS 0
.IP \(bu 4
See packages available to a specific team:
.RE 0

.P
.RS 2
.nf
npm access ls-packages <org:team>
.fi
.RE
.RS 0
.IP \(bu 4
Check which teams are collaborating on a package:
.RE 0

.P
.RS 2
.nf
npm access ls-collaborators <pkg>
.fi
.RE
.SS "See also"
.RS 0
.IP \(bu 4
npm help team
.IP \(bu 4
npm help access
.IP \(bu 4
npm help scope
.RE 0
