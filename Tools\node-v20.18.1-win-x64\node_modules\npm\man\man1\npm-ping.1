.TH "NPM-PING" "1" "July 2024" "NPM@10.8.2" ""
.SH "NAME"
\fBnpm-ping\fR - Ping npm registry
.SS "Synopsis"
.P
.RS 2
.nf
npm ping
.fi
.RE
.P
Note: This command is unaware of workspaces.
.SS "Description"
.P
Ping the configured or given npm registry and verify authentication. If it works it will output something like:
.P
.RS 2
.nf
npm notice PING https://registry.npmjs.org/
npm notice PONG 255ms
.fi
.RE
.P
otherwise you will get an error:
.P
.RS 2
.nf
npm notice PING http://foo.com/
npm ERR! code E404
npm ERR! 404 Not Found - GET http://www.foo.com/-/ping?write=true
.fi
.RE
.SS "Configuration"
.SS "\fBregistry\fR"
.RS 0
.IP \(bu 4
Default: "https://registry.npmjs.org/"
.IP \(bu 4
Type: URL
.RE 0

.P
The base URL of the npm registry.
.SS "See Also"
.RS 0
.IP \(bu 4
npm help doctor
.IP \(bu 4
npm help config
.IP \(bu 4
npm help npmrc
.RE 0
