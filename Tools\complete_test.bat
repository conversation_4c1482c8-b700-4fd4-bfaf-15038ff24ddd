@echo off
echo ========================================
echo SLG Game Complete Environment Test
echo ========================================

echo.
echo 1. Testing PHP Environment...
Tools\php-binary\php.exe --version
if %errorlevel% equ 0 (
    echo [OK] PHP 8.3.23 Working
) else (
    echo [FAIL] PHP Test Failed
)

echo.
echo 2. Testing Node.js Environment...
Tools\node-v20.18.1-win-x64\node.exe --version
if %errorlevel% equ 0 (
    echo [OK] Node.js v20.18.1 Working
) else (
    echo [FAIL] Node.js Test Failed
)

echo.
echo 3. Testing Composer...
Tools\php-binary\php.exe Tools\composer.phar --version
if %errorlevel% equ 0 (
    echo [OK] Composer Working
) else (
    echo [FAIL] Composer Test Failed
)

echo.
echo 4. Testing TypeScript...
cd Client\slg-game
..\..\Tools\node-v20.18.1-win-x64\node.exe node_modules\typescript\bin\tsc --version
if %errorlevel% equ 0 (
    echo [OK] TypeScript 5.8.3 Working
) else (
    echo [FAIL] TypeScript Test Failed
)
cd ..\..

echo.
echo 5. Testing Laravel Framework...
cd Server
..\Tools\php-binary\php.exe artisan --version
if %errorlevel% equ 0 (
    echo [OK] Laravel 10.48.29 Working
) else (
    echo [FAIL] Laravel Test Failed
)
cd ..

echo.
echo 6. Checking Key Files...
if exist "Tools\mysql-8.4.5\bin\mysqld.exe" (
    echo [OK] MySQL 8.4.5 Executable Found
) else (
    echo [FAIL] MySQL Executable Not Found
)

if exist "Tools\Redis-7.2.10\redis-server.exe" (
    echo [OK] Redis 7.2.10 Executable Found
) else (
    echo [FAIL] Redis Executable Not Found
)

if exist "Tools\phpMyAdmin\index.php" (
    echo [OK] phpMyAdmin Found
) else (
    echo [FAIL] phpMyAdmin Not Found
)

if exist "Server\.env" (
    echo [OK] Laravel .env Config Found
) else (
    echo [FAIL] Laravel .env Config Not Found
)

if exist "Client\slg-game\package.json" (
    echo [OK] Vue+TypeScript Project Config Found
) else (
    echo [FAIL] Vue+TypeScript Project Config Not Found
)

echo.
echo 7. Checking phpMyAdmin Configuration...
findstr "blowfish_secret.*slg-game" Tools\phpMyAdmin\config.inc.php >nul
if %errorlevel% equ 0 (
    echo [OK] phpMyAdmin blowfish_secret Configured
) else (
    echo [FAIL] phpMyAdmin blowfish_secret Not Configured
)

findstr "AllowNoPassword.*true" Tools\phpMyAdmin\config.inc.php >nul
if %errorlevel% equ 0 (
    echo [OK] phpMyAdmin Allow No Password Enabled
) else (
    echo [FAIL] phpMyAdmin Allow No Password Disabled
)

echo.
echo ========================================
echo Environment Test Complete!
echo ========================================
echo.
echo Development Environment Components:
echo - PHP 8.3.23 + Laravel 10.48.29
echo - Node.js v20.18.1 + Vue 3 + TypeScript 5.8.3
echo - MySQL 8.4.5 + Redis 7.2.10
echo - phpMyAdmin Database Management Tool
echo.
echo Database Connection Info:
echo - Host: 127.0.0.1:3306
echo - Username: root
echo - Password: (empty)
echo - Database: slg_game
echo.
pause
