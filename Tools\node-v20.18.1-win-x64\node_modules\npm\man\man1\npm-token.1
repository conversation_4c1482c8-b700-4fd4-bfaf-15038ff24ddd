.TH "NPM-TOKEN" "1" "July 2024" "NPM@10.8.2" ""
.SH "NAME"
\fBnpm-token\fR - Manage your authentication tokens
.SS "Synopsis"
.P
.RS 2
.nf
npm token list
npm token revoke <id|token>
npm token create \[lB]--read-only\[rB] \[lB]--cidr=list\[rB]
.fi
.RE
.P
Note: This command is unaware of workspaces.
.SS "Description"
.P
This lets you list, create and revoke authentication tokens.
.RS 0
.IP \(bu 4
\fBnpm token list\fR: Shows a table of all active authentication tokens. You can request this as JSON with \fB--json\fR or tab-separated values with \fB--parseable\fR.
.RE 0

.P
.RS 2
.nf
Read only token npm_1f… with id 7f3134 created 2017-10-21

Publish token npm_af…  with id c03241 created 2017-10-02
with IP Whitelist: ***********/24

Publish token npm_… with id e0cf92 created 2017-10-02

.fi
.RE
.RS 0
.IP \(bu 4
\fBnpm token create \[lB]--read-only\[rB] \[lB]--cidr=<cidr-ranges>\[rB]\fR: Create a new authentication token. It can be \fB--read-only\fR, or accept a list of \fBCIDR\fR \fI\(lahttps://en.wikipedia.org/wiki/Classless_Inter-Domain_Routing\(ra\fR ranges with which to limit use of this token. This will prompt you for your password, and, if you have two-factor authentication enabled, an otp.
.P
Currently, the cli can not generate automation tokens. Please refer to the \fBdocs website\fR \fI\(lahttps://docs.npmjs.com/creating-and-viewing-access-tokens\(ra\fR for more information on generating automation tokens.
.RE 0

.P
.RS 2
.nf
Created publish token a73c9572-f1b9-8983-983d-ba3ac3cc913d
.fi
.RE
.RS 0
.IP \(bu 4
\fBnpm token revoke <token|id>\fR: Immediately removes an authentication token from the registry. You will no longer be able to use it. This can accept both complete tokens (such as those you get back from \fBnpm token create\fR, and those found in your \fB.npmrc\fR), and ids as seen in the parseable or json output of \fBnpm token list\fR. This will NOT accept the truncated token found in the normal \fBnpm token list\fR output.
.RE 0

.SS "Configuration"
.SS "\fBread-only\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
This is used to mark a token as unable to publish when configuring limited access tokens with the \fBnpm token create\fR command.
.SS "\fBcidr\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null or String (can be set multiple times)
.RE 0

.P
This is a list of CIDR address to be used when configuring limited access tokens with the \fBnpm token create\fR command.
.SS "\fBregistry\fR"
.RS 0
.IP \(bu 4
Default: "https://registry.npmjs.org/"
.IP \(bu 4
Type: URL
.RE 0

.P
The base URL of the npm registry.
.SS "\fBotp\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null or String
.RE 0

.P
This is a one-time password from a two-factor authenticator. It's needed when publishing or changing package permissions with \fBnpm access\fR.
.P
If not set, and a registry response fails with a challenge for a one-time password, npm will prompt on the command line for one.
.SS "See Also"
.RS 0
.IP \(bu 4
npm help adduser
.IP \(bu 4
npm help registry
.IP \(bu 4
npm help config
.IP \(bu 4
npm help npmrc
.IP \(bu 4
npm help owner
.IP \(bu 4
npm help whoami
.IP \(bu 4
npm help profile
.RE 0
