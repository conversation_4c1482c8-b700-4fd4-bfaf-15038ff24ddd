.TH "NPM-PUBLISH" "1" "July 2024" "NPM@10.8.2" ""
.SH "NAME"
\fBnpm-publish\fR - Publish a package
.SS "Synopsis"
.P
.RS 2
.nf
npm publish <package-spec>
.fi
.RE
.SS "Description"
.P
Publishes a package to the registry so that it can be installed by name.
.P
By default npm will publish to the public registry. This can be overridden by specifying a different default registry or using a npm help scope in the name, combined with a scope-configured registry (see \fB\fBpackage.json\fR\fR \fI\(la/configuring-npm/package-json\(ra\fR).
.P
A \fBpackage\fR is interpreted the same way as other commands (like \fBnpm install\fR) and can be:
.RS 0
.IP \(bu 4
a) a folder containing a program described by a \fB\fBpackage.json\fR\fR \fI\(la/configuring-npm/package-json\(ra\fR file
.IP \(bu 4
b) a gzipped tarball containing (a)
.IP \(bu 4
c) a url that resolves to (b)
.IP \(bu 4
d) a \fB<name>@<version>\fR that is published on the registry (see npm help registry) with (c)
.IP \(bu 4
e) a \fB<name>@<tag>\fR (see npm help dist-tag) that points to (d)
.IP \(bu 4
f) a \fB<name>\fR that has a "latest" tag satisfying (e)
.IP \(bu 4
g) a \fB<git remote url>\fR that resolves to (a)
.RE 0

.P
The publish will fail if the package name and version combination already exists in the specified registry.
.P
Once a package is published with a given name and version, that specific name and version combination can never be used again, even if it is removed with npm help unpublish.
.P
As of \fBnpm@5\fR, both a sha1sum and an integrity field with a sha512sum of the tarball will be submitted to the registry during publication. Subsequent installs will use the strongest supported algorithm to verify downloads.
.P
Similar to \fB--dry-run\fR see npm help pack, which figures out the files to be included and packs them into a tarball to be uploaded to the registry.
.SS "Files included in package"
.P
To see what will be included in your package, run \fBnpm pack --dry-run\fR. All files are included by default, with the following exceptions:
.RS 0
.IP \(bu 4
Certain files that are relevant to package installation and distribution are always included. For example, \fBpackage.json\fR, \fBREADME.md\fR, \fBLICENSE\fR, and so on.
.IP \(bu 4
If there is a "files" list in \fB\fBpackage.json\fR\fR \fI\(la/configuring-npm/package-json\(ra\fR, then only the files specified will be included. (If directories are specified, then they will be walked recursively and their contents included, subject to the same ignore rules.)
.IP \(bu 4
If there is a \fB.gitignore\fR or \fB.npmignore\fR file, then ignored files in that and all child directories will be excluded from the package. If \fIboth\fR files exist, then the \fB.gitignore\fR is ignored, and only the \fB.npmignore\fR is used.
.P
\fB.npmignore\fR files follow the \fBsame pattern rules\fR \fI\(lahttps://git-scm.com/book/en/v2/Git-Basics-Recording-Changes-to-the-Repository#_ignoring\(ra\fR as \fB.gitignore\fR files
.IP \(bu 4
If the file matches certain patterns, then it will \fInever\fR be included, unless explicitly added to the \fB"files"\fR list in \fBpackage.json\fR, or un-ignored with a \fB!\fR rule in a \fB.npmignore\fR or \fB.gitignore\fR file.
.IP \(bu 4
Symbolic links are never included in npm packages.
.RE 0

.P
See npm help developers for full details on what's included in the published package, as well as details on how the package is built.
.SS "Configuration"
.SS "\fBtag\fR"
.RS 0
.IP \(bu 4
Default: "latest"
.IP \(bu 4
Type: String
.RE 0

.P
If you ask npm to install a package and don't tell it a specific version, then it will install the specified tag.
.P
It is the tag added to the package@version specified in the \fBnpm dist-tag
add\fR command, if no explicit tag is given.
.P
When used by the \fBnpm diff\fR command, this is the tag used to fetch the tarball that will be compared with the local files by default.
.P
If used in the \fBnpm publish\fR command, this is the tag that will be added to the package submitted to the registry.
.SS "\fBaccess\fR"
.RS 0
.IP \(bu 4
Default: 'public' for new packages, existing packages it will not change the current level
.IP \(bu 4
Type: null, "restricted", or "public"
.RE 0

.P
If you do not want your scoped package to be publicly viewable (and installable) set \fB--access=restricted\fR.
.P
Unscoped packages can not be set to \fBrestricted\fR.
.P
Note: This defaults to not changing the current access level for existing packages. Specifying a value of \fBrestricted\fR or \fBpublic\fR during publish will change the access for an existing package the same way that \fBnpm access set
status\fR would.
.SS "\fBdry-run\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Indicates that you don't want npm to make any changes and that it should only report what it would have done. This can be passed into any of the commands that modify your local installation, eg, \fBinstall\fR, \fBupdate\fR, \fBdedupe\fR, \fBuninstall\fR, as well as \fBpack\fR and \fBpublish\fR.
.P
Note: This is NOT honored by other network related commands, eg \fBdist-tags\fR, \fBowner\fR, etc.
.SS "\fBotp\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null or String
.RE 0

.P
This is a one-time password from a two-factor authenticator. It's needed when publishing or changing package permissions with \fBnpm access\fR.
.P
If not set, and a registry response fails with a challenge for a one-time password, npm will prompt on the command line for one.
.SS "\fBworkspace\fR"
.RS 0
.IP \(bu 4
Default:
.IP \(bu 4
Type: String (can be set multiple times)
.RE 0

.P
Enable running a command in the context of the configured workspaces of the current project while filtering by running only the workspaces defined by this configuration option.
.P
Valid values for the \fBworkspace\fR config are either:
.RS 0
.IP \(bu 4
Workspace names
.IP \(bu 4
Path to a workspace directory
.IP \(bu 4
Path to a parent workspace directory (will result in selecting all workspaces within that folder)
.RE 0

.P
When set for the \fBnpm init\fR command, this may be set to the folder of a workspace which does not yet exist, to create the folder and set it up as a brand new workspace within the project.
.P
This value is not exported to the environment for child processes.
.SS "\fBworkspaces\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null or Boolean
.RE 0

.P
Set to true to run the command in the context of \fBall\fR configured workspaces.
.P
Explicitly setting this to false will cause commands like \fBinstall\fR to ignore workspaces altogether. When not set explicitly:
.RS 0
.IP \(bu 4
Commands that operate on the \fBnode_modules\fR tree (install, update, etc.) will link workspaces into the \fBnode_modules\fR folder. - Commands that do other things (test, exec, publish, etc.) will operate on the root project, \fIunless\fR one or more workspaces are specified in the \fBworkspace\fR config.
.RE 0

.P
This value is not exported to the environment for child processes.
.SS "\fBinclude-workspace-root\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Include the workspace root when workspaces are enabled for a command.
.P
When false, specifying individual workspaces via the \fBworkspace\fR config, or all workspaces via the \fBworkspaces\fR flag, will cause npm to operate only on the specified workspaces, and not on the root project.
.P
This value is not exported to the environment for child processes.
.SS "\fBprovenance\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
When publishing from a supported cloud CI/CD system, the package will be publicly linked to where it was built and published from.
.P
This config can not be used with: \fBprovenance-file\fR
.SS "\fBprovenance-file\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: Path
.RE 0

.P
When publishing, the provenance bundle at the given path will be used.
.P
This config can not be used with: \fBprovenance\fR
.SS "See Also"
.RS 0
.IP \(bu 4
npm help "package spec"
.IP \(bu 4
\fBnpm-packlist package\fR \fI\(lahttp://npm.im/npm-packlist\(ra\fR
.IP \(bu 4
npm help registry
.IP \(bu 4
npm help scope
.IP \(bu 4
npm help adduser
.IP \(bu 4
npm help owner
.IP \(bu 4
npm help deprecate
.IP \(bu 4
npm help dist-tag
.IP \(bu 4
npm help pack
.IP \(bu 4
npm help profile
.RE 0
