<!DOCTYPE html><html><head>
<meta charset="utf-8">
<title>npm-dist-tag</title>
<style>
body {
    background-color: #ffffff;
    color: #24292e;

    margin: 0;

    line-height: 1.5;

    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
}
#rainbar {
    height: 10px;
    background-image: linear-gradient(139deg, #fb8817, #ff4b01, #c12127, #e02aff);
}

a {
    text-decoration: none;
    color: #0366d6;
}
a:hover {
    text-decoration: underline;
}

pre {
    margin: 1em 0px;
    padding: 1em;
    border: solid 1px #e1e4e8;
    border-radius: 6px;

    display: block;
    overflow: auto;

    white-space: pre;

    background-color: #f6f8fa;
    color: #393a34;
}
code {
    font-family: SFMono-Regular, <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, <PERSON>urier, monospace;
    font-size: 85%;
    padding: 0.2em 0.4em;
    background-color: #f6f8fa;
    color: #393a34;
}
pre > code {
    padding: 0;
    background-color: inherit;
    color: inherit;
}
h1, h2, h3 {
    font-weight: 600;
}

#logobar {
    background-color: #333333;
    margin: 0 auto;
    padding: 1em 4em;
}
#logobar .logo {
    float: left;
}
#logobar .title {
    font-weight: 600;
    color: #dddddd;
    float: left;
    margin: 5px 0 0 1em;
}
#logobar:after {
    content: "";
    display: block;
    clear: both;
}

#content {
    margin: 0 auto;
    padding: 0 4em;
}

#table_of_contents > h2 {
    font-size: 1.17em;
}
#table_of_contents ul:first-child {
    border: solid 1px #e1e4e8;
    border-radius: 6px;
    padding: 1em;
    background-color: #f6f8fa;
    color: #393a34;
}
#table_of_contents ul {
    list-style-type: none;
    padding-left: 1.5em;
}
#table_of_contents li {
    font-size: 0.9em;
}
#table_of_contents li a {
    color: #000000;
}

header.title {
    border-bottom: solid 1px #e1e4e8;
}
header.title > h1 {
    margin-bottom: 0.25em;
}
header.title > .description {
    display: block;
    margin-bottom: 0.5em;
    line-height: 1;
}

header.title .version {
    font-size: 0.8em;
    color: #666666;
}

footer#edit {
    border-top: solid 1px #e1e4e8;
    margin: 3em 0 4em 0;
    padding-top: 2em;
}
</style>
</head>
<body>
<div id="banner">
<div id="rainbar"></div>
<div id="logobar">
<svg class="logo" role="img" height="32" width="32" viewBox="0 0 700 700">
<polygon fill="#cb0000" points="0,700 700,700 700,0 0,0"></polygon>
<polygon fill="#ffffff" points="150,550 350,550 350,250 450,250 450,550 550,550 550,150 150,150"></polygon>
</svg>
<div class="title">
npm command-line interface
</div>
</div>
</div>

<section id="content">
<header class="title">
<h1 id="----npm-dist-tag----1082">
    <span>npm-dist-tag</span>
    <span class="version">@10.8.2</span>
</h1>
<span class="description">Modify package distribution tags</span>
</header>

<section id="table_of_contents">
<h2 id="table-of-contents">Table of contents</h2>
<div id="_table_of_contents"><ul><li><a href="#synopsis">Synopsis</a></li><li><a href="#description">Description</a></li><li><a href="#purpose">Purpose</a></li><li><a href="#caveats">Caveats</a></li><li><a href="#configuration">Configuration</a></li><ul><li><a href="#workspace"><code>workspace</code></a></li><li><a href="#workspaces"><code>workspaces</code></a></li><li><a href="#include-workspace-root"><code>include-workspace-root</code></a></li></ul><li><a href="#see-also">See Also</a></li></ul></div>
</section>

<div id="_content"><h3 id="synopsis">Synopsis</h3>
<pre><code class="language-bash">npm dist-tag add &lt;package-spec (with version)&gt; [&lt;tag&gt;]
npm dist-tag rm &lt;package-spec&gt; &lt;tag&gt;
npm dist-tag ls [&lt;package-spec&gt;]

alias: dist-tags
</code></pre>
<h3 id="description">Description</h3>
<p>Add, remove, and enumerate distribution tags on a package:</p>
<ul>
<li>
<p>add: Tags the specified version of the package with the specified tag,
or the <a href="../using-npm/config#tag.html"><code>--tag</code> config</a> if not specified. If you have
two-factor authentication on auth-and-writes then you’ll need to include a
one-time password on the command line with
<code>--otp &lt;one-time password&gt;</code>, or go through a second factor flow based on your <code>authtype</code>.</p>
</li>
<li>
<p>rm: Clear a tag that is no longer in use from the package. If you have
two-factor authentication on auth-and-writes then you’ll need to include
a one-time password on the command line with <code>--otp &lt;one-time password&gt;</code>,
or go through a second factor flow based on your <code>authtype</code></p>
</li>
<li>
<p>ls: Show all of the dist-tags for a package, defaulting to the package in
the current prefix. This is the default action if none is specified.</p>
</li>
</ul>
<p>A tag can be used when installing packages as a reference to a version instead
of using a specific version number:</p>
<pre><code class="language-bash">npm install &lt;name&gt;@&lt;tag&gt;
</code></pre>
<p>When installing dependencies, a preferred tagged version may be specified:</p>
<pre><code class="language-bash">npm install --tag &lt;tag&gt;
</code></pre>
<p>(This also applies to any other commands that resolve and install
dependencies, such as <code>npm dedupe</code>, <code>npm update</code>, and <code>npm audit fix</code>.)</p>
<p>Publishing a package sets the <code>latest</code> tag to the published version unless the
<code>--tag</code> option is used. For example, <code>npm publish --tag=beta</code>.</p>
<p>By default, <code>npm install &lt;pkg&gt;</code> (without any <code>@&lt;version&gt;</code> or <code>@&lt;tag&gt;</code>
specifier) installs the <code>latest</code> tag.</p>
<h3 id="purpose">Purpose</h3>
<p>Tags can be used to provide an alias instead of version numbers.</p>
<p>For example, a project might choose to have multiple streams of development
and use a different tag for each stream, e.g., <code>stable</code>, <code>beta</code>, <code>dev</code>,
<code>canary</code>.</p>
<p>By default, the <code>latest</code> tag is used by npm to identify the current version
of a package, and <code>npm install &lt;pkg&gt;</code> (without any <code>@&lt;version&gt;</code> or <code>@&lt;tag&gt;</code>
specifier) installs the <code>latest</code> tag. Typically, projects only use the
<code>latest</code> tag for stable release versions, and use other tags for unstable
versions such as prereleases.</p>
<p>The <code>next</code> tag is used by some projects to identify the upcoming version.</p>
<p>Other than <code>latest</code>, no tag has any special significance to npm itself.</p>
<h3 id="caveats">Caveats</h3>
<p>This command used to be known as <code>npm tag</code>, which only created new tags,
and so had a different syntax.</p>
<p>Tags must share a namespace with version numbers, because they are
specified in the same slot: <code>npm install &lt;pkg&gt;@&lt;version&gt;</code> vs
<code>npm install &lt;pkg&gt;@&lt;tag&gt;</code>.</p>
<p>Tags that can be interpreted as valid semver ranges will be rejected. For
example, <code>v1.4</code> cannot be used as a tag, because it is interpreted by
semver as <code>&gt;=1.4.0 &lt;1.5.0</code>.  See <a href="https://github.com/npm/npm/issues/6082">https://github.com/npm/npm/issues/6082</a>.</p>
<p>The simplest way to avoid semver problems with tags is to use tags that do
not begin with a number or the letter <code>v</code>.</p>
<h3 id="configuration">Configuration</h3>
<h4 id="workspace"><code>workspace</code></h4>
<ul>
<li>Default:</li>
<li>Type: String (can be set multiple times)</li>
</ul>
<p>Enable running a command in the context of the configured workspaces of the
current project while filtering by running only the workspaces defined by
this configuration option.</p>
<p>Valid values for the <code>workspace</code> config are either:</p>
<ul>
<li>Workspace names</li>
<li>Path to a workspace directory</li>
<li>Path to a parent workspace directory (will result in selecting all
workspaces within that folder)</li>
</ul>
<p>When set for the <code>npm init</code> command, this may be set to the folder of a
workspace which does not yet exist, to create the folder and set it up as a
brand new workspace within the project.</p>
<p>This value is not exported to the environment for child processes.</p>
<h4 id="workspaces"><code>workspaces</code></h4>
<ul>
<li>Default: null</li>
<li>Type: null or Boolean</li>
</ul>
<p>Set to true to run the command in the context of <strong>all</strong> configured
workspaces.</p>
<p>Explicitly setting this to false will cause commands like <code>install</code> to
ignore workspaces altogether. When not set explicitly:</p>
<ul>
<li>Commands that operate on the <code>node_modules</code> tree (install, update, etc.)
will link workspaces into the <code>node_modules</code> folder. - Commands that do
other things (test, exec, publish, etc.) will operate on the root project,
<em>unless</em> one or more workspaces are specified in the <code>workspace</code> config.</li>
</ul>
<p>This value is not exported to the environment for child processes.</p>
<h4 id="include-workspace-root"><code>include-workspace-root</code></h4>
<ul>
<li>Default: false</li>
<li>Type: Boolean</li>
</ul>
<p>Include the workspace root when workspaces are enabled for a command.</p>
<p>When false, specifying individual workspaces via the <code>workspace</code> config, or
all workspaces via the <code>workspaces</code> flag, will cause npm to operate only on
the specified workspaces, and not on the root project.</p>
<p>This value is not exported to the environment for child processes.</p>
<h3 id="see-also">See Also</h3>
<ul>
<li><a href="../using-npm/package-spec.html">package spec</a></li>
<li><a href="../commands/npm-publish.html">npm publish</a></li>
<li><a href="../commands/npm-install.html">npm install</a></li>
<li><a href="../commands/npm-dedupe.html">npm dedupe</a></li>
<li><a href="../using-npm/registry.html">npm registry</a></li>
<li><a href="../commands/npm-config.html">npm config</a></li>
<li><a href="../configuring-npm/npmrc.html">npmrc</a></li>
</ul></div>

<footer id="edit">
<a href="https://github.com/npm/cli/edit/latest/docs/content/commands/npm-dist-tag.md">
<svg role="img" viewBox="0 0 16 16" width="16" height="16" fill="currentcolor" style="vertical-align: text-bottom; margin-right: 0.3em;">
<path fill-rule="evenodd" d="M11.013 1.427a1.75 1.75 0 012.474 0l1.086 1.086a1.75 1.75 0 010 2.474l-8.61 8.61c-.21.21-.47.364-.756.445l-3.251.93a.75.75 0 01-.927-.928l.929-3.25a1.75 1.75 0 01.445-.758l8.61-8.61zm1.414 1.06a.25.25 0 00-.354 0L10.811 3.75l1.439 1.44 1.263-1.263a.25.25 0 000-.354l-1.086-1.086zM11.189 6.25L9.75 4.81l-6.286 6.287a.25.25 0 00-.064.108l-.558 1.953 1.953-.558a.249.249 0 00.108-.064l6.286-6.286z"></path>
</svg>
Edit this page on GitHub
</a>
</footer>
</section>



</body></html>