import type { 
  ApiResponse, 
  GameStatus, 
  GameInfo, 
  Player, 
  Building,
  LoginCredentials,
  RegisterData,
  AuthUser
} from '@/types/game'

export class GameAPI {
  private baseURL = 'http://127.0.0.1:8000/api'

  // 游戏状态相关
  async getGameStatus(): Promise<ApiResponse<GameStatus>> {
    const response = await fetch(`${this.baseURL}/game/status`)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    return response.json()
  }

  async getGameInfo(): Promise<ApiResponse<GameInfo>> {
    const response = await fetch(`${this.baseURL}/game/info`)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    return response.json()
  }

  // 玩家相关
  async getPlayerInfo(playerId: number): Promise<ApiResponse<Player>> {
    const response = await fetch(`${this.baseURL}/player/${playerId}`)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    return response.json()
  }

  async updatePlayerResources(playerId: number, resources: Partial<Player['resources']>): Promise<ApiResponse<Player>> {
    const response = await fetch(`${this.baseURL}/player/${playerId}/resources`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(resources)
    })
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    return response.json()
  }

  // 建筑相关
  async getPlayerBuildings(playerId: number): Promise<ApiResponse<Building[]>> {
    const response = await fetch(`${this.baseURL}/player/${playerId}/buildings`)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    return response.json()
  }

  async upgradeBuilding(buildingId: number): Promise<ApiResponse<Building>> {
    const response = await fetch(`${this.baseURL}/building/${buildingId}/upgrade`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    return response.json()
  }

  // 认证相关
  async login(credentials: LoginCredentials): Promise<ApiResponse<{ user: AuthUser, token: string }>> {
    const response = await fetch(`${this.baseURL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials)
    })
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    return response.json()
  }

  async register(data: RegisterData): Promise<ApiResponse<{ user: AuthUser, token: string }>> {
    const response = await fetch(`${this.baseURL}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    })
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    return response.json()
  }

  async logout(): Promise<ApiResponse<null>> {
    const response = await fetch(`${this.baseURL}/auth/logout`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.getToken()}`
      }
    })
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    return response.json()
  }

  // 工具方法
  private getToken(): string | null {
    return localStorage.getItem('auth_token')
  }

  setToken(token: string): void {
    localStorage.setItem('auth_token', token)
  }

  removeToken(): void {
    localStorage.removeItem('auth_token')
  }
}

// 导出单例实例
export const gameAPI = new GameAPI()
