<!DOCTYPE html><html><head>
<meta charset="utf-8">
<title>registry</title>
<style>
body {
    background-color: #ffffff;
    color: #24292e;

    margin: 0;

    line-height: 1.5;

    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
}
#rainbar {
    height: 10px;
    background-image: linear-gradient(139deg, #fb8817, #ff4b01, #c12127, #e02aff);
}

a {
    text-decoration: none;
    color: #0366d6;
}
a:hover {
    text-decoration: underline;
}

pre {
    margin: 1em 0px;
    padding: 1em;
    border: solid 1px #e1e4e8;
    border-radius: 6px;

    display: block;
    overflow: auto;

    white-space: pre;

    background-color: #f6f8fa;
    color: #393a34;
}
code {
    font-family: SFMono-Regular, <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, Courier, monospace;
    font-size: 85%;
    padding: 0.2em 0.4em;
    background-color: #f6f8fa;
    color: #393a34;
}
pre > code {
    padding: 0;
    background-color: inherit;
    color: inherit;
}
h1, h2, h3 {
    font-weight: 600;
}

#logobar {
    background-color: #333333;
    margin: 0 auto;
    padding: 1em 4em;
}
#logobar .logo {
    float: left;
}
#logobar .title {
    font-weight: 600;
    color: #dddddd;
    float: left;
    margin: 5px 0 0 1em;
}
#logobar:after {
    content: "";
    display: block;
    clear: both;
}

#content {
    margin: 0 auto;
    padding: 0 4em;
}

#table_of_contents > h2 {
    font-size: 1.17em;
}
#table_of_contents ul:first-child {
    border: solid 1px #e1e4e8;
    border-radius: 6px;
    padding: 1em;
    background-color: #f6f8fa;
    color: #393a34;
}
#table_of_contents ul {
    list-style-type: none;
    padding-left: 1.5em;
}
#table_of_contents li {
    font-size: 0.9em;
}
#table_of_contents li a {
    color: #000000;
}

header.title {
    border-bottom: solid 1px #e1e4e8;
}
header.title > h1 {
    margin-bottom: 0.25em;
}
header.title > .description {
    display: block;
    margin-bottom: 0.5em;
    line-height: 1;
}

header.title .version {
    font-size: 0.8em;
    color: #666666;
}

footer#edit {
    border-top: solid 1px #e1e4e8;
    margin: 3em 0 4em 0;
    padding-top: 2em;
}
</style>
</head>
<body>
<div id="banner">
<div id="rainbar"></div>
<div id="logobar">
<svg class="logo" role="img" height="32" width="32" viewBox="0 0 700 700">
<polygon fill="#cb0000" points="0,700 700,700 700,0 0,0"></polygon>
<polygon fill="#ffffff" points="150,550 350,550 350,250 450,250 450,550 550,550 550,150 150,150"></polygon>
</svg>
<div class="title">
npm command-line interface
</div>
</div>
</div>

<section id="content">
<header class="title">
<h1 id="----registry----1082">
    <span>registry</span>
    <span class="version">@10.8.2</span>
</h1>
<span class="description">The JavaScript Package Registry</span>
</header>

<section id="table_of_contents">
<h2 id="table-of-contents">Table of contents</h2>
<div id="_table_of_contents"><ul><li><a href="#description">Description</a></li><li><a href="#does-npm-send-any-information-about-me-back-to-the-registry">Does npm send any information about me back to the registry?</a></li><li><a href="#how-can-i-prevent-my-package-from-being-published-in-the-official-registry">How can I prevent my package from being published in the official registry?</a></li><li><a href="#where-can-i-find-my-and-others-published-packages">Where can I find my (and others') published packages?</a></li><li><a href="#see-also">See also</a></li></ul></div>
</section>

<div id="_content"><h3 id="description">Description</h3>
<p>To resolve packages by name and version, npm talks to a registry website
that implements the CommonJS Package Registry specification for reading
package info.</p>
<p>npm is configured to use the <strong>npm public registry</strong> at
<a href="https://registry.npmjs.org">https://registry.npmjs.org</a> by default. Use of the npm public registry is
subject to terms of use available at <a href="https://docs.npmjs.com/policies/terms">https://docs.npmjs.com/policies/terms</a>.</p>
<p>You can configure npm to use any compatible registry you like, and even run
your own registry. Use of someone else's registry may be governed by their
terms of use.</p>
<p>npm's package registry implementation supports several
write APIs as well, to allow for publishing packages and managing user
account information.</p>
<p>The npm public registry is powered by a CouchDB database,
of which there is a public mirror at <a href="https://skimdb.npmjs.com/registry">https://skimdb.npmjs.com/registry</a>.</p>
<p>The registry URL used is determined by the scope of the package (see
<a href="../using-npm/scope.html"><code>scope</code></a>. If no scope is specified, the default registry is
used, which is supplied by the <a href="../using-npm/config#registry.html"><code>registry</code> config</a>
parameter.  See <a href="../commands/npm-config.html"><code>npm config</code></a>,
<a href="../configuring-npm/npmrc.html"><code>npmrc</code></a>, and <a href="../using-npm/config.html"><code>config</code></a> for more on
managing npm's configuration.
Authentication configuration such as auth tokens and certificates are configured
specifically scoped to an individual registry. See
<a href="../configuring-npm/npmrc#auth-related-configuration.html">Auth Related Configuration</a></p>
<p>When the default registry is used in a package-lock or shrinkwrap it has the
special meaning of "the currently configured registry". If you create a lock
file while using the default registry you can switch to another registry and
npm will install packages from the new registry, but if you create a lock
file while using a custom registry packages will be installed from that
registry even after you change to another registry.</p>
<h3 id="does-npm-send-any-information-about-me-back-to-the-registry">Does npm send any information about me back to the registry?</h3>
<p>Yes.</p>
<p>When making requests of the registry npm adds two headers with information
about your environment:</p>
<ul>
<li><code>Npm-Scope</code> – If your project is scoped, this header will contain its
scope. In the future npm hopes to build registry features that use this
information to allow you to customize your experience for your
organization.</li>
<li><code>Npm-In-CI</code> – Set to "true" if npm believes this install is running in a
continuous integration environment, "false" otherwise. This is detected by
looking for the following environment variables: <code>CI</code>, <code>TDDIUM</code>,
<code>JENKINS_URL</code>, <code>bamboo.buildKey</code>. If you'd like to learn more you may find
the <a href="https://github.com/npm/npm-registry-client/pull/129">original PR</a>
interesting.
This is used to gather better metrics on how npm is used by humans, versus
build farms.</li>
</ul>
<p>The npm registry does not try to correlate the information in these headers
with any authenticated accounts that may be used in the same requests.</p>
<h3 id="how-can-i-prevent-my-package-from-being-published-in-the-official-registry">How can I prevent my package from being published in the official registry?</h3>
<p>Set <code>"private": true</code> in your <code>package.json</code> to prevent it from being
published at all, or
<code>"publishConfig":{"registry":"http://my-internal-registry.local"}</code>
to force it to be published only to your internal/private registry.</p>
<p>See <a href="../configuring-npm/package-json.html"><code>package.json</code></a> for more info on what goes in the package.json file.</p>
<h3 id="where-can-i-find-my-and-others-published-packages">Where can I find my (and others') published packages?</h3>
<p><a href="https://www.npmjs.com/">https://www.npmjs.com/</a></p>
<h3 id="see-also">See also</h3>
<ul>
<li><a href="../commands/npm-config.html">npm config</a></li>
<li><a href="../using-npm/config.html">config</a></li>
<li><a href="../configuring-npm/npmrc.html">npmrc</a></li>
<li><a href="../using-npm/developers.html">npm developers</a></li>
</ul></div>

<footer id="edit">
<a href="https://github.com/npm/cli/edit/latest/docs/content/using-npm/registry.md">
<svg role="img" viewBox="0 0 16 16" width="16" height="16" fill="currentcolor" style="vertical-align: text-bottom; margin-right: 0.3em;">
<path fill-rule="evenodd" d="M11.013 1.427a1.75 1.75 0 012.474 0l1.086 1.086a1.75 1.75 0 010 2.474l-8.61 8.61c-.21.21-.47.364-.756.445l-3.251.93a.75.75 0 01-.927-.928l.929-3.25a1.75 1.75 0 01.445-.758l8.61-8.61zm1.414 1.06a.25.25 0 00-.354 0L10.811 3.75l1.439 1.44 1.263-1.263a.25.25 0 000-.354l-1.086-1.086zM11.189 6.25L9.75 4.81l-6.286 6.287a.25.25 0 00-.064.108l-.558 1.953 1.953-.558a.249.249 0 00.108-.064l6.286-6.286z"></path>
</svg>
Edit this page on GitHub
</a>
</footer>
</section>



</body></html>