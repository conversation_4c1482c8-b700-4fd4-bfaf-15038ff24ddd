{"name": "@npmcli/name-from-folder", "version": "2.0.0", "files": ["bin/", "lib/"], "main": "lib/index.js", "description": "Get the package name from a folder path", "repository": {"type": "git", "url": "https://github.com/npm/name-from-folder.git"}, "author": "GitHub Inc.", "license": "ISC", "scripts": {"test": "tap", "snap": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.1", "@npmcli/template-oss": "4.11.0", "tap": "^16.3.2"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.11.0"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}}