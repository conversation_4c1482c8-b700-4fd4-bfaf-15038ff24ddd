<script setup lang="ts">
import { ref, onMounted } from 'vue'
import TheWelcome from '../components/TheWelcome.vue'
import { gameAPI } from '@/services/api'
import type { GameStatus, GameInfo } from '@/types/game'

const gameStatus = ref<GameStatus | null>(null)
const gameInfo = ref<GameInfo | null>(null)
const loading = ref<boolean>(false)
const error = ref<string | null>(null)

const fetchGameStatus = async (): Promise<void> => {
  try {
    loading.value = true
    error.value = null
    const response = await gameAPI.getGameStatus()
    if (response.status === 'success' && response.data) {
      gameStatus.value = response.data
    } else {
      throw new Error(response.message || '获取游戏状态失败')
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : '未知错误'
    console.error('获取游戏状态失败:', err)
  } finally {
    loading.value = false
  }
}

const fetchGameInfo = async (): Promise<void> => {
  try {
    loading.value = true
    error.value = null
    const response = await gameAPI.getGameInfo()
    if (response.status === 'success' && response.data) {
      gameInfo.value = response.data
    } else {
      throw new Error(response.message || '获取游戏信息失败')
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : '未知错误'
    console.error('获取游戏信息失败:', err)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchGameStatus()
  fetchGameInfo()
})
</script>

<template>
  <main>
    <div style="margin-bottom: 20px; padding: 20px; border: 1px solid #ccc; border-radius: 8px;">
      <h2>SLG游戏 - 前后端通信测试</h2>

      <div v-if="loading" style="color: #666;">
        正在加载...
      </div>

      <div v-if="error" style="color: red; margin: 10px 0;">
        错误: {{ error }}
      </div>

      <div v-if="gameStatus" style="margin: 10px 0;">
        <h3>服务器状态:</h3>
        <p><strong>状态:</strong> {{ gameStatus.status }}</p>
        <p><strong>消息:</strong> {{ gameStatus.message }}</p>
        <p><strong>版本:</strong> {{ gameStatus.version }}</p>
        <p><strong>时间:</strong> {{ gameStatus.timestamp }}</p>
      </div>

      <div v-if="gameInfo" style="margin: 10px 0;">
        <h3>游戏信息:</h3>
        <p><strong>游戏名称:</strong> {{ gameInfo.game_name }}</p>
        <p><strong>服务器状态:</strong> {{ gameInfo.server_status }}</p>
        <p><strong>在线玩家:</strong> {{ gameInfo.players_online }}</p>
        <p><strong>服务器时间:</strong> {{ gameInfo.server_time }}</p>
      </div>

      <div style="margin-top: 15px;">
        <button @click="fetchGameStatus" :disabled="loading" style="margin-right: 10px;">
          刷新状态
        </button>
        <button @click="fetchGameInfo" :disabled="loading">
          刷新信息
        </button>
      </div>
    </div>

    <TheWelcome />
  </main>
</template>
