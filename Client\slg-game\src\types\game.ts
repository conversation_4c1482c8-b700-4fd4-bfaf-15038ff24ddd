// 玩家数据类型
export interface Player {
  id: number
  username: string
  email: string
  level: number
  experience: number
  gold: number
  resources: PlayerResources
  buildings: Building[]
  createdAt: string
  updatedAt: string
}

// 玩家资源类型
export interface PlayerResources {
  wood: number
  stone: number
  iron: number
  food: number
}

// 建筑类型
export interface Building {
  id: number
  userId: number
  buildingType: BuildingType
  level: number
  position: Position
  upgradeTime?: number
  isUpgrading?: boolean
  createdAt: string
  updatedAt: string
}

export type BuildingType = 'castle' | 'farm' | 'mine' | 'barracks' | 'wall' | 'warehouse' | 'academy'

// 坐标类型
export interface Position {
  x: number
  y: number
}

// API响应类型
export interface ApiResponse<T = any> {
  status: 'success' | 'error'
  message: string
  data?: T
  timestamp: string
  version?: string
}

// 游戏状态类型
export interface GameStatus {
  status: string
  message: string
  timestamp: string
  version: string
}

// 游戏信息类型
export interface GameInfo {
  gameName: string
  serverStatus: 'online' | 'offline' | 'maintenance'
  playersOnline: number
  serverTime: string
}

// 资源类型枚举
export enum ResourceType {
  WOOD = 'wood',
  STONE = 'stone', 
  IRON = 'iron',
  FOOD = 'food'
}

// 建筑升级需求
export interface BuildingUpgradeRequirement {
  buildingType: BuildingType
  currentLevel: number
  nextLevel: number
  resources: Partial<PlayerResources>
  time: number // 升级时间（秒）
}

// 用户认证相关
export interface LoginCredentials {
  username: string
  password: string
}

export interface RegisterData {
  username: string
  email: string
  password: string
  passwordConfirmation: string
}

export interface AuthUser {
  id: number
  username: string
  email: string
  emailVerifiedAt?: string
  createdAt: string
  updatedAt: string
}
