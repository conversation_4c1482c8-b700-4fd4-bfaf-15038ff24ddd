.TH "NPM-ACCESS" "1" "July 2024" "NPM@10.8.2" ""
.SH "NAME"
\fBnpm-access\fR - Set access level on published packages
.SS "Synopsis"
.P
.RS 2
.nf
npm access list packages \[lB]<user>|<scope>|<scope:team>\[rB] \[lB]<package>\[rB]
npm access list collaborators \[lB]<package> \[lB]<user>\[rB]\[rB]
npm access get status \[lB]<package>\[rB]
npm access set status=public|private \[lB]<package>\[rB]
npm access set mfa=none|publish|automation \[lB]<package>\[rB]
npm access grant <read-only|read-write> <scope:team> \[lB]<package>\[rB]
npm access revoke <scope:team> \[lB]<package>\[rB]
.fi
.RE
.P
Note: This command is unaware of workspaces.
.SS "Description"
.P
Used to set access controls on private packages.
.P
For all of the subcommands, \fBnpm access\fR will perform actions on the packages in the current working directory if no package name is passed to the subcommand.
.RS 0
.IP \(bu 4
public / restricted (deprecated): Set a package to be either publicly accessible or restricted.
.IP \(bu 4
grant / revoke (deprecated): Add or remove the ability of users and teams to have read-only or read-write access to a package.
.IP \(bu 4
2fa-required / 2fa-not-required (deprecated): Configure whether a package requires that anyone publishing it have two-factor authentication enabled on their account.
.IP \(bu 4
ls-packages (deprecated): Show all of the packages a user or a team is able to access, along with the access level, except for read-only public packages (it won't print the whole registry listing)
.IP \(bu 4
ls-collaborators (deprecated): Show all of the access privileges for a package. Will only show permissions for packages to which you have at least read access. If \fB<user>\fR is passed in, the list is filtered only to teams \fIthat\fR user happens to belong to.
.IP \(bu 4
edit (not implemented)
.RE 0

.SS "Details"
.P
\fBnpm access\fR always operates directly on the current registry, configurable from the command line using \fB--registry=<registry url>\fR.
.P
Unscoped packages are \fIalways public\fR.
.P
Scoped packages \fIdefault to restricted\fR, but you can either publish them as public using \fBnpm publish --access=public\fR, or set their access as public using \fBnpm access public\fR after the initial publish.
.P
You must have privileges to set the access of a package:
.RS 0
.IP \(bu 4
You are an owner of an unscoped or scoped package.
.IP \(bu 4
You are a member of the team that owns a scope.
.IP \(bu 4
You have been given read-write privileges for a package, either as a member of a team or directly as an owner.
.RE 0

.P
If you have two-factor authentication enabled then you'll be prompted to provide a second factor, or may use the \fB--otp=...\fR option to specify it on the command line.
.P
If your account is not paid, then attempts to publish scoped packages will fail with an HTTP 402 status code (logically enough), unless you use \fB--access=public\fR.
.P
Management of teams and team memberships is done with the \fBnpm team\fR command.
.SS "Configuration"
.SS "\fBjson\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Whether or not to output JSON data, rather than the normal output.
.RS 0
.IP \(bu 4
In \fBnpm pkg set\fR it enables parsing set values with JSON.parse() before saving them to your \fBpackage.json\fR.
.RE 0

.P
Not supported by all npm commands.
.SS "\fBotp\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null or String
.RE 0

.P
This is a one-time password from a two-factor authenticator. It's needed when publishing or changing package permissions with \fBnpm access\fR.
.P
If not set, and a registry response fails with a challenge for a one-time password, npm will prompt on the command line for one.
.SS "\fBregistry\fR"
.RS 0
.IP \(bu 4
Default: "https://registry.npmjs.org/"
.IP \(bu 4
Type: URL
.RE 0

.P
The base URL of the npm registry.
.SS "See Also"
.RS 0
.IP \(bu 4
\fB\fBlibnpmaccess\fR\fR \fI\(lahttps://npm.im/libnpmaccess\(ra\fR
.IP \(bu 4
npm help team
.IP \(bu 4
npm help publish
.IP \(bu 4
npm help config
.IP \(bu 4
npm help registry
.RE 0
