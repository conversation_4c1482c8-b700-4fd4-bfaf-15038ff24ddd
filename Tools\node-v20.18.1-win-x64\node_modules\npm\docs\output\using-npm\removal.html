<!DOCTYPE html><html><head>
<meta charset="utf-8">
<title>removal</title>
<style>
body {
    background-color: #ffffff;
    color: #24292e;

    margin: 0;

    line-height: 1.5;

    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
}
#rainbar {
    height: 10px;
    background-image: linear-gradient(139deg, #fb8817, #ff4b01, #c12127, #e02aff);
}

a {
    text-decoration: none;
    color: #0366d6;
}
a:hover {
    text-decoration: underline;
}

pre {
    margin: 1em 0px;
    padding: 1em;
    border: solid 1px #e1e4e8;
    border-radius: 6px;

    display: block;
    overflow: auto;

    white-space: pre;

    background-color: #f6f8fa;
    color: #393a34;
}
code {
    font-family: SFMono-Regular, <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, Courier, monospace;
    font-size: 85%;
    padding: 0.2em 0.4em;
    background-color: #f6f8fa;
    color: #393a34;
}
pre > code {
    padding: 0;
    background-color: inherit;
    color: inherit;
}
h1, h2, h3 {
    font-weight: 600;
}

#logobar {
    background-color: #333333;
    margin: 0 auto;
    padding: 1em 4em;
}
#logobar .logo {
    float: left;
}
#logobar .title {
    font-weight: 600;
    color: #dddddd;
    float: left;
    margin: 5px 0 0 1em;
}
#logobar:after {
    content: "";
    display: block;
    clear: both;
}

#content {
    margin: 0 auto;
    padding: 0 4em;
}

#table_of_contents > h2 {
    font-size: 1.17em;
}
#table_of_contents ul:first-child {
    border: solid 1px #e1e4e8;
    border-radius: 6px;
    padding: 1em;
    background-color: #f6f8fa;
    color: #393a34;
}
#table_of_contents ul {
    list-style-type: none;
    padding-left: 1.5em;
}
#table_of_contents li {
    font-size: 0.9em;
}
#table_of_contents li a {
    color: #000000;
}

header.title {
    border-bottom: solid 1px #e1e4e8;
}
header.title > h1 {
    margin-bottom: 0.25em;
}
header.title > .description {
    display: block;
    margin-bottom: 0.5em;
    line-height: 1;
}

header.title .version {
    font-size: 0.8em;
    color: #666666;
}

footer#edit {
    border-top: solid 1px #e1e4e8;
    margin: 3em 0 4em 0;
    padding-top: 2em;
}
</style>
</head>
<body>
<div id="banner">
<div id="rainbar"></div>
<div id="logobar">
<svg class="logo" role="img" height="32" width="32" viewBox="0 0 700 700">
<polygon fill="#cb0000" points="0,700 700,700 700,0 0,0"></polygon>
<polygon fill="#ffffff" points="150,550 350,550 350,250 450,250 450,550 550,550 550,150 150,150"></polygon>
</svg>
<div class="title">
npm command-line interface
</div>
</div>
</div>

<section id="content">
<header class="title">
<h1 id="----removal----1082">
    <span>removal</span>
    <span class="version">@10.8.2</span>
</h1>
<span class="description">Cleaning the Slate</span>
</header>

<section id="table_of_contents">
<h2 id="table-of-contents">Table of contents</h2>
<div id="_table_of_contents"><ul><li><a href="#synopsis">Synopsis</a></li><li><a href="#more-severe-uninstalling">More Severe Uninstalling</a></li><li><a href="#see-also">See also</a></li></ul></div>
</section>

<div id="_content"><h3 id="synopsis">Synopsis</h3>
<p>So sad to see you go.</p>
<pre><code class="language-bash">sudo npm uninstall npm -g
</code></pre>
<p>Or, if that fails, please proceed to more severe uninstalling methods.</p>
<h3 id="more-severe-uninstalling">More Severe Uninstalling</h3>
<p>Usually, the above instructions are sufficient.  That will remove
npm, but leave behind anything you've installed.</p>
<p>If that doesn't work, or if you require more drastic measures,
continue reading.</p>
<p>Note that this is only necessary for globally-installed packages.  Local
installs are completely contained within a project's <code>node_modules</code>
folder.  Delete that folder, and everything is gone unless a package's
install script is particularly ill-behaved.</p>
<p>This assumes that you installed node and npm in the default place.  If
you configured node with a different <code>--prefix</code>, or installed npm with a
different prefix setting, then adjust the paths accordingly, replacing
<code>/usr/local</code> with your install prefix.</p>
<p>To remove everything npm-related manually:</p>
<pre><code class="language-bash">rm -rf /usr/local/{lib/node{,/.npm,_modules},bin,share/man}/npm*
</code></pre>
<p>If you installed things <em>with</em> npm, then your best bet is to uninstall
them with npm first, and then install them again once you have a
proper install.  This can help find any symlinks that are lying
around:</p>
<pre><code class="language-bash">ls -laF /usr/local/{lib/node{,/.npm},bin,share/man} | grep npm
</code></pre>
<p>Prior to version 0.3, npm used shim files for executables and node
modules.  To track those down, you can do the following:</p>
<pre><code class="language-bash">find /usr/local/{lib/node,bin} -exec grep -l npm \{\} \; ;
</code></pre>
<h3 id="see-also">See also</h3>
<ul>
<li><a href="../commands/npm-uninstall.html">npm uninstall</a></li>
<li><a href="../commands/npm-prune.html">npm prune</a></li>
</ul></div>

<footer id="edit">
<a href="https://github.com/npm/cli/edit/latest/docs/content/using-npm/removal.md">
<svg role="img" viewBox="0 0 16 16" width="16" height="16" fill="currentcolor" style="vertical-align: text-bottom; margin-right: 0.3em;">
<path fill-rule="evenodd" d="M11.013 1.427a1.75 1.75 0 012.474 0l1.086 1.086a1.75 1.75 0 010 2.474l-8.61 8.61c-.21.21-.47.364-.756.445l-3.251.93a.75.75 0 01-.927-.928l.929-3.25a1.75 1.75 0 01.445-.758l8.61-8.61zm1.414 1.06a.25.25 0 00-.354 0L10.811 3.75l1.439 1.44 1.263-1.263a.25.25 0 000-.354l-1.086-1.086zM11.189 6.25L9.75 4.81l-6.286 6.287a.25.25 0 00-.064.108l-.558 1.953 1.953-.558a.249.249 0 00.108-.064l6.286-6.286z"></path>
</svg>
Edit this page on GitHub
</a>
</footer>
</section>



</body></html>