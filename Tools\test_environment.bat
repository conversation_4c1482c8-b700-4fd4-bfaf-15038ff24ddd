@echo off
chcp 65001 >nul
echo ========================================
echo Testing SLG Game Development Environment
echo ========================================

echo.
echo 1. Testing PHP version...
"%~dp0php-binary\php.exe" --version
if %errorlevel% neq 0 (
    echo [ERROR] PHP test failed
    goto :error
)

echo.
echo 2. Testing Composer version...
"%~dp0php-binary\php.exe" "%~dp0composer.phar" --version
if %errorlevel% neq 0 (
    echo [ERROR] Composer test failed
    goto :error
)

echo.
echo 3. Testing Node.js version...
"%~dp0node-v20.18.1-win-x64\node.exe" --version
if %errorlevel% neq 0 (
    echo [ERROR] Node.js test failed
    goto :error
)

echo.
echo 4. Testing npm version...
"%~dp0node-v20.18.1-win-x64\npm.cmd" --version
if %errorlevel% neq 0 (
    echo [ERROR] npm test failed
    goto :error
)

echo.
echo 5. Checking MySQL directory...
if not exist "%~dp0mysql-8.4.5\bin\mysqld.exe" (
    echo [ERROR] MySQL executable not found
    goto :error
)
echo MySQL executable exists

echo.
echo 6. Checking Redis directory...
if not exist "%~dp0Redis-7.2.10\redis-server.exe" (
    echo [ERROR] Redis executable not found
    goto :error
)
echo Redis executable exists

echo.
echo 7. Testing MySQL connection...
echo Testing MySQL startup...
start /wait /min "" "%~dp0mysql-8.4.5\bin\mysqld.exe" --console --initialize-insecure --user=mysql --datadir="%~dp0mysql-8.4.5\data" 2>nul
timeout /t 2 /nobreak >nul

echo.
echo ========================================
echo All environment tests passed!
echo ========================================
echo.
echo Development tools locations:
echo - PHP: %~dp0php-binary\php.exe
echo - Composer: %~dp0composer.phar
echo - Node.js: %~dp0node-v20.18.1-win-x64\node.exe
echo - MySQL: %~dp0mysql-8.4.5\bin\mysqld.exe
echo - Redis: %~dp0Redis-7.2.10\redis-server.exe
echo.
goto :end

:error
echo.
echo ========================================
echo Environment test failed! Please check configuration.
echo ========================================

:end
pause
