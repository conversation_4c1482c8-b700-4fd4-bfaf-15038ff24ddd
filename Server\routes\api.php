<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// SLG游戏API路由
Route::get('/game/status', function () {
    return response()->json([
        'status' => 'success',
        'message' => 'SLG游戏服务器运行正常',
        'timestamp' => now(),
        'version' => '1.0.0'
    ]);
});

Route::get('/game/info', function () {
    return response()->json([
        'game_name' => '文字类SLG游戏',
        'server_status' => 'online',
        'players_online' => 0,
        'server_time' => now()->toDateTimeString()
    ]);
});
