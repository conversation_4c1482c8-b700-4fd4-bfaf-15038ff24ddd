<!DOCTYPE html><html><head>
<meta charset="utf-8">
<title>folders</title>
<style>
body {
    background-color: #ffffff;
    color: #24292e;

    margin: 0;

    line-height: 1.5;

    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
}
#rainbar {
    height: 10px;
    background-image: linear-gradient(139deg, #fb8817, #ff4b01, #c12127, #e02aff);
}

a {
    text-decoration: none;
    color: #0366d6;
}
a:hover {
    text-decoration: underline;
}

pre {
    margin: 1em 0px;
    padding: 1em;
    border: solid 1px #e1e4e8;
    border-radius: 6px;

    display: block;
    overflow: auto;

    white-space: pre;

    background-color: #f6f8fa;
    color: #393a34;
}
code {
    font-family: SFMono-Regular, <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, Courier, monospace;
    font-size: 85%;
    padding: 0.2em 0.4em;
    background-color: #f6f8fa;
    color: #393a34;
}
pre > code {
    padding: 0;
    background-color: inherit;
    color: inherit;
}
h1, h2, h3 {
    font-weight: 600;
}

#logobar {
    background-color: #333333;
    margin: 0 auto;
    padding: 1em 4em;
}
#logobar .logo {
    float: left;
}
#logobar .title {
    font-weight: 600;
    color: #dddddd;
    float: left;
    margin: 5px 0 0 1em;
}
#logobar:after {
    content: "";
    display: block;
    clear: both;
}

#content {
    margin: 0 auto;
    padding: 0 4em;
}

#table_of_contents > h2 {
    font-size: 1.17em;
}
#table_of_contents ul:first-child {
    border: solid 1px #e1e4e8;
    border-radius: 6px;
    padding: 1em;
    background-color: #f6f8fa;
    color: #393a34;
}
#table_of_contents ul {
    list-style-type: none;
    padding-left: 1.5em;
}
#table_of_contents li {
    font-size: 0.9em;
}
#table_of_contents li a {
    color: #000000;
}

header.title {
    border-bottom: solid 1px #e1e4e8;
}
header.title > h1 {
    margin-bottom: 0.25em;
}
header.title > .description {
    display: block;
    margin-bottom: 0.5em;
    line-height: 1;
}

header.title .version {
    font-size: 0.8em;
    color: #666666;
}

footer#edit {
    border-top: solid 1px #e1e4e8;
    margin: 3em 0 4em 0;
    padding-top: 2em;
}
</style>
</head>
<body>
<div id="banner">
<div id="rainbar"></div>
<div id="logobar">
<svg class="logo" role="img" height="32" width="32" viewBox="0 0 700 700">
<polygon fill="#cb0000" points="0,700 700,700 700,0 0,0"></polygon>
<polygon fill="#ffffff" points="150,550 350,550 350,250 450,250 450,550 550,550 550,150 150,150"></polygon>
</svg>
<div class="title">
npm command-line interface
</div>
</div>
</div>

<section id="content">
<header class="title">
<h1 id="----folders----1082">
    <span>folders</span>
    <span class="version">@10.8.2</span>
</h1>
<span class="description">Folder Structures Used by npm</span>
</header>

<section id="table_of_contents">
<h2 id="table-of-contents">Table of contents</h2>
<div id="_table_of_contents"><ul><li><a href="#description">Description</a></li><ul><li><a href="#tldr">tl;dr</a></li><li><a href="#prefix-configuration">prefix Configuration</a></li><li><a href="#node-modules">Node Modules</a></li><li><a href="#executables">Executables</a></li><li><a href="#man-pages">Man Pages</a></li><li><a href="#cache">Cache</a></li><li><a href="#temp-files">Temp Files</a></li></ul><li><a href="#more-information">More Information</a></li><ul><li><a href="#global-installation">Global Installation</a></li><li><a href="#cycles-conflicts-and-folder-parsimony">Cycles, Conflicts, and Folder Parsimony</a></li><li><a href="#example">Example</a></li><li><a href="#publishing">Publishing</a></li></ul><li><a href="#see-also">See also</a></li></ul></div>
</section>

<div id="_content"><h3 id="description">Description</h3>
<p>npm puts various things on your computer.  That's its job.</p>
<p>This document will tell you what it puts where.</p>
<h4 id="tldr">tl;dr</h4>
<ul>
<li>Local install (default): puts stuff in <code>./node_modules</code> of the current
package root.</li>
<li>Global install (with <code>-g</code>): puts stuff in /usr/local or wherever node
is installed.</li>
<li>Install it <strong>locally</strong> if you're going to <code>require()</code> it.</li>
<li>Install it <strong>globally</strong> if you're going to run it on the command line.</li>
<li>If you need both, then install it in both places, or use <code>npm link</code>.</li>
</ul>
<h4 id="prefix-configuration">prefix Configuration</h4>
<p>The <a href="../using-npm/config#prefix.html"><code>prefix</code> config</a> defaults to the location where
node is installed. On most systems, this is <code>/usr/local</code>. On Windows, it's
<code>%AppData%\npm</code>. On Unix systems, it's one level up, since node is typically
installed at <code>{prefix}/bin/node</code> rather than <code>{prefix}/node.exe</code>.</p>
<p>When the <code>global</code> flag is set, npm installs things into this prefix.
When it is not set, it uses the root of the current package, or the
current working directory if not in a package already.</p>
<h4 id="node-modules">Node Modules</h4>
<p>Packages are dropped into the <code>node_modules</code> folder under the <code>prefix</code>.
When installing locally, this means that you can
<code>require("packagename")</code> to load its main module, or
<code>require("packagename/lib/path/to/sub/module")</code> to load other modules.</p>
<p>Global installs on Unix systems go to <code>{prefix}/lib/node_modules</code>.
Global installs on Windows go to <code>{prefix}/node_modules</code> (that is, no
<code>lib</code> folder.)</p>
<p>Scoped packages are installed the same way, except they are grouped together
in a sub-folder of the relevant <code>node_modules</code> folder with the name of that
scope prefix by the @ symbol, e.g. <code>npm install @myorg/package</code> would place
the package in <code>{prefix}/node_modules/@myorg/package</code>. See
<a href="../using-npm/scope.html"><code>scope</code></a> for more details.</p>
<p>If you wish to <code>require()</code> a package, then install it locally.</p>
<h4 id="executables">Executables</h4>
<p>When in global mode, executables are linked into <code>{prefix}/bin</code> on Unix,
or directly into <code>{prefix}</code> on Windows.  Ensure that path is in your
terminal's <code>PATH</code> environment to run them.</p>
<p>When in local mode, executables are linked into
<code>./node_modules/.bin</code> so that they can be made available to scripts run
through npm.  (For example, so that a test runner will be in the path
when you run <code>npm test</code>.)</p>
<h4 id="man-pages">Man Pages</h4>
<p>When in global mode, man pages are linked into <code>{prefix}/share/man</code>.</p>
<p>When in local mode, man pages are not installed.</p>
<p>Man pages are not installed on Windows systems.</p>
<h4 id="cache">Cache</h4>
<p>See <a href="../commands/npm-cache.html"><code>npm cache</code></a>.  Cache files are stored in <code>~/.npm</code> on Posix, or
<code>%LocalAppData%/npm-cache</code> on Windows.</p>
<p>This is controlled by the <a href="../using-npm/config#cache.html"><code>cache</code> config</a> param.</p>
<h4 id="temp-files">Temp Files</h4>
<p>Temporary files are stored by default in the folder specified by the
<a href="../using-npm/config#tmp.html"><code>tmp</code> config</a>, which defaults to the TMPDIR, TMP, or
TEMP environment variables, or <code>/tmp</code> on Unix and <code>c:\windows\temp</code> on Windows.</p>
<p>Temp files are given a unique folder under this root for each run of the
program, and are deleted upon successful exit.</p>
<h3 id="more-information">More Information</h3>
<p>When installing locally, npm first tries to find an appropriate
<code>prefix</code> folder.  This is so that <code>npm install foo@1.2.3</code> will install
to the sensible root of your package, even if you happen to have <code>cd</code>ed
into some other folder.</p>
<p>Starting at the $PWD, npm will walk up the folder tree checking for a
folder that contains either a <code>package.json</code> file, or a <code>node_modules</code>
folder.  If such a thing is found, then that is treated as the effective
"current directory" for the purpose of running npm commands.  (This
behavior is inspired by and similar to git's .git-folder seeking
logic when running git commands in a working dir.)</p>
<p>If no package root is found, then the current folder is used.</p>
<p>When you run <code>npm install foo@1.2.3</code>, then the package is loaded into
the cache, and then unpacked into <code>./node_modules/foo</code>.  Then, any of
foo's dependencies are similarly unpacked into
<code>./node_modules/foo/node_modules/...</code>.</p>
<p>Any bin files are symlinked to <code>./node_modules/.bin/</code>, so that they may
be found by npm scripts when necessary.</p>
<h4 id="global-installation">Global Installation</h4>
<p>If the <a href="../using-npm/config#global.html"><code>global</code> config</a> is set to true, then npm will
install packages "globally".</p>
<p>For global installation, packages are installed roughly the same way,
but using the folders described above.</p>
<h4 id="cycles-conflicts-and-folder-parsimony">Cycles, Conflicts, and Folder Parsimony</h4>
<p>Cycles are handled using the property of node's module system that it
walks up the directories looking for <code>node_modules</code> folders.  So, at every
stage, if a package is already installed in an ancestor <code>node_modules</code>
folder, then it is not installed at the current location.</p>
<p>Consider the case above, where <code>foo -&gt; bar -&gt; baz</code>.  Imagine if, in
addition to that, baz depended on bar, so you'd have:
<code>foo -&gt; bar -&gt; baz -&gt; bar -&gt; baz ...</code>.  However, since the folder
structure is: <code>foo/node_modules/bar/node_modules/baz</code>, there's no need to
put another copy of bar into <code>.../baz/node_modules</code>, since when baz calls
<code>require("bar")</code>, it will get the copy that is installed in
<code>foo/node_modules/bar</code>.</p>
<p>This shortcut is only used if the exact same
version would be installed in multiple nested <code>node_modules</code> folders.  It
is still possible to have <code>a/node_modules/b/node_modules/a</code> if the two
"a" packages are different versions.  However, without repeating the
exact same package multiple times, an infinite regress will always be
prevented.</p>
<p>Another optimization can be made by installing dependencies at the
highest level possible, below the localized "target" folder (hoisting).
Since version 3, npm hoists dependencies by default.</p>
<h4 id="example">Example</h4>
<p>Consider this dependency graph:</p>
<pre><code class="language-bash">foo
+-- blerg@1.2.5
+-- bar@1.2.3
|   +-- blerg@1.x (latest=1.3.7)
|   +-- baz@2.x
|   |   `-- quux@3.x
|   |       `-- bar@1.2.3 (cycle)
|   `-- asdf@*
`-- baz@1.2.3
    `-- quux@3.x
        `-- bar
</code></pre>
<p>In this case, we might expect a folder structure like this
(with all dependencies hoisted to the highest level possible):</p>
<pre><code class="language-bash">foo
+-- node_modules
    +-- blerg (1.2.5) &lt;---[A]
    +-- bar (1.2.3) &lt;---[B]
    |   +-- node_modules
    |       +-- baz (2.0.2) &lt;---[C]
    +-- asdf (2.3.4)
    +-- baz (1.2.3) &lt;---[D]
    +-- quux (3.2.0) &lt;---[E]
</code></pre>
<p>Since foo depends directly on <code>bar@1.2.3</code> and <code>baz@1.2.3</code>, those are
installed in foo's <code>node_modules</code> folder.</p>
<p>Even though the latest copy of blerg is 1.3.7, foo has a specific
dependency on version 1.2.5.  So, that gets installed at [A].  Since the
parent installation of blerg satisfies bar's dependency on <code>blerg@1.x</code>,
it does not install another copy under [B].</p>
<p>Bar [B] also has dependencies on baz and asdf.  Because it depends on <code>baz@2.x</code>, it cannot
re-use the <code>baz@1.2.3</code> installed in the parent <code>node_modules</code> folder [D],
and must install its own copy [C]. In order to minimize duplication, npm hoists
dependencies to the top level by default, so asdf is installed under [A].</p>
<p>Underneath bar, the <code>baz -&gt; quux -&gt; bar</code> dependency creates a cycle.
However, because bar is already in quux's ancestry [B], it does not
unpack another copy of bar into that folder. Likewise, quux's [E]
folder tree is empty, because its dependency on bar is satisfied
by the parent folder copy installed at [B].</p>
<p>For a graphical breakdown of what is installed where, use <code>npm ls</code>.</p>
<h4 id="publishing">Publishing</h4>
<p>Upon publishing, npm will look in the <code>node_modules</code> folder.  If any of
the items there are not in the <code>bundleDependencies</code> array, then they will
not be included in the package tarball.</p>
<p>This allows a package maintainer to install all of their dependencies
(and dev dependencies) locally, but only re-publish those items that
cannot be found elsewhere.  See <a href="../configuring-npm/package-json.html"><code>package.json</code></a> for more information.</p>
<h3 id="see-also">See also</h3>
<ul>
<li><a href="../configuring-npm/package-json.html">package.json</a></li>
<li><a href="../commands/npm-install.html">npm install</a></li>
<li><a href="../commands/npm-pack.html">npm pack</a></li>
<li><a href="../commands/npm-cache.html">npm cache</a></li>
<li><a href="../commands/npm-config.html">npm config</a></li>
<li><a href="../configuring-npm/npmrc.html">npmrc</a></li>
<li><a href="../using-npm/config.html">config</a></li>
<li><a href="../commands/npm-publish.html">npm publish</a></li>
</ul></div>

<footer id="edit">
<a href="https://github.com/npm/cli/edit/latest/docs/content/configuring-npm/folders.md">
<svg role="img" viewBox="0 0 16 16" width="16" height="16" fill="currentcolor" style="vertical-align: text-bottom; margin-right: 0.3em;">
<path fill-rule="evenodd" d="M11.013 1.427a1.75 1.75 0 012.474 0l1.086 1.086a1.75 1.75 0 010 2.474l-8.61 8.61c-.21.21-.47.364-.756.445l-3.251.93a.75.75 0 01-.927-.928l.929-3.25a1.75 1.75 0 01.445-.758l8.61-8.61zm1.414 1.06a.25.25 0 00-.354 0L10.811 3.75l1.439 1.44 1.263-1.263a.25.25 0 000-.354l-1.086-1.086zM11.189 6.25L9.75 4.81l-6.286 6.287a.25.25 0 00-.064.108l-.558 1.953 1.953-.558a.249.249 0 00.108-.064l6.286-6.286z"></path>
</svg>
Edit this page on GitHub
</a>
</footer>
</section>



</body></html>