.TH "NPM-QUERY" "1" "July 2024" "NPM@10.8.2" ""
.SH "NAME"
\fBnpm-query\fR - Dependency selector query
.SS "Synopsis"
.P
.RS 2
.nf
npm query <selector>
.fi
.RE
.SS "Description"
.P
The \fBnpm query\fR command allows for usage of css selectors in order to retrieve an array of dependency objects.
.SS "Piping npm query to other commands"
.P
.RS 2
.nf
# find all dependencies with postinstall scripts & uninstall them
npm query ":attr(scripts, \[lB]postinstall\[rB])" | jq 'map(.name)|join("\[rs]n")' -r | xargs -I {} npm uninstall {}

# find all git dependencies & explain who requires them
npm query ":type(git)" | jq 'map(.name)' | xargs -I {} npm why {}
.fi
.RE
.SS "Extended Use Cases & Queries"
.P
.RS 2
.nf
// all deps
*

// all direct deps
:root > *

// direct production deps
:root > .prod

// direct development deps
:root > .dev

// any peer dep of a direct deps
:root > * > .peer

// any workspace dep
.workspace

// all workspaces that depend on another workspace
.workspace > .workspace

// all workspaces that have peer deps
.workspace:has(.peer)

// any dep named "lodash"
// equivalent to \[lB]name="lodash"\[rB]
#lodash

// any deps named "lodash" & within semver range ^"1.2.3"
#lodash@^1.2.3
// equivalent to...
\[lB]name="lodash"\[rB]:semver(^1.2.3)

// get the hoisted node for a given semver range
#lodash@^1.2.3:not(:deduped)

// querying deps with a specific version
#lodash@2.1.5
// equivalent to...
\[lB]name="lodash"\[rB]\[lB]version="2.1.5"\[rB]

// has any deps
:has(*)

// deps with no other deps (ie. "leaf" nodes)
:empty

// manually querying git dependencies
\[lB]repository^=github:\[rB],
\[lB]repository^=git:\[rB],
\[lB]repository^=https://github.com\[rB],
\[lB]repository^=http://github.com\[rB],
\[lB]repository^=https://github.com\[rB],
\[lB]repository^=+git:...\[rB]

// querying for all git dependencies
:type(git)

// get production dependencies that aren't also dev deps
.prod:not(.dev)

// get dependencies with specific licenses
\[lB]license=MIT\[rB], \[lB]license=ISC\[rB]

// find all packages that have @ruyadorno as a contributor
:attr(contributors, \[lB]email=<EMAIL>\[rB])
.fi
.RE
.SS "Example Response Output"
.RS 0
.IP \(bu 4
an array of dependency objects is returned which can contain multiple copies of the same package which may or may not have been linked or deduped
.RE 0

.P
.RS 2
.nf
\[lB]
  {
    "name": "",
    "version": "",
    "description": "",
    "homepage": "",
    "bugs": {},
    "author": {},
    "license": {},
    "funding": {},
    "files": \[lB]\[rB],
    "main": "",
    "browser": "",
    "bin": {},
    "man": \[lB]\[rB],
    "directories": {},
    "repository": {},
    "scripts": {},
    "config": {},
    "dependencies": {},
    "devDependencies": {},
    "optionalDependencies": {},
    "bundledDependencies": {},
    "peerDependencies": {},
    "peerDependenciesMeta": {},
    "engines": {},
    "os": \[lB]\[rB],
    "cpu": \[lB]\[rB],
    "workspaces": {},
    "keywords": \[lB]\[rB],
    ...
  },
  ...
.fi
.RE
.SS "Expecting a certain number of results"
.P
One common use of \fBnpm query\fR is to make sure there is only one version of a certain dependency in your tree. This is especially common for ecosystems like that rely on \fBtypescript\fR where having state split across two different but identically-named packages causes bugs. You can use the \fB--expect-results\fR or \fB--expect-result-count\fR in your setup to ensure that npm will exit with an exit code if your tree doesn't look like you want it to.
.P
.RS 2
.nf
$ npm query '#react' --expect-result-count=1
.fi
.RE
.P
Perhaps you want to quickly check if there are any production dependencies that could be updated:
.P
.RS 2
.nf
$ npm query ':root>:outdated(in-range).prod' --no-expect-results
.fi
.RE
.SS "Package lock only mode"
.P
If package-lock-only is enabled, only the information in the package lock (or shrinkwrap) is loaded. This means that information from the package.json files of your dependencies will not be included in the result set (e.g. description, homepage, engines).
.SS "Configuration"
.SS "\fBglobal\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Operates in "global" mode, so that packages are installed into the \fBprefix\fR folder instead of the current working directory. See npm help folders for more on the differences in behavior.
.RS 0
.IP \(bu 4
packages are installed into the \fB{prefix}/lib/node_modules\fR folder, instead of the current working directory.
.IP \(bu 4
bin files are linked to \fB{prefix}/bin\fR
.IP \(bu 4
man pages are linked to \fB{prefix}/share/man\fR
.RE 0

.SS "\fBworkspace\fR"
.RS 0
.IP \(bu 4
Default:
.IP \(bu 4
Type: String (can be set multiple times)
.RE 0

.P
Enable running a command in the context of the configured workspaces of the current project while filtering by running only the workspaces defined by this configuration option.
.P
Valid values for the \fBworkspace\fR config are either:
.RS 0
.IP \(bu 4
Workspace names
.IP \(bu 4
Path to a workspace directory
.IP \(bu 4
Path to a parent workspace directory (will result in selecting all workspaces within that folder)
.RE 0

.P
When set for the \fBnpm init\fR command, this may be set to the folder of a workspace which does not yet exist, to create the folder and set it up as a brand new workspace within the project.
.P
This value is not exported to the environment for child processes.
.SS "\fBworkspaces\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null or Boolean
.RE 0

.P
Set to true to run the command in the context of \fBall\fR configured workspaces.
.P
Explicitly setting this to false will cause commands like \fBinstall\fR to ignore workspaces altogether. When not set explicitly:
.RS 0
.IP \(bu 4
Commands that operate on the \fBnode_modules\fR tree (install, update, etc.) will link workspaces into the \fBnode_modules\fR folder. - Commands that do other things (test, exec, publish, etc.) will operate on the root project, \fIunless\fR one or more workspaces are specified in the \fBworkspace\fR config.
.RE 0

.P
This value is not exported to the environment for child processes.
.SS "\fBinclude-workspace-root\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Include the workspace root when workspaces are enabled for a command.
.P
When false, specifying individual workspaces via the \fBworkspace\fR config, or all workspaces via the \fBworkspaces\fR flag, will cause npm to operate only on the specified workspaces, and not on the root project.
.P
This value is not exported to the environment for child processes.
.SS "\fBpackage-lock-only\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
If set to true, the current operation will only use the \fBpackage-lock.json\fR, ignoring \fBnode_modules\fR.
.P
For \fBupdate\fR this means only the \fBpackage-lock.json\fR will be updated, instead of checking \fBnode_modules\fR and downloading dependencies.
.P
For \fBlist\fR this means the output will be based on the tree described by the \fBpackage-lock.json\fR, rather than the contents of \fBnode_modules\fR.
.SS "\fBexpect-results\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null or Boolean
.RE 0

.P
Tells npm whether or not to expect results from the command. Can be either true (expect some results) or false (expect no results).
.P
This config can not be used with: \fBexpect-result-count\fR
.SS "\fBexpect-result-count\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null or Number
.RE 0

.P
Tells to expect a specific number of results from the command.
.P
This config can not be used with: \fBexpect-results\fR
.SH "SEE ALSO"
.RS 0
.IP \(bu 4
npm help "dependency selectors"
.RE 0
