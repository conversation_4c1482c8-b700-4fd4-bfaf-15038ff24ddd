.TH "NPM-DIST-TAG" "1" "July 2024" "NPM@10.8.2" ""
.SH "NAME"
\fBnpm-dist-tag\fR - Modify package distribution tags
.SS "Synopsis"
.P
.RS 2
.nf
npm dist-tag add <package-spec (with version)> \[lB]<tag>\[rB]
npm dist-tag rm <package-spec> <tag>
npm dist-tag ls \[lB]<package-spec>\[rB]

alias: dist-tags
.fi
.RE
.SS "Description"
.P
Add, remove, and enumerate distribution tags on a package:
.RS 0
.IP \(bu 4
add: Tags the specified version of the package with the specified tag, or the \fB\fB--tag\fR config\fR \fI\(la/using-npm/config#tag\(ra\fR if not specified. If you have two-factor authentication on auth-and-writes then you\[cq]ll need to include a one-time password on the command line with \fB--otp <one-time password>\fR, or go through a second factor flow based on your \fBauthtype\fR.
.IP \(bu 4
rm: Clear a tag that is no longer in use from the package. If you have two-factor authentication on auth-and-writes then you\[cq]ll need to include a one-time password on the command line with \fB--otp <one-time password>\fR, or go through a second factor flow based on your \fBauthtype\fR
.IP \(bu 4
ls: Show all of the dist-tags for a package, defaulting to the package in the current prefix. This is the default action if none is specified.
.RE 0

.P
A tag can be used when installing packages as a reference to a version instead of using a specific version number:
.P
.RS 2
.nf
npm install <name>@<tag>
.fi
.RE
.P
When installing dependencies, a preferred tagged version may be specified:
.P
.RS 2
.nf
npm install --tag <tag>
.fi
.RE
.P
(This also applies to any other commands that resolve and install dependencies, such as \fBnpm dedupe\fR, \fBnpm update\fR, and \fBnpm audit fix\fR.)
.P
Publishing a package sets the \fBlatest\fR tag to the published version unless the \fB--tag\fR option is used. For example, \fBnpm publish --tag=beta\fR.
.P
By default, \fBnpm install <pkg>\fR (without any \fB@<version>\fR or \fB@<tag>\fR specifier) installs the \fBlatest\fR tag.
.SS "Purpose"
.P
Tags can be used to provide an alias instead of version numbers.
.P
For example, a project might choose to have multiple streams of development and use a different tag for each stream, e.g., \fBstable\fR, \fBbeta\fR, \fBdev\fR, \fBcanary\fR.
.P
By default, the \fBlatest\fR tag is used by npm to identify the current version of a package, and \fBnpm install <pkg>\fR (without any \fB@<version>\fR or \fB@<tag>\fR specifier) installs the \fBlatest\fR tag. Typically, projects only use the \fBlatest\fR tag for stable release versions, and use other tags for unstable versions such as prereleases.
.P
The \fBnext\fR tag is used by some projects to identify the upcoming version.
.P
Other than \fBlatest\fR, no tag has any special significance to npm itself.
.SS "Caveats"
.P
This command used to be known as \fBnpm tag\fR, which only created new tags, and so had a different syntax.
.P
Tags must share a namespace with version numbers, because they are specified in the same slot: \fBnpm install <pkg>@<version>\fR vs \fBnpm install <pkg>@<tag>\fR.
.P
Tags that can be interpreted as valid semver ranges will be rejected. For example, \fBv1.4\fR cannot be used as a tag, because it is interpreted by semver as \fB>=1.4.0 <1.5.0\fR. See \fI\(lahttps://github.com/npm/npm/issues/6082\(ra\fR.
.P
The simplest way to avoid semver problems with tags is to use tags that do not begin with a number or the letter \fBv\fR.
.SS "Configuration"
.SS "\fBworkspace\fR"
.RS 0
.IP \(bu 4
Default:
.IP \(bu 4
Type: String (can be set multiple times)
.RE 0

.P
Enable running a command in the context of the configured workspaces of the current project while filtering by running only the workspaces defined by this configuration option.
.P
Valid values for the \fBworkspace\fR config are either:
.RS 0
.IP \(bu 4
Workspace names
.IP \(bu 4
Path to a workspace directory
.IP \(bu 4
Path to a parent workspace directory (will result in selecting all workspaces within that folder)
.RE 0

.P
When set for the \fBnpm init\fR command, this may be set to the folder of a workspace which does not yet exist, to create the folder and set it up as a brand new workspace within the project.
.P
This value is not exported to the environment for child processes.
.SS "\fBworkspaces\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null or Boolean
.RE 0

.P
Set to true to run the command in the context of \fBall\fR configured workspaces.
.P
Explicitly setting this to false will cause commands like \fBinstall\fR to ignore workspaces altogether. When not set explicitly:
.RS 0
.IP \(bu 4
Commands that operate on the \fBnode_modules\fR tree (install, update, etc.) will link workspaces into the \fBnode_modules\fR folder. - Commands that do other things (test, exec, publish, etc.) will operate on the root project, \fIunless\fR one or more workspaces are specified in the \fBworkspace\fR config.
.RE 0

.P
This value is not exported to the environment for child processes.
.SS "\fBinclude-workspace-root\fR"
.RS 0
.IP \(bu 4
Default: false
.IP \(bu 4
Type: Boolean
.RE 0

.P
Include the workspace root when workspaces are enabled for a command.
.P
When false, specifying individual workspaces via the \fBworkspace\fR config, or all workspaces via the \fBworkspaces\fR flag, will cause npm to operate only on the specified workspaces, and not on the root project.
.P
This value is not exported to the environment for child processes.
.SS "See Also"
.RS 0
.IP \(bu 4
npm help "package spec"
.IP \(bu 4
npm help publish
.IP \(bu 4
npm help install
.IP \(bu 4
npm help dedupe
.IP \(bu 4
npm help registry
.IP \(bu 4
npm help config
.IP \(bu 4
npm help npmrc
.RE 0
