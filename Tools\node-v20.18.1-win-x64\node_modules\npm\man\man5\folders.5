.TH "FOLDERS" "5" "July 2024" "NPM@10.8.2" ""
.SH "NAME"
\fBfolders\fR - Folder Structures Used by npm
.SS "Description"
.P
npm puts various things on your computer. That's its job.
.P
This document will tell you what it puts where.
.SS "tl;dr"
.RS 0
.IP \(bu 4
Local install (default): puts stuff in \fB./node_modules\fR of the current package root.
.IP \(bu 4
Global install (with \fB-g\fR): puts stuff in /usr/local or wherever node is installed.
.IP \(bu 4
Install it \fBlocally\fR if you're going to \fBrequire()\fR it.
.IP \(bu 4
Install it \fBglobally\fR if you're going to run it on the command line.
.IP \(bu 4
If you need both, then install it in both places, or use \fBnpm link\fR.
.RE 0

.SS "prefix Configuration"
.P
The \fB\fBprefix\fR config\fR \fI\(la/using-npm/config#prefix\(ra\fR defaults to the location where node is installed. On most systems, this is \fB/usr/local\fR. On Windows, it's \fB%AppData%\[rs]npm\fR. On Unix systems, it's one level up, since node is typically installed at \fB{prefix}/bin/node\fR rather than \fB{prefix}/node.exe\fR.
.P
When the \fBglobal\fR flag is set, npm installs things into this prefix. When it is not set, it uses the root of the current package, or the current working directory if not in a package already.
.SS "Node Modules"
.P
Packages are dropped into the \fBnode_modules\fR folder under the \fBprefix\fR. When installing locally, this means that you can \fBrequire("packagename")\fR to load its main module, or \fBrequire("packagename/lib/path/to/sub/module")\fR to load other modules.
.P
Global installs on Unix systems go to \fB{prefix}/lib/node_modules\fR. Global installs on Windows go to \fB{prefix}/node_modules\fR (that is, no \fBlib\fR folder.)
.P
Scoped packages are installed the same way, except they are grouped together in a sub-folder of the relevant \fBnode_modules\fR folder with the name of that scope prefix by the @ symbol, e.g. \fBnpm install @myorg/package\fR would place the package in \fB{prefix}/node_modules/@myorg/package\fR. See npm help scope for more details.
.P
If you wish to \fBrequire()\fR a package, then install it locally.
.SS "Executables"
.P
When in global mode, executables are linked into \fB{prefix}/bin\fR on Unix, or directly into \fB{prefix}\fR on Windows. Ensure that path is in your terminal's \fBPATH\fR environment to run them.
.P
When in local mode, executables are linked into \fB./node_modules/.bin\fR so that they can be made available to scripts run through npm. (For example, so that a test runner will be in the path when you run \fBnpm test\fR.)
.SS "Man Pages"
.P
When in global mode, man pages are linked into \fB{prefix}/share/man\fR.
.P
When in local mode, man pages are not installed.
.P
Man pages are not installed on Windows systems.
.SS "Cache"
.P
See npm help cache. Cache files are stored in \fB~/.npm\fR on Posix, or \fB%LocalAppData%/npm-cache\fR on Windows.
.P
This is controlled by the \fB\fBcache\fR config\fR \fI\(la/using-npm/config#cache\(ra\fR param.
.SS "Temp Files"
.P
Temporary files are stored by default in the folder specified by the \fB\fBtmp\fR config\fR \fI\(la/using-npm/config#tmp\(ra\fR, which defaults to the TMPDIR, TMP, or TEMP environment variables, or \fB/tmp\fR on Unix and \fBc:\[rs]windows\[rs]temp\fR on Windows.
.P
Temp files are given a unique folder under this root for each run of the program, and are deleted upon successful exit.
.SS "More Information"
.P
When installing locally, npm first tries to find an appropriate \fBprefix\fR folder. This is so that \fBnpm install foo@1.2.3\fR will install to the sensible root of your package, even if you happen to have \fBcd\fRed into some other folder.
.P
Starting at the $PWD, npm will walk up the folder tree checking for a folder that contains either a \fBpackage.json\fR file, or a \fBnode_modules\fR folder. If such a thing is found, then that is treated as the effective "current directory" for the purpose of running npm commands. (This behavior is inspired by and similar to git's .git-folder seeking logic when running git commands in a working dir.)
.P
If no package root is found, then the current folder is used.
.P
When you run \fBnpm install foo@1.2.3\fR, then the package is loaded into the cache, and then unpacked into \fB./node_modules/foo\fR. Then, any of foo's dependencies are similarly unpacked into \fB./node_modules/foo/node_modules/...\fR.
.P
Any bin files are symlinked to \fB./node_modules/.bin/\fR, so that they may be found by npm scripts when necessary.
.SS "Global Installation"
.P
If the \fB\fBglobal\fR config\fR \fI\(la/using-npm/config#global\(ra\fR is set to true, then npm will install packages "globally".
.P
For global installation, packages are installed roughly the same way, but using the folders described above.
.SS "Cycles, Conflicts, and Folder Parsimony"
.P
Cycles are handled using the property of node's module system that it walks up the directories looking for \fBnode_modules\fR folders. So, at every stage, if a package is already installed in an ancestor \fBnode_modules\fR folder, then it is not installed at the current location.
.P
Consider the case above, where \fBfoo -> bar -> baz\fR. Imagine if, in addition to that, baz depended on bar, so you'd have: \fBfoo -> bar -> baz -> bar -> baz ...\fR. However, since the folder structure is: \fBfoo/node_modules/bar/node_modules/baz\fR, there's no need to put another copy of bar into \fB.../baz/node_modules\fR, since when baz calls \fBrequire("bar")\fR, it will get the copy that is installed in \fBfoo/node_modules/bar\fR.
.P
This shortcut is only used if the exact same version would be installed in multiple nested \fBnode_modules\fR folders. It is still possible to have \fBa/node_modules/b/node_modules/a\fR if the two "a" packages are different versions. However, without repeating the exact same package multiple times, an infinite regress will always be prevented.
.P
Another optimization can be made by installing dependencies at the highest level possible, below the localized "target" folder (hoisting). Since version 3, npm hoists dependencies by default.
.SS "Example"
.P
Consider this dependency graph:
.P
.RS 2
.nf
foo
+-- blerg@1.2.5
+-- bar@1.2.3
|   +-- blerg@1.x (latest=1.3.7)
|   +-- baz@2.x
|   |   `-- quux@3.x
|   |       `-- bar@1.2.3 (cycle)
|   `-- asdf@*
`-- baz@1.2.3
    `-- quux@3.x
        `-- bar
.fi
.RE
.P
In this case, we might expect a folder structure like this (with all dependencies hoisted to the highest level possible):
.P
.RS 2
.nf
foo
+-- node_modules
    +-- blerg (1.2.5) <---\[lB]A\[rB]
    +-- bar (1.2.3) <---\[lB]B\[rB]
    |   +-- node_modules
    |       +-- baz (2.0.2) <---\[lB]C\[rB]
    +-- asdf (2.3.4)
    +-- baz (1.2.3) <---\[lB]D\[rB]
    +-- quux (3.2.0) <---\[lB]E\[rB]
.fi
.RE
.P
Since foo depends directly on \fBbar@1.2.3\fR and \fBbaz@1.2.3\fR, those are installed in foo's \fBnode_modules\fR folder.
.P
Even though the latest copy of blerg is 1.3.7, foo has a specific dependency on version 1.2.5. So, that gets installed at \[lB]A\[rB]. Since the parent installation of blerg satisfies bar's dependency on \fBblerg@1.x\fR, it does not install another copy under \[lB]B\[rB].
.P
Bar \[lB]B\[rB] also has dependencies on baz and asdf. Because it depends on \fBbaz@2.x\fR, it cannot re-use the \fBbaz@1.2.3\fR installed in the parent \fBnode_modules\fR folder \[lB]D\[rB], and must install its own copy \[lB]C\[rB]. In order to minimize duplication, npm hoists dependencies to the top level by default, so asdf is installed under \[lB]A\[rB].
.P
Underneath bar, the \fBbaz -> quux -> bar\fR dependency creates a cycle. However, because bar is already in quux's ancestry \[lB]B\[rB], it does not unpack another copy of bar into that folder. Likewise, quux's \[lB]E\[rB] folder tree is empty, because its dependency on bar is satisfied by the parent folder copy installed at \[lB]B\[rB].
.P
For a graphical breakdown of what is installed where, use \fBnpm ls\fR.
.SS "Publishing"
.P
Upon publishing, npm will look in the \fBnode_modules\fR folder. If any of the items there are not in the \fBbundleDependencies\fR array, then they will not be included in the package tarball.
.P
This allows a package maintainer to install all of their dependencies (and dev dependencies) locally, but only re-publish those items that cannot be found elsewhere. See \fB\fBpackage.json\fR\fR \fI\(la/configuring-npm/package-json\(ra\fR for more information.
.SS "See also"
.RS 0
.IP \(bu 4
\fBpackage.json\fR \fI\(la/configuring-npm/package-json\(ra\fR
.IP \(bu 4
npm help install
.IP \(bu 4
npm help pack
.IP \(bu 4
npm help cache
.IP \(bu 4
npm help config
.IP \(bu 4
npm help npmrc
.IP \(bu 4
npm help config
.IP \(bu 4
npm help publish
.RE 0
