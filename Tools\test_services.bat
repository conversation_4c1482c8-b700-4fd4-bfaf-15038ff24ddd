@echo off
chcp 65001 >nul
echo ========================================
echo Testing SLG Game Services
echo ========================================

echo.
echo 1. Starting MySQL Server...
start "MySQL Server" /D "%~dp0mysql-8.4.5" bin\mysqld.exe --defaults-file=my.ini --console
timeout /t 5 /nobreak >nul

echo.
echo 2. Starting Redis Server...
start "Redis Server" /D "%~dp0Redis-7.2.10" redis-server.exe
timeout /t 3 /nobreak >nul

echo.
echo 3. Testing MySQL connection...
echo Testing if MySQL is listening on port 3306...
netstat -an | findstr :3306
if %errorlevel% equ 0 (
    echo MySQL is running on port 3306
) else (
    echo MySQL may not be running on port 3306
)

echo.
echo 4. Testing Redis connection...
echo Testing if Red<PERSON> is listening on port 6379...
netstat -an | findstr :6379
if %errorlevel% equ 0 (
    echo Redis is running on port 6379
) else (
    echo Redis may not be running on port 6379
)

echo.
echo 5. Starting Laravel Backend...
start "Laravel Backend" /D "%~dp0..\Server" "%~dp0php-binary\php.exe" artisan serve
timeout /t 5 /nobreak >nul

echo.
echo 6. Testing Laravel Backend...
echo Testing if Laravel is listening on port 8000...
netstat -an | findstr :8000
if %errorlevel% equ 0 (
    echo Laravel is running on port 8000
) else (
    echo Laravel may not be running on port 8000
)

echo.
echo 7. Starting phpMyAdmin...
start "phpMyAdmin" cmd /c "cd /d %~dp0phpMyAdmin && %~dp0php-binary\php.exe -S 127.0.0.1:8080 -t ."
timeout /t 3 /nobreak >nul

echo.
echo 8. Testing phpMyAdmin...
echo Testing if phpMyAdmin is listening on port 8080...
netstat -an | findstr :8080
if %errorlevel% equ 0 (
    echo phpMyAdmin is running on port 8080
) else (
    echo phpMyAdmin may not be running on port 8080
)

echo.
echo ========================================
echo Service Test Summary
echo ========================================
echo.
echo Service URLs:
echo - Laravel Backend: http://127.0.0.1:8000
echo - phpMyAdmin: http://127.0.0.1:8080
echo.
echo Database Info:
echo - MySQL Host: 127.0.0.1:3306
echo - Redis Host: 127.0.0.1:6379
echo - Username: root
echo - Password: (empty)
echo.
echo Press any key to continue...
pause >nul
