# SLG游戏开发环境使用说明

## � 技术栈

- **服务端**: PHP 8.3.23 + Laravel 10.48.29 (LTS版本)
- **前端**: Vue 3 + TypeScript 5.8.3
- **数据库**: MySQL 8.4.5 + Redis 7.2.10
- **开发工具**: phpMyAdmin 数据库管理工具

## �🚀 快速启动

### 启动所有服务
```
双击运行: Tools/start_all_services.bat
```

### 停止所有服务
```
双击运行: Tools/stop_all_services.bat
```

### 单独启动phpMyAdmin
```
双击运行: Tools/start_phpmyadmin.bat
```

### 测试开发环境
```
双击运行: Tools/complete_test.bat
```

## 🌐 服务访问地址

| 服务 | 地址 | 说明 |
|------|------|------|
| **Laravel后端** | http://127.0.0.1:8000 | API服务器 |
| **Vue前端** | http://localhost:5173 | 游戏前端界面 |
| **phpMyAdmin** | http://127.0.0.1:8080 | 数据库管理工具 |
| **API测试** | http://127.0.0.1:8000/api/game/status | 测试API是否正常 |

## 🔑 数据库连接信息

### MySQL数据库
- **主机地址**: 127.0.0.1
- **端口**: 3306
- **用户名**: root
- **密码**: (留空，无密码)
- **数据库名**: slg_game

### Redis缓存
- **主机地址**: 127.0.0.1
- **端口**: 6379
- **密码**: (无密码)

## 📁 工具路径

| 工具 | 完整路径 |
|------|----------|
| **PHP** | `Tools/php-binary/php.exe` |
| **Composer** | `Tools/composer.phar` |
| **Node.js** | `Tools/node-v20.18.1-win-x64/node.exe` |
| **npm** | `Tools/node-v20.18.1-win-x64/npm.cmd` |
| **MySQL** | `Tools/mysql-8.4.5/bin/mysqld.exe` |
| **Redis** | `Tools/Redis-7.2.10/redis-server.exe` |
| **phpMyAdmin** | `Tools/phpMyAdmin/` |

## 🔧 开发命令

### Laravel后端开发
```bash
# 进入Server目录
cd Server

# 启动开发服务器
d:\work\JY\zongheng\Tools\php-binary\php.exe artisan serve

# 运行数据库迁移
d:\work\JY\zongheng\Tools\php-binary\php.exe artisan migrate

# 清除缓存
d:\work\JY\zongheng\Tools\php-binary\php.exe artisan cache:clear
```

### Vue + TypeScript 前端开发
```bash
# 进入Client/slg-game目录
cd Client/slg-game

# 设置Node.js路径并启动开发服务器
set PATH=d:\work\JY\zongheng\Tools\node-v20.18.1-win-x64;%PATH%
npm run dev

# 构建生产版本
npm run build

# TypeScript类型检查
npm run type-check

# 代码格式化
npm run format

# 代码检查
npm run lint
```

## 🗄️ 数据库管理

### 使用phpMyAdmin
1. 确保MySQL服务已启动
2. 访问: http://127.0.0.1:8080
3. 用户名: `root`，密码留空
4. 点击登录即可管理数据库

### 使用命令行
```bash
# 连接MySQL
Tools/mysql-8.4.5/bin/mysql.exe -u root

# 连接Redis
Tools/Redis-7.2.10/redis-cli.exe
```

## ⚠️ 注意事项

1. **端口占用**: 确保端口 3306、6379、8000、5173、8080 未被其他程序占用
2. **防火墙**: 如果无法访问，请检查Windows防火墙设置
3. **路径问题**: 所有路径使用英文，避免中文路径导致的问题
4. **服务启动顺序**: 建议先启动MySQL和Redis，再启动Laravel和Vue
5. **开发环境**: 此配置仅用于开发，不适用于生产环境

## 🐛 常见问题

### 1. MySQL无法启动
- 检查端口3306是否被占用
- 查看 `Tools/mysql-8.4.5/data/` 目录是否存在

### 2. Laravel报错
- 检查 `Server/.env` 文件配置
- 确保MySQL服务已启动
- 运行 `php artisan key:generate`

### 3. Vue前端无法访问
- 检查Node.js是否正确安装
- 确保在 `Client/slg-game` 目录下运行命令
- 检查端口5173是否被占用

### 4. phpMyAdmin无法登录
- 确保MySQL服务已启动
- 检查用户名是否为 `root`，密码留空
- 清除浏览器缓存重试

### 5. TypeScript编译错误
- 检查 `Client/slg-game/tsconfig.json` 配置
- 运行 `npm run type-check` 检查类型错误
- 确保所有依赖已正确安装

## 🔧 环境修复记录

**已修复的问题 (2025-01-10):**
1. ✅ 删除了不必要的PHP源码目录 `Tools/php-8.3.23`
2. ✅ 修复了phpMyAdmin配置（blowfish_secret和无密码登录）
3. ✅ 确认MySQL数据库已正确初始化
4. ✅ 验证TypeScript 5.8.3正常工作
5. ✅ 创建了完整的环境测试脚本

## 📞 技术支持

如遇到问题，请检查：
1. 所有服务是否正常启动
2. 端口是否被占用
3. 配置文件是否正确
4. 路径是否包含中文字符
5. 运行 `Tools/complete_test.bat` 进行完整环境测试
