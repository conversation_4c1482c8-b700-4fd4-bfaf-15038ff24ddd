.TH "NPM-HOOK" "1" "July 2024" "NPM@10.8.2" ""
.SH "NAME"
\fBnpm-hook\fR - Manage registry hooks
.SS "Synopsis"
.P
.RS 2
.nf
npm hook add <pkg> <url> <secret> \[lB]--type=<type>\[rB]
npm hook ls \[lB]pkg\[rB]
npm hook rm <id>
npm hook update <id> <url> <secret>
.fi
.RE
.P
Note: This command is unaware of workspaces.
.SS "Description"
.P
Allows you to manage \fBnpm hooks\fR \fI\(lahttps://blog.npmjs.org/post/145260155635/introducing-hooks-get-notifications-of-npm\(ra\fR, including adding, removing, listing, and updating.
.P
Hooks allow you to configure URL endpoints that will be notified whenever a change happens to any of the supported entity types. Three different types of entities can be watched by hooks: packages, owners, and scopes.
.P
To create a package hook, simply reference the package name.
.P
To create an owner hook, prefix the owner name with \fB~\fR (as in, \fB~youruser\fR).
.P
To create a scope hook, prefix the scope name with \fB@\fR (as in, \fB@yourscope\fR).
.P
The hook \fBid\fR used by \fBupdate\fR and \fBrm\fR are the IDs listed in \fBnpm hook ls\fR for that particular hook.
.P
The shared secret will be sent along to the URL endpoint so you can verify the request came from your own configured hook.
.SS "Example"
.P
Add a hook to watch a package for changes:
.P
.RS 2
.nf
$ npm hook add lodash https://example.com/ my-shared-secret
.fi
.RE
.P
Add a hook to watch packages belonging to the user \fBsubstack\fR:
.P
.RS 2
.nf
$ npm hook add ~substack https://example.com/ my-shared-secret
.fi
.RE
.P
Add a hook to watch packages in the scope \fB@npm\fR
.P
.RS 2
.nf
$ npm hook add @npm https://example.com/ my-shared-secret
.fi
.RE
.P
List all your active hooks:
.P
.RS 2
.nf
$ npm hook ls
.fi
.RE
.P
List your active hooks for the \fBlodash\fR package:
.P
.RS 2
.nf
$ npm hook ls lodash
.fi
.RE
.P
Update an existing hook's url:
.P
.RS 2
.nf
$ npm hook update id-deadbeef https://my-new-website.here/
.fi
.RE
.P
Remove a hook:
.P
.RS 2
.nf
$ npm hook rm id-deadbeef
.fi
.RE
.SS "Configuration"
.SS "\fBregistry\fR"
.RS 0
.IP \(bu 4
Default: "https://registry.npmjs.org/"
.IP \(bu 4
Type: URL
.RE 0

.P
The base URL of the npm registry.
.SS "\fBotp\fR"
.RS 0
.IP \(bu 4
Default: null
.IP \(bu 4
Type: null or String
.RE 0

.P
This is a one-time password from a two-factor authenticator. It's needed when publishing or changing package permissions with \fBnpm access\fR.
.P
If not set, and a registry response fails with a challenge for a one-time password, npm will prompt on the command line for one.
.SS "See Also"
.RS 0
.IP \(bu 4
\fB"Introducing Hooks" blog post\fR \fI\(lahttps://blog.npmjs.org/post/145260155635/introducing-hooks-get-notifications-of-npm\(ra\fR
.RE 0
