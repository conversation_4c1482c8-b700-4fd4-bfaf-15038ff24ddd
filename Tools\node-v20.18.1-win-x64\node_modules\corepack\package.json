{"name": "corepack", "version": "0.29.4", "homepage": "https://github.com/nodejs/corepack#readme", "bugs": {"url": "https://github.com/nodejs/corepack/issues"}, "repository": {"type": "git", "url": "https://github.com/nodejs/corepack.git"}, "engines": {"node": "^18.17.1 || >=20.10.0"}, "exports": {"./package.json": "./package.json"}, "license": "MIT", "packageManager": "yarn@4.3.1+sha224.934d21773e22af4b69a7032a2d3b4cb38c1f7c019624777cc9916b23", "devDependencies": {"@types/debug": "^4.1.5", "@types/node": "^20.4.6", "@types/proxy-from-env": "^1", "@types/semver": "^7.1.0", "@types/which": "^3.0.0", "@yarnpkg/eslint-config": "^2.0.0", "@yarnpkg/fslib": "^3.0.0-rc.48", "@zkochan/cmd-shim": "^6.0.0", "better-sqlite3": "^10.0.0", "clipanion": "patch:clipanion@npm%3A3.2.1#~/.yarn/patches/clipanion-npm-3.2.1-fc9187f56c.patch", "debug": "^4.1.1", "esbuild": "^0.21.0", "eslint": "^8.57.0", "proxy-from-env": "^1.1.0", "semver": "^7.6.3", "supports-color": "^9.0.0", "tar": "^7.4.0", "tsx": "^4.16.2", "typescript": "^5.3.3", "undici": "^6.19.2", "v8-compile-cache": "^2.3.0", "vitest": "^2.0.3", "which": "^4.0.0"}, "resolutions": {"undici-types": "6.x"}, "scripts": {"build": "run clean && run build:bundle && tsx ./mkshims.ts", "build:bundle": "esbuild ./sources/_lib.ts --bundle --platform=node --target=node18.17.0 --external:corepack --outfile='./dist/lib/corepack.cjs' --resolve-extensions='.ts,.mjs,.js'", "clean": "run rimraf dist shims", "corepack": "tsx ./sources/_cli.ts", "lint": "eslint .", "prepack": "yarn build", "postpack": "run clean", "rimraf": "node -e 'for(let i=2;i<process.argv.length;i++)fs.rmSync(process.argv[i],{recursive:true,force:true});'", "typecheck": "tsc --noEmit", "test": "vitest"}, "files": ["dist", "shims", "LICENSE.md"], "publishConfig": {"bin": {"corepack": "./dist/corepack.js", "pnpm": "./dist/pnpm.js", "pnpx": "./dist/pnpx.js", "yarn": "./dist/yarn.js", "yarnpkg": "./dist/yarnpkg.js"}, "executableFiles": ["./dist/npm.js", "./dist/npx.js", "./dist/pnpm.js", "./dist/pnpx.js", "./dist/yarn.js", "./dist/yarnpkg.js", "./dist/corepack.js", "./shims/npm", "./shims/npm.ps1", "./shims/npx", "./shims/npx.ps1", "./shims/pnpm", "./shims/pnpm.ps1", "./shims/pnpx", "./shims/pnpx.ps1", "./shims/yarn", "./shims/yarn.ps1", "./shims/yarnpkg", "./shims/yarnpkg.ps1"]}, "bin": {"corepack": "./dist/corepack.js", "pnpm": "./dist/pnpm.js", "pnpx": "./dist/pnpx.js", "yarn": "./dist/yarn.js", "yarnpkg": "./dist/yarnpkg.js"}}