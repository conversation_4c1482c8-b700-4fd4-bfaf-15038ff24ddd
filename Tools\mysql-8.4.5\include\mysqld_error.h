/* Copyright (c) 2000, 2025, Oracle and/or its affiliates.

   This program is free software; you can redistribute it and/or modify
   it under the terms of the GNU General Public License, version 2.0,
   as published by the Free Software Foundation.

   This program is designed to work with certain software (including
   but not limited to OpenSSL) that is licensed under separate terms,
   as designated in a particular file or component or in included license
   documentation.  The authors of MySQL hereby grant you an additional
   permission to link the program and your derivative works with the
   separately licensed software that they have either included with
   the program or referenced in the documentation.

   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License, version 2.0, for more details.

   You should have received a copy of the GNU General Public License
   along with this program; if not, write to the Free Software
   Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA */
/* Autogenerated file, please don't edit */

#ifndef MYSQLD_ERROR_INCLUDED
#define MYSQLD_ERROR_INCLUDED

static const int errmsg_section_start[] = { 1000, 3000, 3500, 6000, 10000, 15000 };
static const int errmsg_section_size[] = { 888, 239, 667, 141, 4083, 147 };

static const int total_error_count = 6165;

//#define OBSOLETE_ER_HASHCHK 1000
//#define OBSOLETE_ER_NISAMCHK 1001
#define ER_NO 1002
#define ER_YES 1003
#define ER_CANT_CREATE_FILE 1004
#define ER_CANT_CREATE_TABLE 1005
#define ER_CANT_CREATE_DB 1006
#define ER_DB_CREATE_EXISTS 1007
#define ER_DB_DROP_EXISTS 1008
//#define OBSOLETE_ER_DB_DROP_DELETE 1009
#define ER_DB_DROP_RMDIR 1010
//#define OBSOLETE_ER_CANT_DELETE_FILE 1011
#define ER_CANT_FIND_SYSTEM_REC 1012
#define ER_CANT_GET_STAT 1013
//#define OBSOLETE_ER_CANT_GET_WD 1014
#define ER_CANT_LOCK 1015
#define ER_CANT_OPEN_FILE 1016
#define ER_FILE_NOT_FOUND 1017
#define ER_CANT_READ_DIR 1018
//#define OBSOLETE_ER_CANT_SET_WD 1019
#define ER_CHECKREAD 1020
//#define OBSOLETE_ER_DISK_FULL 1021
#define ER_DUP_KEY 1022
//#define OBSOLETE_ER_ERROR_ON_CLOSE 1023
#define ER_ERROR_ON_READ 1024
#define ER_ERROR_ON_RENAME 1025
#define ER_ERROR_ON_WRITE 1026
#define ER_FILE_USED 1027
//#define OBSOLETE_ER_FILSORT_ABORT 1028
//#define OBSOLETE_ER_FORM_NOT_FOUND 1029
#define ER_GET_ERRNO 1030
#define ER_ILLEGAL_HA 1031
#define ER_KEY_NOT_FOUND 1032
#define ER_NOT_FORM_FILE 1033
#define ER_NOT_KEYFILE 1034
#define ER_OLD_KEYFILE 1035
#define ER_OPEN_AS_READONLY 1036
#define ER_OUTOFMEMORY 1037
#define ER_OUT_OF_SORTMEMORY 1038
//#define OBSOLETE_ER_UNEXPECTED_EOF 1039
#define ER_CON_COUNT_ERROR 1040
#define ER_OUT_OF_RESOURCES 1041
#define ER_BAD_HOST_ERROR 1042
#define ER_HANDSHAKE_ERROR 1043
#define ER_DBACCESS_DENIED_ERROR 1044
#define ER_ACCESS_DENIED_ERROR 1045
#define ER_NO_DB_ERROR 1046
#define ER_UNKNOWN_COM_ERROR 1047
#define ER_BAD_NULL_ERROR 1048
#define ER_BAD_DB_ERROR 1049
#define ER_TABLE_EXISTS_ERROR 1050
#define ER_BAD_TABLE_ERROR 1051
#define ER_NON_UNIQ_ERROR 1052
#define ER_SERVER_SHUTDOWN 1053
#define ER_BAD_FIELD_ERROR 1054
#define ER_WRONG_FIELD_WITH_GROUP 1055
#define ER_WRONG_GROUP_FIELD 1056
#define ER_WRONG_SUM_SELECT 1057
#define ER_WRONG_VALUE_COUNT 1058
#define ER_TOO_LONG_IDENT 1059
#define ER_DUP_FIELDNAME 1060
#define ER_DUP_KEYNAME 1061
#define ER_DUP_ENTRY 1062
#define ER_WRONG_FIELD_SPEC 1063
#define ER_PARSE_ERROR 1064
#define ER_EMPTY_QUERY 1065
#define ER_NONUNIQ_TABLE 1066
#define ER_INVALID_DEFAULT 1067
#define ER_MULTIPLE_PRI_KEY 1068
#define ER_TOO_MANY_KEYS 1069
#define ER_TOO_MANY_KEY_PARTS 1070
#define ER_TOO_LONG_KEY 1071
#define ER_KEY_COLUMN_DOES_NOT_EXITS 1072
#define ER_BLOB_USED_AS_KEY 1073
#define ER_TOO_BIG_FIELDLENGTH 1074
#define ER_WRONG_AUTO_KEY 1075
#define ER_READY 1076
//#define OBSOLETE_ER_NORMAL_SHUTDOWN 1077
//#define OBSOLETE_ER_GOT_SIGNAL 1078
#define ER_SHUTDOWN_COMPLETE 1079
#define ER_FORCING_CLOSE 1080
#define ER_IPSOCK_ERROR 1081
#define ER_NO_SUCH_INDEX 1082
#define ER_WRONG_FIELD_TERMINATORS 1083
#define ER_BLOBS_AND_NO_TERMINATED 1084
#define ER_TEXTFILE_NOT_READABLE 1085
#define ER_FILE_EXISTS_ERROR 1086
#define ER_LOAD_INFO 1087
#define ER_ALTER_INFO 1088
#define ER_WRONG_SUB_KEY 1089
#define ER_CANT_REMOVE_ALL_FIELDS 1090
#define ER_CANT_DROP_FIELD_OR_KEY 1091
#define ER_INSERT_INFO 1092
#define ER_UPDATE_TABLE_USED 1093
#define ER_NO_SUCH_THREAD 1094
#define ER_KILL_DENIED_ERROR 1095
#define ER_NO_TABLES_USED 1096
#define ER_TOO_BIG_SET 1097
#define ER_NO_UNIQUE_LOGFILE 1098
#define ER_TABLE_NOT_LOCKED_FOR_WRITE 1099
#define ER_TABLE_NOT_LOCKED 1100
#define ER_BLOB_CANT_HAVE_DEFAULT 1101
#define ER_WRONG_DB_NAME 1102
#define ER_WRONG_TABLE_NAME 1103
#define ER_TOO_BIG_SELECT 1104
#define ER_UNKNOWN_ERROR 1105
#define ER_UNKNOWN_PROCEDURE 1106
#define ER_WRONG_PARAMCOUNT_TO_PROCEDURE 1107
#define ER_WRONG_PARAMETERS_TO_PROCEDURE 1108
#define ER_UNKNOWN_TABLE 1109
#define ER_FIELD_SPECIFIED_TWICE 1110
#define ER_INVALID_GROUP_FUNC_USE 1111
#define ER_UNSUPPORTED_EXTENSION 1112
#define ER_TABLE_MUST_HAVE_COLUMNS 1113
#define ER_RECORD_FILE_FULL 1114
#define ER_UNKNOWN_CHARACTER_SET 1115
#define ER_TOO_MANY_TABLES 1116
#define ER_TOO_MANY_FIELDS 1117
#define ER_TOO_BIG_ROWSIZE 1118
#define ER_STACK_OVERRUN 1119
#define ER_WRONG_OUTER_JOIN_UNUSED 1120
#define ER_NULL_COLUMN_IN_INDEX 1121
#define ER_CANT_FIND_UDF 1122
#define ER_CANT_INITIALIZE_UDF 1123
#define ER_UDF_NO_PATHS 1124
#define ER_UDF_EXISTS 1125
#define ER_CANT_OPEN_LIBRARY 1126
#define ER_CANT_FIND_DL_ENTRY 1127
#define ER_FUNCTION_NOT_DEFINED 1128
#define ER_HOST_IS_BLOCKED 1129
#define ER_HOST_NOT_PRIVILEGED 1130
#define ER_PASSWORD_ANONYMOUS_USER 1131
#define ER_PASSWORD_NOT_ALLOWED 1132
#define ER_PASSWORD_NO_MATCH 1133
#define ER_UPDATE_INFO 1134
#define ER_CANT_CREATE_THREAD 1135
#define ER_WRONG_VALUE_COUNT_ON_ROW 1136
#define ER_CANT_REOPEN_TABLE 1137
#define ER_INVALID_USE_OF_NULL 1138
#define ER_REGEXP_ERROR 1139
#define ER_MIX_OF_GROUP_FUNC_AND_FIELDS 1140
#define ER_NONEXISTING_GRANT 1141
#define ER_TABLEACCESS_DENIED_ERROR 1142
#define ER_COLUMNACCESS_DENIED_ERROR 1143
#define ER_ILLEGAL_GRANT_FOR_TABLE 1144
#define ER_GRANT_WRONG_HOST_OR_USER 1145
#define ER_NO_SUCH_TABLE 1146
#define ER_NONEXISTING_TABLE_GRANT 1147
#define ER_NOT_ALLOWED_COMMAND 1148
#define ER_SYNTAX_ERROR 1149
//#define OBSOLETE_ER_UNUSED1 1150
//#define OBSOLETE_ER_UNUSED2 1151
#define ER_ABORTING_CONNECTION 1152
#define ER_NET_PACKET_TOO_LARGE 1153
#define ER_NET_READ_ERROR_FROM_PIPE 1154
#define ER_NET_FCNTL_ERROR 1155
#define ER_NET_PACKETS_OUT_OF_ORDER 1156
#define ER_NET_UNCOMPRESS_ERROR 1157
#define ER_NET_READ_ERROR 1158
#define ER_NET_READ_INTERRUPTED 1159
#define ER_NET_ERROR_ON_WRITE 1160
#define ER_NET_WRITE_INTERRUPTED 1161
#define ER_TOO_LONG_STRING 1162
#define ER_TABLE_CANT_HANDLE_BLOB 1163
#define ER_TABLE_CANT_HANDLE_AUTO_INCREMENT 1164
//#define OBSOLETE_ER_UNUSED3 1165
#define ER_WRONG_COLUMN_NAME 1166
#define ER_WRONG_KEY_COLUMN 1167
#define ER_WRONG_MRG_TABLE 1168
#define ER_DUP_UNIQUE 1169
#define ER_BLOB_KEY_WITHOUT_LENGTH 1170
#define ER_PRIMARY_CANT_HAVE_NULL 1171
#define ER_TOO_MANY_ROWS 1172
#define ER_REQUIRES_PRIMARY_KEY 1173
//#define OBSOLETE_ER_NO_RAID_COMPILED 1174
#define ER_UPDATE_WITHOUT_KEY_IN_SAFE_MODE 1175
#define ER_KEY_DOES_NOT_EXITS 1176
#define ER_CHECK_NO_SUCH_TABLE 1177
#define ER_CHECK_NOT_IMPLEMENTED 1178
#define ER_CANT_DO_THIS_DURING_AN_TRANSACTION 1179
#define ER_ERROR_DURING_COMMIT 1180
#define ER_ERROR_DURING_ROLLBACK 1181
#define ER_ERROR_DURING_FLUSH_LOGS 1182
//#define OBSOLETE_ER_ERROR_DURING_CHECKPOINT 1183
#define ER_NEW_ABORTING_CONNECTION 1184
//#define OBSOLETE_ER_DUMP_NOT_IMPLEMENTED 1185
//#define OBSOLETE_ER_FLUSH_MASTER_BINLOG_CLOSED 1186
//#define OBSOLETE_ER_INDEX_REBUILD 1187
#define ER_SOURCE 1188
#define ER_SOURCE_NET_READ 1189
#define ER_SOURCE_NET_WRITE 1190
#define ER_FT_MATCHING_KEY_NOT_FOUND 1191
#define ER_LOCK_OR_ACTIVE_TRANSACTION 1192
#define ER_UNKNOWN_SYSTEM_VARIABLE 1193
#define ER_CRASHED_ON_USAGE 1194
#define ER_CRASHED_ON_REPAIR 1195
#define ER_WARNING_NOT_COMPLETE_ROLLBACK 1196
#define ER_TRANS_CACHE_FULL 1197
//#define OBSOLETE_ER_SLAVE_MUST_STOP 1198
#define ER_REPLICA_NOT_RUNNING 1199
#define ER_BAD_REPLICA 1200
#define ER_CONNECTION_METADATA 1201
#define ER_REPLICA_THREAD 1202
#define ER_TOO_MANY_USER_CONNECTIONS 1203
#define ER_SET_CONSTANTS_ONLY 1204
#define ER_LOCK_WAIT_TIMEOUT 1205
#define ER_LOCK_TABLE_FULL 1206
#define ER_READ_ONLY_TRANSACTION 1207
//#define OBSOLETE_ER_DROP_DB_WITH_READ_LOCK 1208
//#define OBSOLETE_ER_CREATE_DB_WITH_READ_LOCK 1209
#define ER_WRONG_ARGUMENTS 1210
#define ER_NO_PERMISSION_TO_CREATE_USER 1211
//#define OBSOLETE_ER_UNION_TABLES_IN_DIFFERENT_DIR 1212
#define ER_LOCK_DEADLOCK 1213
#define ER_TABLE_CANT_HANDLE_FT 1214
#define ER_CANNOT_ADD_FOREIGN 1215
#define ER_NO_REFERENCED_ROW 1216
#define ER_ROW_IS_REFERENCED 1217
#define ER_CONNECT_TO_SOURCE 1218
//#define OBSOLETE_ER_QUERY_ON_MASTER 1219
#define ER_ERROR_WHEN_EXECUTING_COMMAND 1220
#define ER_WRONG_USAGE 1221
#define ER_WRONG_NUMBER_OF_COLUMNS_IN_SELECT 1222
#define ER_CANT_UPDATE_WITH_READLOCK 1223
#define ER_MIXING_NOT_ALLOWED 1224
#define ER_DUP_ARGUMENT 1225
#define ER_USER_LIMIT_REACHED 1226
#define ER_SPECIFIC_ACCESS_DENIED_ERROR 1227
#define ER_LOCAL_VARIABLE 1228
#define ER_GLOBAL_VARIABLE 1229
#define ER_NO_DEFAULT 1230
#define ER_WRONG_VALUE_FOR_VAR 1231
#define ER_WRONG_TYPE_FOR_VAR 1232
#define ER_VAR_CANT_BE_READ 1233
#define ER_CANT_USE_OPTION_HERE 1234
#define ER_NOT_SUPPORTED_YET 1235
#define ER_SOURCE_FATAL_ERROR_READING_BINLOG 1236
#define ER_REPLICA_IGNORED_TABLE 1237
#define ER_INCORRECT_GLOBAL_LOCAL_VAR 1238
#define ER_WRONG_FK_DEF 1239
#define ER_KEY_REF_DO_NOT_MATCH_TABLE_REF 1240
#define ER_OPERAND_COLUMNS 1241
#define ER_SUBQUERY_NO_1_ROW 1242
#define ER_UNKNOWN_STMT_HANDLER 1243
#define ER_CORRUPT_HELP_DB 1244
//#define OBSOLETE_ER_CYCLIC_REFERENCE 1245
#define ER_AUTO_CONVERT 1246
#define ER_ILLEGAL_REFERENCE 1247
#define ER_DERIVED_MUST_HAVE_ALIAS 1248
#define ER_SELECT_REDUCED 1249
#define ER_TABLENAME_NOT_ALLOWED_HERE 1250
#define ER_NOT_SUPPORTED_AUTH_MODE 1251
#define ER_SPATIAL_CANT_HAVE_NULL 1252
#define ER_COLLATION_CHARSET_MISMATCH 1253
//#define OBSOLETE_ER_SLAVE_WAS_RUNNING 1254
//#define OBSOLETE_ER_SLAVE_WAS_NOT_RUNNING 1255
#define ER_TOO_BIG_FOR_UNCOMPRESS 1256
#define ER_ZLIB_Z_MEM_ERROR 1257
#define ER_ZLIB_Z_BUF_ERROR 1258
#define ER_ZLIB_Z_DATA_ERROR 1259
#define ER_CUT_VALUE_GROUP_CONCAT 1260
#define ER_WARN_TOO_FEW_RECORDS 1261
#define ER_WARN_TOO_MANY_RECORDS 1262
#define ER_WARN_NULL_TO_NOTNULL 1263
#define ER_WARN_DATA_OUT_OF_RANGE 1264
#define WARN_DATA_TRUNCATED 1265
#define ER_WARN_USING_OTHER_HANDLER 1266
#define ER_CANT_AGGREGATE_2COLLATIONS 1267
//#define OBSOLETE_ER_DROP_USER 1268
#define ER_REVOKE_GRANTS 1269
#define ER_CANT_AGGREGATE_3COLLATIONS 1270
#define ER_CANT_AGGREGATE_NCOLLATIONS 1271
#define ER_VARIABLE_IS_NOT_STRUCT 1272
#define ER_UNKNOWN_COLLATION 1273
#define ER_REPLICA_IGNORED_SSL_PARAMS 1274
//#define OBSOLETE_ER_SERVER_IS_IN_SECURE_AUTH_MODE 1275
#define ER_WARN_FIELD_RESOLVED 1276
#define ER_BAD_REPLICA_UNTIL_COND 1277
#define ER_MISSING_SKIP_REPLICA 1278
#define ER_UNTIL_COND_IGNORED 1279
#define ER_WRONG_NAME_FOR_INDEX 1280
#define ER_WRONG_NAME_FOR_CATALOG 1281
//#define OBSOLETE_ER_WARN_QC_RESIZE 1282
#define ER_BAD_FT_COLUMN 1283
#define ER_UNKNOWN_KEY_CACHE 1284
#define ER_WARN_HOSTNAME_WONT_WORK 1285
#define ER_UNKNOWN_STORAGE_ENGINE 1286
#define ER_WARN_DEPRECATED_SYNTAX 1287
#define ER_NON_UPDATABLE_TABLE 1288
#define ER_FEATURE_DISABLED 1289
#define ER_OPTION_PREVENTS_STATEMENT 1290
#define ER_DUPLICATED_VALUE_IN_TYPE 1291
#define ER_TRUNCATED_WRONG_VALUE 1292
//#define OBSOLETE_ER_TOO_MUCH_AUTO_TIMESTAMP_COLS 1293
#define ER_INVALID_ON_UPDATE 1294
#define ER_UNSUPPORTED_PS 1295
#define ER_GET_ERRMSG 1296
#define ER_GET_TEMPORARY_ERRMSG 1297
#define ER_UNKNOWN_TIME_ZONE 1298
#define ER_WARN_INVALID_TIMESTAMP 1299
#define ER_INVALID_CHARACTER_STRING 1300
#define ER_WARN_ALLOWED_PACKET_OVERFLOWED 1301
#define ER_CONFLICTING_DECLARATIONS 1302
#define ER_SP_NO_RECURSIVE_CREATE 1303
#define ER_SP_ALREADY_EXISTS 1304
#define ER_SP_DOES_NOT_EXIST 1305
#define ER_SP_DROP_FAILED 1306
#define ER_SP_STORE_FAILED 1307
#define ER_SP_LILABEL_MISMATCH 1308
#define ER_SP_LABEL_REDEFINE 1309
#define ER_SP_LABEL_MISMATCH 1310
#define ER_SP_UNINIT_VAR 1311
#define ER_SP_BADSELECT 1312
#define ER_SP_BADRETURN 1313
#define ER_SP_BADSTATEMENT 1314
#define ER_UPDATE_LOG_DEPRECATED_IGNORED 1315
#define ER_UPDATE_LOG_DEPRECATED_TRANSLATED 1316
#define ER_QUERY_INTERRUPTED 1317
#define ER_SP_WRONG_NO_OF_ARGS 1318
#define ER_SP_COND_MISMATCH 1319
#define ER_SP_NORETURN 1320
#define ER_SP_NORETURNEND 1321
#define ER_SP_BAD_CURSOR_QUERY 1322
#define ER_SP_BAD_CURSOR_SELECT 1323
#define ER_SP_CURSOR_MISMATCH 1324
#define ER_SP_CURSOR_ALREADY_OPEN 1325
#define ER_SP_CURSOR_NOT_OPEN 1326
#define ER_SP_UNDECLARED_VAR 1327
#define ER_SP_WRONG_NO_OF_FETCH_ARGS 1328
#define ER_SP_FETCH_NO_DATA 1329
#define ER_SP_DUP_PARAM 1330
#define ER_SP_DUP_VAR 1331
#define ER_SP_DUP_COND 1332
#define ER_SP_DUP_CURS 1333
#define ER_SP_CANT_ALTER 1334
#define ER_SP_SUBSELECT_NYI 1335
#define ER_STMT_NOT_ALLOWED_IN_SF_OR_TRG 1336
#define ER_SP_VARCOND_AFTER_CURSHNDLR 1337
#define ER_SP_CURSOR_AFTER_HANDLER 1338
#define ER_SP_CASE_NOT_FOUND 1339
#define ER_FPARSER_TOO_BIG_FILE 1340
#define ER_FPARSER_BAD_HEADER 1341
#define ER_FPARSER_EOF_IN_COMMENT 1342
#define ER_FPARSER_ERROR_IN_PARAMETER 1343
#define ER_FPARSER_EOF_IN_UNKNOWN_PARAMETER 1344
#define ER_VIEW_NO_EXPLAIN 1345
//#define OBSOLETE_ER_FRM_UNKNOWN_TYPE 1346
#define ER_WRONG_OBJECT 1347
#define ER_NONUPDATEABLE_COLUMN 1348
//#define OBSOLETE_ER_VIEW_SELECT_DERIVED_UNUSED 1349
#define ER_VIEW_SELECT_CLAUSE 1350
#define ER_VIEW_SELECT_VARIABLE 1351
#define ER_VIEW_SELECT_TMPTABLE 1352
#define ER_VIEW_WRONG_LIST 1353
#define ER_WARN_VIEW_MERGE 1354
#define ER_WARN_VIEW_WITHOUT_KEY 1355
#define ER_VIEW_INVALID 1356
#define ER_SP_NO_DROP_SP 1357
//#define OBSOLETE_ER_SP_GOTO_IN_HNDLR 1358
#define ER_TRG_ALREADY_EXISTS 1359
#define ER_TRG_DOES_NOT_EXIST 1360
#define ER_TRG_ON_VIEW_OR_TEMP_TABLE 1361
#define ER_TRG_CANT_CHANGE_ROW 1362
#define ER_TRG_NO_SUCH_ROW_IN_TRG 1363
#define ER_NO_DEFAULT_FOR_FIELD 1364
#define ER_DIVISION_BY_ZERO 1365
#define ER_TRUNCATED_WRONG_VALUE_FOR_FIELD 1366
#define ER_ILLEGAL_VALUE_FOR_TYPE 1367
#define ER_VIEW_NONUPD_CHECK 1368
#define ER_VIEW_CHECK_FAILED 1369
#define ER_PROCACCESS_DENIED_ERROR 1370
#define ER_RELAY_LOG_FAIL 1371
//#define OBSOLETE_ER_PASSWD_LENGTH 1372
#define ER_UNKNOWN_TARGET_BINLOG 1373
#define ER_IO_ERR_LOG_INDEX_READ 1374
#define ER_BINLOG_PURGE_PROHIBITED 1375
#define ER_FSEEK_FAIL 1376
#define ER_BINLOG_PURGE_FATAL_ERR 1377
#define ER_LOG_IN_USE 1378
#define ER_LOG_PURGE_UNKNOWN_ERR 1379
#define ER_RELAY_LOG_INIT 1380
#define ER_NO_BINARY_LOGGING 1381
#define ER_RESERVED_SYNTAX 1382
//#define OBSOLETE_ER_WSAS_FAILED 1383
//#define OBSOLETE_ER_DIFF_GROUPS_PROC 1384
//#define OBSOLETE_ER_NO_GROUP_FOR_PROC 1385
//#define OBSOLETE_ER_ORDER_WITH_PROC 1386
//#define OBSOLETE_ER_LOGGING_PROHIBIT_CHANGING_OF 1387
//#define OBSOLETE_ER_NO_FILE_MAPPING 1388
//#define OBSOLETE_ER_WRONG_MAGIC 1389
#define ER_PS_MANY_PARAM 1390
#define ER_KEY_PART_0 1391
#define ER_VIEW_CHECKSUM 1392
#define ER_VIEW_MULTIUPDATE 1393
#define ER_VIEW_NO_INSERT_FIELD_LIST 1394
#define ER_VIEW_DELETE_MERGE_VIEW 1395
#define ER_CANNOT_USER 1396
#define ER_XAER_NOTA 1397
#define ER_XAER_INVAL 1398
#define ER_XAER_RMFAIL 1399
#define ER_XAER_OUTSIDE 1400
#define ER_XAER_RMERR 1401
#define ER_XA_RBROLLBACK 1402
#define ER_NONEXISTING_PROC_GRANT 1403
#define ER_PROC_AUTO_GRANT_FAIL 1404
#define ER_PROC_AUTO_REVOKE_FAIL 1405
#define ER_DATA_TOO_LONG 1406
#define ER_SP_BAD_SQLSTATE 1407
#define ER_STARTUP 1408
#define ER_LOAD_FROM_FIXED_SIZE_ROWS_TO_VAR 1409
#define ER_CANT_CREATE_USER_WITH_GRANT 1410
#define ER_WRONG_VALUE_FOR_TYPE 1411
#define ER_TABLE_DEF_CHANGED 1412
#define ER_SP_DUP_HANDLER 1413
#define ER_SP_NOT_VAR_ARG 1414
#define ER_SP_NO_RETSET 1415
#define ER_CANT_CREATE_GEOMETRY_OBJECT 1416
//#define OBSOLETE_ER_FAILED_ROUTINE_BREAK_BINLOG 1417
#define ER_BINLOG_UNSAFE_ROUTINE 1418
#define ER_BINLOG_CREATE_ROUTINE_NEED_SUPER 1419
//#define OBSOLETE_ER_EXEC_STMT_WITH_OPEN_CURSOR 1420
#define ER_STMT_HAS_NO_OPEN_CURSOR 1421
#define ER_COMMIT_NOT_ALLOWED_IN_SF_OR_TRG 1422
#define ER_NO_DEFAULT_FOR_VIEW_FIELD 1423
#define ER_SP_NO_RECURSION 1424
#define ER_TOO_BIG_SCALE 1425
#define ER_TOO_BIG_PRECISION 1426
#define ER_M_BIGGER_THAN_D 1427
#define ER_WRONG_LOCK_OF_SYSTEM_TABLE 1428
#define ER_CONNECT_TO_FOREIGN_DATA_SOURCE 1429
#define ER_QUERY_ON_FOREIGN_DATA_SOURCE 1430
#define ER_FOREIGN_DATA_SOURCE_DOESNT_EXIST 1431
#define ER_FOREIGN_DATA_STRING_INVALID_CANT_CREATE 1432
#define ER_FOREIGN_DATA_STRING_INVALID 1433
//#define OBSOLETE_ER_CANT_CREATE_FEDERATED_TABLE 1434
#define ER_TRG_IN_WRONG_SCHEMA 1435
#define ER_STACK_OVERRUN_NEED_MORE 1436
#define ER_TOO_LONG_BODY 1437
#define ER_WARN_CANT_DROP_DEFAULT_KEYCACHE 1438
#define ER_TOO_BIG_DISPLAYWIDTH 1439
#define ER_XAER_DUPID 1440
#define ER_DATETIME_FUNCTION_OVERFLOW 1441
#define ER_CANT_UPDATE_USED_TABLE_IN_SF_OR_TRG 1442
#define ER_VIEW_PREVENT_UPDATE 1443
#define ER_PS_NO_RECURSION 1444
#define ER_SP_CANT_SET_AUTOCOMMIT 1445
//#define OBSOLETE_ER_MALFORMED_DEFINER 1446
#define ER_VIEW_FRM_NO_USER 1447
#define ER_VIEW_OTHER_USER 1448
#define ER_NO_SUCH_USER 1449
#define ER_FORBID_SCHEMA_CHANGE 1450
#define ER_ROW_IS_REFERENCED_2 1451
#define ER_NO_REFERENCED_ROW_2 1452
#define ER_SP_BAD_VAR_SHADOW 1453
#define ER_TRG_NO_DEFINER 1454
#define ER_OLD_FILE_FORMAT 1455
#define ER_SP_RECURSION_LIMIT 1456
//#define OBSOLETE_ER_SP_PROC_TABLE_CORRUPT 1457
#define ER_SP_WRONG_NAME 1458
#define ER_TABLE_NEEDS_UPGRADE 1459
#define ER_SP_NO_AGGREGATE 1460
#define ER_MAX_PREPARED_STMT_COUNT_REACHED 1461
#define ER_VIEW_RECURSIVE 1462
#define ER_NON_GROUPING_FIELD_USED 1463
#define ER_TABLE_CANT_HANDLE_SPKEYS 1464
#define ER_NO_TRIGGERS_ON_SYSTEM_SCHEMA 1465
#define ER_REMOVED_SPACES 1466
#define ER_AUTOINC_READ_FAILED 1467
#define ER_USERNAME 1468
#define ER_HOSTNAME 1469
#define ER_WRONG_STRING_LENGTH 1470
#define ER_NON_INSERTABLE_TABLE 1471
#define ER_ADMIN_WRONG_MRG_TABLE 1472
#define ER_TOO_HIGH_LEVEL_OF_NESTING_FOR_SELECT 1473
#define ER_NAME_BECOMES_EMPTY 1474
#define ER_AMBIGUOUS_FIELD_TERM 1475
#define ER_FOREIGN_SERVER_EXISTS 1476
#define ER_FOREIGN_SERVER_DOESNT_EXIST 1477
#define ER_ILLEGAL_HA_CREATE_OPTION 1478
#define ER_PARTITION_REQUIRES_VALUES_ERROR 1479
#define ER_PARTITION_WRONG_VALUES_ERROR 1480
#define ER_PARTITION_MAXVALUE_ERROR 1481
//#define OBSOLETE_ER_PARTITION_SUBPARTITION_ERROR 1482
//#define OBSOLETE_ER_PARTITION_SUBPART_MIX_ERROR 1483
#define ER_PARTITION_WRONG_NO_PART_ERROR 1484
#define ER_PARTITION_WRONG_NO_SUBPART_ERROR 1485
#define ER_WRONG_EXPR_IN_PARTITION_FUNC_ERROR 1486
//#define OBSOLETE_ER_NO_CONST_EXPR_IN_RANGE_OR_LIST_ERROR 1487
#define ER_FIELD_NOT_FOUND_PART_ERROR 1488
//#define OBSOLETE_ER_LIST_OF_FIELDS_ONLY_IN_HASH_ERROR 1489
#define ER_INCONSISTENT_PARTITION_INFO_ERROR 1490
#define ER_PARTITION_FUNC_NOT_ALLOWED_ERROR 1491
#define ER_PARTITIONS_MUST_BE_DEFINED_ERROR 1492
#define ER_RANGE_NOT_INCREASING_ERROR 1493
#define ER_INCONSISTENT_TYPE_OF_FUNCTIONS_ERROR 1494
#define ER_MULTIPLE_DEF_CONST_IN_LIST_PART_ERROR 1495
#define ER_PARTITION_ENTRY_ERROR 1496
#define ER_MIX_HANDLER_ERROR 1497
#define ER_PARTITION_NOT_DEFINED_ERROR 1498
#define ER_TOO_MANY_PARTITIONS_ERROR 1499
#define ER_SUBPARTITION_ERROR 1500
#define ER_CANT_CREATE_HANDLER_FILE 1501
#define ER_BLOB_FIELD_IN_PART_FUNC_ERROR 1502
#define ER_UNIQUE_KEY_NEED_ALL_FIELDS_IN_PF 1503
#define ER_NO_PARTS_ERROR 1504
#define ER_PARTITION_MGMT_ON_NONPARTITIONED 1505
#define ER_FOREIGN_KEY_ON_PARTITIONED 1506
#define ER_DROP_PARTITION_NON_EXISTENT 1507
#define ER_DROP_LAST_PARTITION 1508
#define ER_COALESCE_ONLY_ON_HASH_PARTITION 1509
#define ER_REORG_HASH_ONLY_ON_SAME_NO 1510
#define ER_REORG_NO_PARAM_ERROR 1511
#define ER_ONLY_ON_RANGE_LIST_PARTITION 1512
#define ER_ADD_PARTITION_SUBPART_ERROR 1513
#define ER_ADD_PARTITION_NO_NEW_PARTITION 1514
#define ER_COALESCE_PARTITION_NO_PARTITION 1515
#define ER_REORG_PARTITION_NOT_EXIST 1516
#define ER_SAME_NAME_PARTITION 1517
#define ER_NO_BINLOG_ERROR 1518
#define ER_CONSECUTIVE_REORG_PARTITIONS 1519
#define ER_REORG_OUTSIDE_RANGE 1520
#define ER_PARTITION_FUNCTION_FAILURE 1521
//#define OBSOLETE_ER_PART_STATE_ERROR 1522
#define ER_LIMITED_PART_RANGE 1523
#define ER_PLUGIN_IS_NOT_LOADED 1524
#define ER_WRONG_VALUE 1525
#define ER_NO_PARTITION_FOR_GIVEN_VALUE 1526
#define ER_FILEGROUP_OPTION_ONLY_ONCE 1527
#define ER_CREATE_FILEGROUP_FAILED 1528
#define ER_DROP_FILEGROUP_FAILED 1529
#define ER_TABLESPACE_AUTO_EXTEND_ERROR 1530
#define ER_WRONG_SIZE_NUMBER 1531
#define ER_SIZE_OVERFLOW_ERROR 1532
#define ER_ALTER_FILEGROUP_FAILED 1533
#define ER_BINLOG_ROW_LOGGING_FAILED 1534
//#define OBSOLETE_ER_BINLOG_ROW_WRONG_TABLE_DEF 1535
//#define OBSOLETE_ER_BINLOG_ROW_RBR_TO_SBR 1536
#define ER_EVENT_ALREADY_EXISTS 1537
//#define OBSOLETE_ER_EVENT_STORE_FAILED 1538
#define ER_EVENT_DOES_NOT_EXIST 1539
//#define OBSOLETE_ER_EVENT_CANT_ALTER 1540
//#define OBSOLETE_ER_EVENT_DROP_FAILED 1541
#define ER_EVENT_INTERVAL_NOT_POSITIVE_OR_TOO_BIG 1542
#define ER_EVENT_ENDS_BEFORE_STARTS 1543
#define ER_EVENT_EXEC_TIME_IN_THE_PAST 1544
//#define OBSOLETE_ER_EVENT_OPEN_TABLE_FAILED 1545
//#define OBSOLETE_ER_EVENT_NEITHER_M_EXPR_NOR_M_AT 1546
//#define OBSOLETE_ER_COL_COUNT_DOESNT_MATCH_CORRUPTED 1547
//#define OBSOLETE_ER_CANNOT_LOAD_FROM_TABLE 1548
//#define OBSOLETE_ER_EVENT_CANNOT_DELETE 1549
//#define OBSOLETE_ER_EVENT_COMPILE_ERROR 1550
#define ER_EVENT_SAME_NAME 1551
//#define OBSOLETE_ER_EVENT_DATA_TOO_LONG 1552
#define ER_DROP_INDEX_FK 1553
#define ER_WARN_DEPRECATED_SYNTAX_WITH_VER 1554
//#define OBSOLETE_ER_CANT_WRITE_LOCK_LOG_TABLE 1555
#define ER_CANT_LOCK_LOG_TABLE 1556
#define ER_FOREIGN_DUPLICATE_KEY_OLD_UNUSED 1557
#define ER_COL_COUNT_DOESNT_MATCH_PLEASE_UPDATE 1558
//#define OBSOLETE_ER_TEMP_TABLE_PREVENTS_SWITCH_OUT_OF_RBR 1559
#define ER_STORED_FUNCTION_PREVENTS_SWITCH_BINLOG_FORMAT 1560
//#define OBSOLETE_ER_NDB_CANT_SWITCH_BINLOG_FORMAT 1561
#define ER_PARTITION_NO_TEMPORARY 1562
#define ER_PARTITION_CONST_DOMAIN_ERROR 1563
#define ER_PARTITION_FUNCTION_IS_NOT_ALLOWED 1564
//#define OBSOLETE_ER_DDL_LOG_ERROR_UNUSED 1565
#define ER_NULL_IN_VALUES_LESS_THAN 1566
#define ER_WRONG_PARTITION_NAME 1567
#define ER_CANT_CHANGE_TX_CHARACTERISTICS 1568
#define ER_DUP_ENTRY_AUTOINCREMENT_CASE 1569
//#define OBSOLETE_ER_EVENT_MODIFY_QUEUE_ERROR 1570
#define ER_EVENT_SET_VAR_ERROR 1571
#define ER_PARTITION_MERGE_ERROR 1572
//#define OBSOLETE_ER_CANT_ACTIVATE_LOG 1573
//#define OBSOLETE_ER_RBR_NOT_AVAILABLE 1574
#define ER_BASE64_DECODE_ERROR 1575
#define ER_EVENT_RECURSION_FORBIDDEN 1576
//#define OBSOLETE_ER_EVENTS_DB_ERROR 1577
#define ER_ONLY_INTEGERS_ALLOWED 1578
#define ER_UNSUPORTED_LOG_ENGINE 1579
#define ER_BAD_LOG_STATEMENT 1580
#define ER_CANT_RENAME_LOG_TABLE 1581
#define ER_WRONG_PARAMCOUNT_TO_NATIVE_FCT 1582
#define ER_WRONG_PARAMETERS_TO_NATIVE_FCT 1583
#define ER_WRONG_PARAMETERS_TO_STORED_FCT 1584
#define ER_NATIVE_FCT_NAME_COLLISION 1585
#define ER_DUP_ENTRY_WITH_KEY_NAME 1586
#define ER_BINLOG_PURGE_EMFILE 1587
#define ER_EVENT_CANNOT_CREATE_IN_THE_PAST 1588
#define ER_EVENT_CANNOT_ALTER_IN_THE_PAST 1589
//#define OBSOLETE_ER_SLAVE_INCIDENT 1590
#define ER_NO_PARTITION_FOR_GIVEN_VALUE_SILENT 1591
#define ER_BINLOG_UNSAFE_STATEMENT 1592
#define ER_BINLOG_FATAL_ERROR 1593
//#define OBSOLETE_ER_SLAVE_RELAY_LOG_READ_FAILURE 1594
//#define OBSOLETE_ER_SLAVE_RELAY_LOG_WRITE_FAILURE 1595
//#define OBSOLETE_ER_SLAVE_CREATE_EVENT_FAILURE 1596
//#define OBSOLETE_ER_SLAVE_MASTER_COM_FAILURE 1597
#define ER_BINLOG_LOGGING_IMPOSSIBLE 1598
#define ER_VIEW_NO_CREATION_CTX 1599
#define ER_VIEW_INVALID_CREATION_CTX 1600
//#define OBSOLETE_ER_SR_INVALID_CREATION_CTX 1601
#define ER_TRG_CORRUPTED_FILE 1602
#define ER_TRG_NO_CREATION_CTX 1603
#define ER_TRG_INVALID_CREATION_CTX 1604
#define ER_EVENT_INVALID_CREATION_CTX 1605
#define ER_TRG_CANT_OPEN_TABLE 1606
//#define OBSOLETE_ER_CANT_CREATE_SROUTINE 1607
//#define OBSOLETE_ER_NEVER_USED 1608
#define ER_NO_FORMAT_DESCRIPTION_EVENT_BEFORE_BINLOG_STATEMENT 1609
#define ER_REPLICA_CORRUPT_EVENT 1610
//#define OBSOLETE_ER_LOAD_DATA_INVALID_COLUMN_UNUSED 1611
#define ER_LOG_PURGE_NO_FILE 1612
#define ER_XA_RBTIMEOUT 1613
#define ER_XA_RBDEADLOCK 1614
#define ER_NEED_REPREPARE 1615
//#define OBSOLETE_ER_DELAYED_NOT_SUPPORTED 1616
#define WARN_NO_CONNECTION_METADATA 1617
#define WARN_OPTION_IGNORED 1618
#define ER_PLUGIN_DELETE_BUILTIN 1619
#define WARN_PLUGIN_BUSY 1620
#define ER_VARIABLE_IS_READONLY 1621
#define ER_WARN_ENGINE_TRANSACTION_ROLLBACK 1622
//#define OBSOLETE_ER_SLAVE_HEARTBEAT_FAILURE 1623
#define ER_REPLICA_HEARTBEAT_VALUE_OUT_OF_RANGE 1624
#define ER_NDB_REPLICATION_SCHEMA_ERROR 1625
#define ER_CONFLICT_FN_PARSE_ERROR 1626
#define ER_EXCEPTIONS_WRITE_ERROR 1627
#define ER_TOO_LONG_TABLE_COMMENT 1628
#define ER_TOO_LONG_FIELD_COMMENT 1629
#define ER_FUNC_INEXISTENT_NAME_COLLISION 1630
#define ER_DATABASE_NAME 1631
#define ER_TABLE_NAME 1632
#define ER_PARTITION_NAME 1633
#define ER_SUBPARTITION_NAME 1634
#define ER_TEMPORARY_NAME 1635
#define ER_RENAMED_NAME 1636
#define ER_TOO_MANY_CONCURRENT_TRXS 1637
#define WARN_NON_ASCII_SEPARATOR_NOT_IMPLEMENTED 1638
#define ER_DEBUG_SYNC_TIMEOUT 1639
#define ER_DEBUG_SYNC_HIT_LIMIT 1640
#define ER_DUP_SIGNAL_SET 1641
#define ER_SIGNAL_WARN 1642
#define ER_SIGNAL_NOT_FOUND 1643
#define ER_SIGNAL_EXCEPTION 1644
#define ER_RESIGNAL_WITHOUT_ACTIVE_HANDLER 1645
#define ER_SIGNAL_BAD_CONDITION_TYPE 1646
#define WARN_COND_ITEM_TRUNCATED 1647
#define ER_COND_ITEM_TOO_LONG 1648
#define ER_UNKNOWN_LOCALE 1649
#define ER_REPLICA_IGNORE_SERVER_IDS 1650
//#define OBSOLETE_ER_QUERY_CACHE_DISABLED 1651
#define ER_SAME_NAME_PARTITION_FIELD 1652
#define ER_PARTITION_COLUMN_LIST_ERROR 1653
#define ER_WRONG_TYPE_COLUMN_VALUE_ERROR 1654
#define ER_TOO_MANY_PARTITION_FUNC_FIELDS_ERROR 1655
#define ER_MAXVALUE_IN_VALUES_IN 1656
#define ER_TOO_MANY_VALUES_ERROR 1657
#define ER_ROW_SINGLE_PARTITION_FIELD_ERROR 1658
#define ER_FIELD_TYPE_NOT_ALLOWED_AS_PARTITION_FIELD 1659
#define ER_PARTITION_FIELDS_TOO_LONG 1660
#define ER_BINLOG_ROW_ENGINE_AND_STMT_ENGINE 1661
#define ER_BINLOG_ROW_MODE_AND_STMT_ENGINE 1662
#define ER_BINLOG_UNSAFE_AND_STMT_ENGINE 1663
#define ER_BINLOG_ROW_INJECTION_AND_STMT_ENGINE 1664
#define ER_BINLOG_STMT_MODE_AND_ROW_ENGINE 1665
#define ER_BINLOG_ROW_INJECTION_AND_STMT_MODE 1666
#define ER_BINLOG_MULTIPLE_ENGINES_AND_SELF_LOGGING_ENGINE 1667
#define ER_BINLOG_UNSAFE_LIMIT 1668
//#define OBSOLETE_ER_UNUSED4 1669
#define ER_BINLOG_UNSAFE_SYSTEM_TABLE 1670
#define ER_BINLOG_UNSAFE_AUTOINC_COLUMNS 1671
#define ER_BINLOG_UNSAFE_UDF 1672
#define ER_BINLOG_UNSAFE_SYSTEM_VARIABLE 1673
#define ER_BINLOG_UNSAFE_SYSTEM_FUNCTION 1674
#define ER_BINLOG_UNSAFE_NONTRANS_AFTER_TRANS 1675
#define ER_MESSAGE_AND_STATEMENT 1676
//#define OBSOLETE_ER_SLAVE_CONVERSION_FAILED 1677
#define ER_REPLICA_CANT_CREATE_CONVERSION 1678
#define ER_INSIDE_TRANSACTION_PREVENTS_SWITCH_BINLOG_FORMAT 1679
#define ER_PATH_LENGTH 1680
#define ER_WARN_DEPRECATED_SYNTAX_NO_REPLACEMENT 1681
#define ER_WRONG_NATIVE_TABLE_STRUCTURE 1682
#define ER_WRONG_PERFSCHEMA_USAGE 1683
#define ER_WARN_I_S_SKIPPED_TABLE 1684
#define ER_INSIDE_TRANSACTION_PREVENTS_SWITCH_BINLOG_DIRECT 1685
#define ER_STORED_FUNCTION_PREVENTS_SWITCH_BINLOG_DIRECT 1686
#define ER_SPATIAL_MUST_HAVE_GEOM_COL 1687
#define ER_TOO_LONG_INDEX_COMMENT 1688
#define ER_LOCK_ABORTED 1689
#define ER_DATA_OUT_OF_RANGE 1690
//#define OBSOLETE_ER_WRONG_SPVAR_TYPE_IN_LIMIT 1691
#define ER_BINLOG_UNSAFE_MULTIPLE_ENGINES_AND_SELF_LOGGING_ENGINE 1692
#define ER_BINLOG_UNSAFE_MIXED_STATEMENT 1693
#define ER_INSIDE_TRANSACTION_PREVENTS_SWITCH_SQL_LOG_BIN 1694
#define ER_STORED_FUNCTION_PREVENTS_SWITCH_SQL_LOG_BIN 1695
#define ER_FAILED_READ_FROM_PAR_FILE 1696
#define ER_VALUES_IS_NOT_INT_TYPE_ERROR 1697
#define ER_ACCESS_DENIED_NO_PASSWORD_ERROR 1698
//#define OBSOLETE_ER_SET_PASSWORD_AUTH_PLUGIN 1699
//#define OBSOLETE_ER_GRANT_PLUGIN_USER_EXISTS 1700
#define ER_TRUNCATE_ILLEGAL_FK 1701
#define ER_PLUGIN_IS_PERMANENT 1702
#define ER_REPLICA_HEARTBEAT_VALUE_OUT_OF_RANGE_MIN 1703
#define ER_REPLICA_HEARTBEAT_VALUE_OUT_OF_RANGE_MAX 1704
#define ER_STMT_CACHE_FULL 1705
#define ER_MULTI_UPDATE_KEY_CONFLICT 1706
#define ER_TABLE_NEEDS_REBUILD 1707
#define WARN_OPTION_BELOW_LIMIT 1708
#define ER_INDEX_COLUMN_TOO_LONG 1709
#define ER_ERROR_IN_TRIGGER_BODY 1710
#define ER_ERROR_IN_UNKNOWN_TRIGGER_BODY 1711
#define ER_INDEX_CORRUPT 1712
#define ER_UNDO_RECORD_TOO_BIG 1713
#define ER_BINLOG_UNSAFE_INSERT_IGNORE_SELECT 1714
#define ER_BINLOG_UNSAFE_INSERT_SELECT_UPDATE 1715
#define ER_BINLOG_UNSAFE_REPLACE_SELECT 1716
#define ER_BINLOG_UNSAFE_CREATE_IGNORE_SELECT 1717
#define ER_BINLOG_UNSAFE_CREATE_REPLACE_SELECT 1718
#define ER_BINLOG_UNSAFE_UPDATE_IGNORE 1719
#define ER_PLUGIN_NO_UNINSTALL 1720
#define ER_PLUGIN_NO_INSTALL 1721
#define ER_BINLOG_UNSAFE_WRITE_AUTOINC_SELECT 1722
#define ER_BINLOG_UNSAFE_CREATE_SELECT_AUTOINC 1723
#define ER_BINLOG_UNSAFE_INSERT_TWO_KEYS 1724
#define ER_TABLE_IN_FK_CHECK 1725
#define ER_UNSUPPORTED_ENGINE 1726
#define ER_BINLOG_UNSAFE_AUTOINC_NOT_FIRST 1727
#define ER_CANNOT_LOAD_FROM_TABLE_V2 1728
#define ER_SOURCE_DELAY_VALUE_OUT_OF_RANGE 1729
#define ER_ONLY_FD_AND_RBR_EVENTS_ALLOWED_IN_BINLOG_STATEMENT 1730
#define ER_PARTITION_EXCHANGE_DIFFERENT_OPTION 1731
#define ER_PARTITION_EXCHANGE_PART_TABLE 1732
#define ER_PARTITION_EXCHANGE_TEMP_TABLE 1733
#define ER_PARTITION_INSTEAD_OF_SUBPARTITION 1734
#define ER_UNKNOWN_PARTITION 1735
#define ER_TABLES_DIFFERENT_METADATA 1736
#define ER_ROW_DOES_NOT_MATCH_PARTITION 1737
#define ER_BINLOG_CACHE_SIZE_GREATER_THAN_MAX 1738
#define ER_WARN_INDEX_NOT_APPLICABLE 1739
#define ER_PARTITION_EXCHANGE_FOREIGN_KEY 1740
//#define OBSOLETE_ER_NO_SUCH_KEY_VALUE 1741
#define ER_RPL_INFO_DATA_TOO_LONG 1742
//#define OBSOLETE_ER_NETWORK_READ_EVENT_CHECKSUM_FAILURE 1743
//#define OBSOLETE_ER_BINLOG_READ_EVENT_CHECKSUM_FAILURE 1744
#define ER_BINLOG_STMT_CACHE_SIZE_GREATER_THAN_MAX 1745
#define ER_CANT_UPDATE_TABLE_IN_CREATE_TABLE_SELECT 1746
#define ER_PARTITION_CLAUSE_ON_NONPARTITIONED 1747
#define ER_ROW_DOES_NOT_MATCH_GIVEN_PARTITION_SET 1748
//#define OBSOLETE_ER_NO_SUCH_PARTITION__UNUSED 1749
#define ER_CHANGE_RPL_INFO_REPOSITORY_FAILURE 1750
#define ER_WARNING_NOT_COMPLETE_ROLLBACK_WITH_CREATED_TEMP_TABLE 1751
#define ER_WARNING_NOT_COMPLETE_ROLLBACK_WITH_DROPPED_TEMP_TABLE 1752
#define ER_MTA_FEATURE_IS_NOT_SUPPORTED 1753
#define ER_MTA_UPDATED_DBS_GREATER_MAX 1754
#define ER_MTA_CANT_PARALLEL 1755
#define ER_MTA_INCONSISTENT_DATA 1756
#define ER_FULLTEXT_NOT_SUPPORTED_WITH_PARTITIONING 1757
#define ER_DA_INVALID_CONDITION_NUMBER 1758
#define ER_INSECURE_PLAIN_TEXT 1759
#define ER_INSECURE_CHANGE_SOURCE 1760
#define ER_FOREIGN_DUPLICATE_KEY_WITH_CHILD_INFO 1761
#define ER_FOREIGN_DUPLICATE_KEY_WITHOUT_CHILD_INFO 1762
#define ER_SQLTHREAD_WITH_SECURE_REPLICA 1763
#define ER_TABLE_HAS_NO_FT 1764
#define ER_VARIABLE_NOT_SETTABLE_IN_SF_OR_TRIGGER 1765
#define ER_VARIABLE_NOT_SETTABLE_IN_TRANSACTION 1766
//#define OBSOLETE_ER_GTID_NEXT_IS_NOT_IN_GTID_NEXT_LIST 1767
//#define OBSOLETE_ER_CANT_CHANGE_GTID_NEXT_IN_TRANSACTION 1768
#define ER_SET_STATEMENT_CANNOT_INVOKE_FUNCTION 1769
#define ER_GTID_NEXT_CANT_BE_AUTOMATIC_IF_GTID_NEXT_LIST_IS_NON_NULL 1770
//#define OBSOLETE_ER_SKIPPING_LOGGED_TRANSACTION 1771
#define ER_MALFORMED_GTID_SET_SPECIFICATION 1772
#define ER_MALFORMED_GTID_SET_ENCODING 1773
#define ER_MALFORMED_GTID_SPECIFICATION 1774
#define ER_GNO_EXHAUSTED 1775
#define ER_BAD_REPLICA_AUTO_POSITION 1776
#define ER_AUTO_POSITION_REQUIRES_GTID_MODE_NOT_OFF 1777
#define ER_CANT_DO_IMPLICIT_COMMIT_IN_TRX_WHEN_GTID_NEXT_IS_SET 1778
#define ER_GTID_MODE_ON_REQUIRES_ENFORCE_GTID_CONSISTENCY_ON 1779
//#define OBSOLETE_ER_GTID_MODE_REQUIRES_BINLOG 1780
#define ER_CANT_SET_GTID_NEXT_TO_GTID_WHEN_GTID_MODE_IS_OFF 1781
#define ER_CANT_SET_GTID_NEXT_TO_ANONYMOUS_WHEN_GTID_MODE_IS_ON 1782
#define ER_CANT_SET_GTID_NEXT_LIST_TO_NON_NULL_WHEN_GTID_MODE_IS_OFF 1783
//#define OBSOLETE_ER_FOUND_GTID_EVENT_WHEN_GTID_MODE_IS_OFF__UNUSED 1784
#define ER_GTID_UNSAFE_NON_TRANSACTIONAL_TABLE 1785
#define ER_GTID_UNSAFE_CREATE_SELECT 1786
//#define OBSOLETE_ER_GTID_UNSAFE_CREATE_DROP_TEMP_TABLE_IN_TRANSACTION 1787
#define ER_GTID_MODE_CAN_ONLY_CHANGE_ONE_STEP_AT_A_TIME 1788
#define ER_SOURCE_HAS_PURGED_REQUIRED_GTIDS 1789
#define ER_CANT_SET_GTID_NEXT_WHEN_OWNING_GTID 1790
#define ER_UNKNOWN_EXPLAIN_FORMAT 1791
#define ER_CANT_EXECUTE_IN_READ_ONLY_TRANSACTION 1792
#define ER_TOO_LONG_TABLE_PARTITION_COMMENT 1793
#define ER_REPLICA_CONFIGURATION 1794
#define ER_INNODB_FT_LIMIT 1795
#define ER_INNODB_NO_FT_TEMP_TABLE 1796
#define ER_INNODB_FT_WRONG_DOCID_COLUMN 1797
#define ER_INNODB_FT_WRONG_DOCID_INDEX 1798
#define ER_INNODB_ONLINE_LOG_TOO_BIG 1799
#define ER_UNKNOWN_ALTER_ALGORITHM 1800
#define ER_UNKNOWN_ALTER_LOCK 1801
#define ER_MTA_CHANGE_SOURCE_CANT_RUN_WITH_GAPS 1802
#define ER_MTA_RECOVERY_FAILURE 1803
#define ER_MTA_RESET_WORKERS 1804
#define ER_COL_COUNT_DOESNT_MATCH_CORRUPTED_V2 1805
#define ER_REPLICA_SILENT_RETRY_TRANSACTION 1806
#define ER_DISCARD_FK_CHECKS_RUNNING 1807
#define ER_TABLE_SCHEMA_MISMATCH 1808
#define ER_TABLE_IN_SYSTEM_TABLESPACE 1809
#define ER_IO_READ_ERROR 1810
#define ER_IO_WRITE_ERROR 1811
#define ER_TABLESPACE_MISSING 1812
#define ER_TABLESPACE_EXISTS 1813
#define ER_TABLESPACE_DISCARDED 1814
#define ER_INTERNAL_ERROR 1815
#define ER_INNODB_IMPORT_ERROR 1816
#define ER_INNODB_INDEX_CORRUPT 1817
#define ER_INVALID_YEAR_COLUMN_LENGTH 1818
#define ER_NOT_VALID_PASSWORD 1819
#define ER_MUST_CHANGE_PASSWORD 1820
#define ER_FK_NO_INDEX_CHILD 1821
#define ER_FK_NO_INDEX_PARENT 1822
#define ER_FK_FAIL_ADD_SYSTEM 1823
#define ER_FK_CANNOT_OPEN_PARENT 1824
#define ER_FK_INCORRECT_OPTION 1825
#define ER_FK_DUP_NAME 1826
#define ER_PASSWORD_FORMAT 1827
#define ER_FK_COLUMN_CANNOT_DROP 1828
#define ER_FK_COLUMN_CANNOT_DROP_CHILD 1829
#define ER_FK_COLUMN_NOT_NULL 1830
#define ER_DUP_INDEX 1831
#define ER_FK_COLUMN_CANNOT_CHANGE 1832
#define ER_FK_COLUMN_CANNOT_CHANGE_CHILD 1833
//#define OBSOLETE_ER_UNUSED5 1834
#define ER_MALFORMED_PACKET 1835
#define ER_READ_ONLY_MODE 1836
#define ER_GTID_NEXT_TYPE_UNDEFINED_GTID 1837
#define ER_VARIABLE_NOT_SETTABLE_IN_SP 1838
//#define OBSOLETE_ER_CANT_SET_GTID_PURGED_WHEN_GTID_MODE_IS_OFF 1839
#define ER_CANT_SET_GTID_PURGED_WHEN_GTID_EXECUTED_IS_NOT_EMPTY 1840
#define ER_CANT_SET_GTID_PURGED_WHEN_OWNED_GTIDS_IS_NOT_EMPTY 1841
#define ER_GTID_PURGED_WAS_CHANGED 1842
#define ER_GTID_EXECUTED_WAS_CHANGED 1843
#define ER_BINLOG_STMT_MODE_AND_NO_REPL_TABLES 1844
#define ER_ALTER_OPERATION_NOT_SUPPORTED 1845
#define ER_ALTER_OPERATION_NOT_SUPPORTED_REASON 1846
#define ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_COPY 1847
#define ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_PARTITION 1848
#define ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_FK_RENAME 1849
#define ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_COLUMN_TYPE 1850
#define ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_FK_CHECK 1851
//#define OBSOLETE_ER_UNUSED6 1852
#define ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_NOPK 1853
#define ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_AUTOINC 1854
#define ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_HIDDEN_FTS 1855
#define ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_CHANGE_FTS 1856
#define ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_FTS 1857
//#define OBSOLETE_ER_SQL_REPLICA_SKIP_COUNTER_NOT_SETTABLE_IN_GTID_MODE 1858
#define ER_DUP_UNKNOWN_IN_INDEX 1859
#define ER_IDENT_CAUSES_TOO_LONG_PATH 1860
#define ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_NOT_NULL 1861
#define ER_MUST_CHANGE_PASSWORD_LOGIN 1862
#define ER_ROW_IN_WRONG_PARTITION 1863
#define ER_MTA_EVENT_BIGGER_PENDING_JOBS_SIZE_MAX 1864
//#define OBSOLETE_ER_INNODB_NO_FT_USES_PARSER 1865
#define ER_BINLOG_LOGICAL_CORRUPTION 1866
#define ER_WARN_PURGE_LOG_IN_USE 1867
#define ER_WARN_PURGE_LOG_IS_ACTIVE 1868
#define ER_AUTO_INCREMENT_CONFLICT 1869
#define WARN_ON_BLOCKHOLE_IN_RBR 1870
#define ER_REPLICA_CM_INIT_REPOSITORY 1871
#define ER_REPLICA_AM_INIT_REPOSITORY 1872
#define ER_ACCESS_DENIED_CHANGE_USER_ERROR 1873
#define ER_INNODB_READ_ONLY 1874
#define ER_STOP_REPLICA_SQL_THREAD_TIMEOUT 1875
#define ER_STOP_REPLICA_IO_THREAD_TIMEOUT 1876
#define ER_TABLE_CORRUPT 1877
#define ER_TEMP_FILE_WRITE_FAILURE 1878
#define ER_INNODB_FT_AUX_NOT_HEX_ID 1879
#define ER_OLD_TEMPORALS_UPGRADED 1880
#define ER_INNODB_FORCED_RECOVERY 1881
#define ER_AES_INVALID_IV 1882
#define ER_PLUGIN_CANNOT_BE_UNINSTALLED 1883
#define ER_GTID_UNSAFE_BINLOG_SPLITTABLE_STATEMENT_AND_ASSIGNED_GTID 1884
#define ER_REPLICA_HAS_MORE_GTIDS_THAN_SOURCE 1885
#define ER_MISSING_KEY 1886
#define WARN_NAMED_PIPE_ACCESS_EVERYONE 1887
#define ER_FILE_CORRUPT 3000
#define ER_ERROR_ON_SOURCE 3001
//#define OBSOLETE_ER_INCONSISTENT_ERROR 3002
#define ER_STORAGE_ENGINE_NOT_LOADED 3003
#define ER_GET_STACKED_DA_WITHOUT_ACTIVE_HANDLER 3004
#define ER_WARN_LEGACY_SYNTAX_CONVERTED 3005
#define ER_BINLOG_UNSAFE_FULLTEXT_PLUGIN 3006
#define ER_CANNOT_DISCARD_TEMPORARY_TABLE 3007
#define ER_FK_DEPTH_EXCEEDED 3008
#define ER_COL_COUNT_DOESNT_MATCH_PLEASE_UPDATE_V2 3009
#define ER_WARN_TRIGGER_DOESNT_HAVE_CREATED 3010
#define ER_REFERENCED_TRG_DOES_NOT_EXIST 3011
#define ER_EXPLAIN_NOT_SUPPORTED 3012
#define ER_INVALID_FIELD_SIZE 3013
#define ER_MISSING_HA_CREATE_OPTION 3014
#define ER_ENGINE_OUT_OF_MEMORY 3015
#define ER_PASSWORD_EXPIRE_ANONYMOUS_USER 3016
#define ER_REPLICA_SQL_THREAD_MUST_STOP 3017
#define ER_NO_FT_MATERIALIZED_SUBQUERY 3018
#define ER_INNODB_UNDO_LOG_FULL 3019
#define ER_INVALID_ARGUMENT_FOR_LOGARITHM 3020
#define ER_REPLICA_CHANNEL_IO_THREAD_MUST_STOP 3021
#define ER_WARN_OPEN_TEMP_TABLES_MUST_BE_ZERO 3022
#define ER_WARN_ONLY_SOURCE_LOG_FILE_NO_POS 3023
#define ER_QUERY_TIMEOUT 3024
#define ER_NON_RO_SELECT_DISABLE_TIMER 3025
#define ER_DUP_LIST_ENTRY 3026
//#define OBSOLETE_ER_SQL_MODE_NO_EFFECT 3027
#define ER_AGGREGATE_ORDER_FOR_UNION 3028
#define ER_AGGREGATE_ORDER_NON_AGG_QUERY 3029
#define ER_REPLICA_WORKER_STOPPED_PREVIOUS_THD_ERROR 3030
#define ER_DONT_SUPPORT_REPLICA_PRESERVE_COMMIT_ORDER 3031
#define ER_SERVER_OFFLINE_MODE 3032
#define ER_GIS_DIFFERENT_SRIDS 3033
#define ER_GIS_UNSUPPORTED_ARGUMENT 3034
#define ER_GIS_UNKNOWN_ERROR 3035
#define ER_GIS_UNKNOWN_EXCEPTION 3036
#define ER_GIS_INVALID_DATA 3037
#define ER_BOOST_GEOMETRY_EMPTY_INPUT_EXCEPTION 3038
#define ER_BOOST_GEOMETRY_CENTROID_EXCEPTION 3039
#define ER_BOOST_GEOMETRY_OVERLAY_INVALID_INPUT_EXCEPTION 3040
#define ER_BOOST_GEOMETRY_TURN_INFO_EXCEPTION 3041
#define ER_BOOST_GEOMETRY_SELF_INTERSECTION_POINT_EXCEPTION 3042
#define ER_BOOST_GEOMETRY_UNKNOWN_EXCEPTION 3043
#define ER_STD_BAD_ALLOC_ERROR 3044
#define ER_STD_DOMAIN_ERROR 3045
#define ER_STD_LENGTH_ERROR 3046
#define ER_STD_INVALID_ARGUMENT 3047
#define ER_STD_OUT_OF_RANGE_ERROR 3048
#define ER_STD_OVERFLOW_ERROR 3049
#define ER_STD_RANGE_ERROR 3050
#define ER_STD_UNDERFLOW_ERROR 3051
#define ER_STD_LOGIC_ERROR 3052
#define ER_STD_RUNTIME_ERROR 3053
#define ER_STD_UNKNOWN_EXCEPTION 3054
#define ER_GIS_DATA_WRONG_ENDIANESS 3055
#define ER_CHANGE_SOURCE_PASSWORD_LENGTH 3056
#define ER_USER_LOCK_WRONG_NAME 3057
#define ER_USER_LOCK_DEADLOCK 3058
#define ER_REPLACE_INACCESSIBLE_ROWS 3059
#define ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_GIS 3060
#define ER_ILLEGAL_USER_VAR 3061
#define ER_GTID_MODE_OFF 3062
//#define OBSOLETE_ER_UNSUPPORTED_BY_REPLICATION_THREAD 3063
#define ER_INCORRECT_TYPE 3064
#define ER_FIELD_IN_ORDER_NOT_SELECT 3065
#define ER_AGGREGATE_IN_ORDER_NOT_SELECT 3066
#define ER_INVALID_RPL_WILD_TABLE_FILTER_PATTERN 3067
#define ER_NET_OK_PACKET_TOO_LARGE 3068
#define ER_INVALID_JSON_DATA 3069
#define ER_INVALID_GEOJSON_MISSING_MEMBER 3070
#define ER_INVALID_GEOJSON_WRONG_TYPE 3071
#define ER_INVALID_GEOJSON_UNSPECIFIED 3072
#define ER_DIMENSION_UNSUPPORTED 3073
#define ER_REPLICA_CHANNEL_DOES_NOT_EXIST 3074
//#define OBSOLETE_ER_SLAVE_MULTIPLE_CHANNELS_HOST_PORT 3075
#define ER_REPLICA_CHANNEL_NAME_INVALID_OR_TOO_LONG 3076
#define ER_REPLICA_NEW_CHANNEL_WRONG_REPOSITORY 3077
//#define OBSOLETE_ER_SLAVE_CHANNEL_DELETE 3078
#define ER_REPLICA_MULTIPLE_CHANNELS_CMD 3079
#define ER_REPLICA_MAX_CHANNELS_EXCEEDED 3080
#define ER_REPLICA_CHANNEL_MUST_STOP 3081
#define ER_REPLICA_CHANNEL_NOT_RUNNING 3082
#define ER_REPLICA_CHANNEL_WAS_RUNNING 3083
#define ER_REPLICA_CHANNEL_WAS_NOT_RUNNING 3084
#define ER_REPLICA_CHANNEL_SQL_THREAD_MUST_STOP 3085
#define ER_REPLICA_CHANNEL_SQL_SKIP_COUNTER 3086
#define ER_WRONG_FIELD_WITH_GROUP_V2 3087
#define ER_MIX_OF_GROUP_FUNC_AND_FIELDS_V2 3088
#define ER_WARN_DEPRECATED_SYSVAR_UPDATE 3089
#define ER_WARN_DEPRECATED_SQLMODE 3090
#define ER_CANNOT_LOG_PARTIAL_DROP_DATABASE_WITH_GTID 3091
#define ER_GROUP_REPLICATION_CONFIGURATION 3092
#define ER_GROUP_REPLICATION_RUNNING 3093
#define ER_GROUP_REPLICATION_APPLIER_INIT_ERROR 3094
#define ER_GROUP_REPLICATION_STOP_APPLIER_THREAD_TIMEOUT 3095
#define ER_GROUP_REPLICATION_COMMUNICATION_LAYER_SESSION_ERROR 3096
#define ER_GROUP_REPLICATION_COMMUNICATION_LAYER_JOIN_ERROR 3097
#define ER_BEFORE_DML_VALIDATION_ERROR 3098
#define ER_PREVENTS_VARIABLE_WITHOUT_RBR 3099
#define ER_RUN_HOOK_ERROR 3100
#define ER_TRANSACTION_ROLLBACK_DURING_COMMIT 3101
#define ER_GENERATED_COLUMN_FUNCTION_IS_NOT_ALLOWED 3102
#define ER_UNSUPPORTED_ALTER_INPLACE_ON_VIRTUAL_COLUMN 3103
#define ER_WRONG_FK_OPTION_FOR_GENERATED_COLUMN 3104
#define ER_NON_DEFAULT_VALUE_FOR_GENERATED_COLUMN 3105
#define ER_UNSUPPORTED_ACTION_ON_GENERATED_COLUMN 3106
#define ER_GENERATED_COLUMN_NON_PRIOR 3107
#define ER_DEPENDENT_BY_GENERATED_COLUMN 3108
#define ER_GENERATED_COLUMN_REF_AUTO_INC 3109
#define ER_FEATURE_NOT_AVAILABLE 3110
#define ER_CANT_SET_GTID_MODE 3111
#define ER_CANT_USE_AUTO_POSITION_WITH_GTID_MODE_OFF 3112
//#define OBSOLETE_ER_CANT_REPLICATE_ANONYMOUS_WITH_AUTO_POSITION 3113
//#define OBSOLETE_ER_CANT_REPLICATE_ANONYMOUS_WITH_GTID_MODE_ON 3114
//#define OBSOLETE_ER_CANT_REPLICATE_GTID_WITH_GTID_MODE_OFF 3115
#define ER_CANT_ENFORCE_GTID_CONSISTENCY_WITH_ONGOING_GTID_VIOLATING_TX 3116
#define ER_ENFORCE_GTID_CONSISTENCY_WARN_WITH_ONGOING_GTID_VIOLATING_TX 3117
#define ER_ACCOUNT_HAS_BEEN_LOCKED 3118
#define ER_WRONG_TABLESPACE_NAME 3119
#define ER_TABLESPACE_IS_NOT_EMPTY 3120
#define ER_WRONG_FILE_NAME 3121
#define ER_BOOST_GEOMETRY_INCONSISTENT_TURNS_EXCEPTION 3122
#define ER_WARN_OPTIMIZER_HINT_SYNTAX_ERROR 3123
#define ER_WARN_BAD_MAX_EXECUTION_TIME 3124
#define ER_WARN_UNSUPPORTED_MAX_EXECUTION_TIME 3125
#define ER_WARN_CONFLICTING_HINT 3126
#define ER_WARN_UNKNOWN_QB_NAME 3127
#define ER_UNRESOLVED_HINT_NAME 3128
#define ER_WARN_ON_MODIFYING_GTID_EXECUTED_TABLE 3129
#define ER_PLUGGABLE_PROTOCOL_COMMAND_NOT_SUPPORTED 3130
#define ER_LOCKING_SERVICE_WRONG_NAME 3131
#define ER_LOCKING_SERVICE_DEADLOCK 3132
#define ER_LOCKING_SERVICE_TIMEOUT 3133
#define ER_GIS_MAX_POINTS_IN_GEOMETRY_OVERFLOWED 3134
#define ER_SQL_MODE_MERGED 3135
#define ER_VTOKEN_PLUGIN_TOKEN_MISMATCH 3136
#define ER_VTOKEN_PLUGIN_TOKEN_NOT_FOUND 3137
#define ER_CANT_SET_VARIABLE_WHEN_OWNING_GTID 3138
#define ER_REPLICA_CHANNEL_OPERATION_NOT_ALLOWED 3139
#define ER_INVALID_JSON_TEXT 3140
#define ER_INVALID_JSON_TEXT_IN_PARAM 3141
#define ER_INVALID_JSON_BINARY_DATA 3142
#define ER_INVALID_JSON_PATH 3143
#define ER_INVALID_JSON_CHARSET 3144
#define ER_INVALID_JSON_CHARSET_IN_FUNCTION 3145
#define ER_INVALID_TYPE_FOR_JSON 3146
#define ER_INVALID_CAST_TO_JSON 3147
#define ER_INVALID_JSON_PATH_CHARSET 3148
#define ER_INVALID_JSON_PATH_WILDCARD 3149
#define ER_JSON_VALUE_TOO_BIG 3150
#define ER_JSON_KEY_TOO_BIG 3151
#define ER_JSON_USED_AS_KEY 3152
#define ER_JSON_VACUOUS_PATH 3153
#define ER_JSON_BAD_ONE_OR_ALL_ARG 3154
#define ER_NUMERIC_JSON_VALUE_OUT_OF_RANGE 3155
#define ER_INVALID_JSON_VALUE_FOR_CAST 3156
#define ER_JSON_DOCUMENT_TOO_DEEP 3157
#define ER_JSON_DOCUMENT_NULL_KEY 3158
#define ER_SECURE_TRANSPORT_REQUIRED 3159
#define ER_NO_SECURE_TRANSPORTS_CONFIGURED 3160
#define ER_DISABLED_STORAGE_ENGINE 3161
#define ER_USER_DOES_NOT_EXIST 3162
#define ER_USER_ALREADY_EXISTS 3163
#define ER_AUDIT_API_ABORT 3164
#define ER_INVALID_JSON_PATH_ARRAY_CELL 3165
#define ER_BUFPOOL_RESIZE_INPROGRESS 3166
#define ER_FEATURE_DISABLED_SEE_DOC 3167
#define ER_SERVER_ISNT_AVAILABLE 3168
#define ER_SESSION_WAS_KILLED 3169
#define ER_CAPACITY_EXCEEDED 3170
#define ER_CAPACITY_EXCEEDED_IN_RANGE_OPTIMIZER 3171
//#define OBSOLETE_ER_TABLE_NEEDS_UPG_PART 3172
#define ER_CANT_WAIT_FOR_EXECUTED_GTID_SET_WHILE_OWNING_A_GTID 3173
#define ER_CANNOT_ADD_FOREIGN_BASE_COL_VIRTUAL 3174
#define ER_CANNOT_CREATE_VIRTUAL_INDEX_CONSTRAINT 3175
#define ER_ERROR_ON_MODIFYING_GTID_EXECUTED_TABLE 3176
#define ER_LOCK_REFUSED_BY_ENGINE 3177
#define ER_UNSUPPORTED_ALTER_ONLINE_ON_VIRTUAL_COLUMN 3178
#define ER_MASTER_KEY_ROTATION_NOT_SUPPORTED_BY_SE 3179
//#define OBSOLETE_ER_MASTER_KEY_ROTATION_ERROR_BY_SE 3180
#define ER_MASTER_KEY_ROTATION_BINLOG_FAILED 3181
#define ER_MASTER_KEY_ROTATION_SE_UNAVAILABLE 3182
#define ER_TABLESPACE_CANNOT_ENCRYPT 3183
#define ER_INVALID_ENCRYPTION_OPTION 3184
#define ER_CANNOT_FIND_KEY_IN_KEYRING 3185
#define ER_CAPACITY_EXCEEDED_IN_PARSER 3186
#define ER_UNSUPPORTED_ALTER_ENCRYPTION_INPLACE 3187
#define ER_KEYRING_UDF_KEYRING_SERVICE_ERROR 3188
#define ER_USER_COLUMN_OLD_LENGTH 3189
#define ER_CANT_RESET_SOURCE 3190
#define ER_GROUP_REPLICATION_MAX_GROUP_SIZE 3191
#define ER_CANNOT_ADD_FOREIGN_BASE_COL_STORED 3192
#define ER_TABLE_REFERENCED 3193
//#define OBSOLETE_ER_PARTITION_ENGINE_DEPRECATED_FOR_TABLE 3194
//#define OBSOLETE_ER_WARN_USING_GEOMFROMWKB_TO_SET_SRID_ZERO 3195
//#define OBSOLETE_ER_WARN_USING_GEOMFROMWKB_TO_SET_SRID 3196
#define ER_XA_RETRY 3197
#define ER_KEYRING_AWS_UDF_AWS_KMS_ERROR 3198
#define ER_BINLOG_UNSAFE_XA 3199
#define ER_UDF_ERROR 3200
#define ER_KEYRING_MIGRATION_FAILURE 3201
#define ER_KEYRING_ACCESS_DENIED_ERROR 3202
#define ER_KEYRING_MIGRATION_STATUS 3203
//#define OBSOLETE_ER_PLUGIN_FAILED_TO_OPEN_TABLES 3204
//#define OBSOLETE_ER_PLUGIN_FAILED_TO_OPEN_TABLE 3205
//#define OBSOLETE_ER_AUDIT_LOG_NO_KEYRING_PLUGIN_INSTALLED 3206
//#define OBSOLETE_ER_AUDIT_LOG_ENCRYPTION_PASSWORD_HAS_NOT_BEEN_SET 3207
//#define OBSOLETE_ER_AUDIT_LOG_COULD_NOT_CREATE_AES_KEY 3208
//#define OBSOLETE_ER_AUDIT_LOG_ENCRYPTION_PASSWORD_CANNOT_BE_FETCHED 3209
//#define OBSOLETE_ER_AUDIT_LOG_JSON_FILTERING_NOT_ENABLED 3210
//#define OBSOLETE_ER_AUDIT_LOG_UDF_INSUFFICIENT_PRIVILEGE 3211
//#define OBSOLETE_ER_AUDIT_LOG_SUPER_PRIVILEGE_REQUIRED 3212
//#define OBSOLETE_ER_COULD_NOT_REINITIALIZE_AUDIT_LOG_FILTERS 3213
//#define OBSOLETE_ER_AUDIT_LOG_UDF_INVALID_ARGUMENT_TYPE 3214
//#define OBSOLETE_ER_AUDIT_LOG_UDF_INVALID_ARGUMENT_COUNT 3215
//#define OBSOLETE_ER_AUDIT_LOG_HAS_NOT_BEEN_INSTALLED 3216
//#define OBSOLETE_ER_AUDIT_LOG_UDF_READ_INVALID_MAX_ARRAY_LENGTH_ARG_TYPE 3217
#define ER_AUDIT_LOG_UDF_READ_INVALID_MAX_ARRAY_LENGTH_ARG_VALUE 3218
//#define OBSOLETE_ER_AUDIT_LOG_JSON_FILTER_PARSING_ERROR 3219
//#define OBSOLETE_ER_AUDIT_LOG_JSON_FILTER_NAME_CANNOT_BE_EMPTY 3220
//#define OBSOLETE_ER_AUDIT_LOG_JSON_USER_NAME_CANNOT_BE_EMPTY 3221
//#define OBSOLETE_ER_AUDIT_LOG_JSON_FILTER_DOES_NOT_EXISTS 3222
//#define OBSOLETE_ER_AUDIT_LOG_USER_FIRST_CHARACTER_MUST_BE_ALPHANUMERIC 3223
//#define OBSOLETE_ER_AUDIT_LOG_USER_NAME_INVALID_CHARACTER 3224
//#define OBSOLETE_ER_AUDIT_LOG_HOST_NAME_INVALID_CHARACTER 3225
#define OBSOLETE_WARN_DEPRECATED_MAXDB_SQL_MODE_FOR_TIMESTAMP 3226
//#define OBSOLETE_ER_XA_REPLICATION_FILTERS 3227
//#define OBSOLETE_ER_CANT_OPEN_ERROR_LOG 3228
//#define OBSOLETE_ER_GROUPING_ON_TIMESTAMP_IN_DST 3229
//#define OBSOLETE_ER_CANT_START_SERVER_NAMED_PIPE 3230
#define ER_WRITE_SET_EXCEEDS_LIMIT 3231
//#define OBSOLETE_ER_DEPRECATED_TLS_VERSION_SESSION_57 3232
//#define OBSOLETE_ER_WARN_DEPRECATED_TLS_VERSION_57 3233
//#define OBSOLETE_ER_WARN_WRONG_NATIVE_TABLE_STRUCTURE 3234
#define ER_AES_INVALID_KDF_NAME 3235
#define ER_AES_INVALID_KDF_ITERATIONS 3236
#define WARN_AES_KEY_SIZE 3237
#define ER_AES_INVALID_KDF_OPTION_SIZE 3238
#define ER_UNSUPPORT_COMPRESSED_TEMPORARY_TABLE 3500
#define ER_ACL_OPERATION_FAILED 3501
#define ER_UNSUPPORTED_INDEX_ALGORITHM 3502
#define ER_NO_SUCH_DB 3503
#define ER_TOO_BIG_ENUM 3504
#define ER_TOO_LONG_SET_ENUM_VALUE 3505
#define ER_INVALID_DD_OBJECT 3506
#define ER_UPDATING_DD_TABLE 3507
#define ER_INVALID_DD_OBJECT_ID 3508
#define ER_INVALID_DD_OBJECT_NAME 3509
#define ER_TABLESPACE_MISSING_WITH_NAME 3510
#define ER_TOO_LONG_ROUTINE_COMMENT 3511
#define ER_SP_LOAD_FAILED 3512
#define ER_INVALID_BITWISE_OPERANDS_SIZE 3513
#define ER_INVALID_BITWISE_AGGREGATE_OPERANDS_SIZE 3514
#define ER_WARN_UNSUPPORTED_HINT 3515
#define ER_UNEXPECTED_GEOMETRY_TYPE 3516
#define ER_SRS_PARSE_ERROR 3517
#define ER_SRS_PROJ_PARAMETER_MISSING 3518
#define ER_WARN_SRS_NOT_FOUND 3519
#define ER_SRS_NOT_CARTESIAN 3520
#define ER_SRS_NOT_CARTESIAN_UNDEFINED 3521
#define ER_PK_INDEX_CANT_BE_INVISIBLE 3522
#define ER_UNKNOWN_AUTHID 3523
#define ER_FAILED_ROLE_GRANT 3524
#define ER_OPEN_ROLE_TABLES 3525
#define ER_FAILED_DEFAULT_ROLES 3526
#define ER_COMPONENTS_NO_SCHEME 3527
#define ER_COMPONENTS_NO_SCHEME_SERVICE 3528
#define ER_COMPONENTS_CANT_LOAD 3529
#define ER_ROLE_NOT_GRANTED 3530
#define ER_FAILED_REVOKE_ROLE 3531
#define ER_RENAME_ROLE 3532
#define ER_COMPONENTS_CANT_ACQUIRE_SERVICE_IMPLEMENTATION 3533
#define ER_COMPONENTS_CANT_SATISFY_DEPENDENCY 3534
#define ER_COMPONENTS_LOAD_CANT_REGISTER_SERVICE_IMPLEMENTATION 3535
#define ER_COMPONENTS_LOAD_CANT_INITIALIZE 3536
#define ER_COMPONENTS_UNLOAD_NOT_LOADED 3537
#define ER_COMPONENTS_UNLOAD_CANT_DEINITIALIZE 3538
#define ER_COMPONENTS_CANT_RELEASE_SERVICE 3539
#define ER_COMPONENTS_UNLOAD_CANT_UNREGISTER_SERVICE 3540
#define ER_COMPONENTS_CANT_UNLOAD 3541
#define ER_WARN_UNLOAD_THE_NOT_PERSISTED 3542
#define ER_COMPONENT_TABLE_INCORRECT 3543
#define ER_COMPONENT_MANIPULATE_ROW_FAILED 3544
#define ER_COMPONENTS_UNLOAD_DUPLICATE_IN_GROUP 3545
#define ER_CANT_SET_GTID_PURGED_DUE_SETS_CONSTRAINTS 3546
#define ER_CANNOT_LOCK_USER_MANAGEMENT_CACHES 3547
#define ER_SRS_NOT_FOUND 3548
#define ER_VARIABLE_NOT_PERSISTED 3549
#define ER_IS_QUERY_INVALID_CLAUSE 3550
#define ER_UNABLE_TO_STORE_STATISTICS 3551
#define ER_NO_SYSTEM_SCHEMA_ACCESS 3552
#define ER_NO_SYSTEM_TABLESPACE_ACCESS 3553
#define ER_NO_SYSTEM_TABLE_ACCESS 3554
#define ER_NO_SYSTEM_TABLE_ACCESS_FOR_DICTIONARY_TABLE 3555
#define ER_NO_SYSTEM_TABLE_ACCESS_FOR_SYSTEM_TABLE 3556
#define ER_NO_SYSTEM_TABLE_ACCESS_FOR_TABLE 3557
#define ER_INVALID_OPTION_KEY 3558
#define ER_INVALID_OPTION_VALUE 3559
#define ER_INVALID_OPTION_KEY_VALUE_PAIR 3560
#define ER_INVALID_OPTION_START_CHARACTER 3561
#define ER_INVALID_OPTION_END_CHARACTER 3562
#define ER_INVALID_OPTION_CHARACTERS 3563
#define ER_DUPLICATE_OPTION_KEY 3564
#define ER_WARN_SRS_NOT_FOUND_AXIS_ORDER 3565
#define ER_NO_ACCESS_TO_NATIVE_FCT 3566
#define ER_RESET_SOURCE_TO_VALUE_OUT_OF_RANGE 3567
#define ER_UNRESOLVED_TABLE_LOCK 3568
#define ER_DUPLICATE_TABLE_LOCK 3569
#define ER_BINLOG_UNSAFE_SKIP_LOCKED 3570
#define ER_BINLOG_UNSAFE_NOWAIT 3571
#define ER_LOCK_NOWAIT 3572
#define ER_CTE_RECURSIVE_REQUIRES_UNION 3573
#define ER_CTE_RECURSIVE_REQUIRES_NONRECURSIVE_FIRST 3574
#define ER_CTE_RECURSIVE_FORBIDS_AGGREGATION 3575
#define ER_CTE_RECURSIVE_FORBIDDEN_JOIN_ORDER 3576
#define ER_CTE_RECURSIVE_REQUIRES_SINGLE_REFERENCE 3577
#define ER_SWITCH_TMP_ENGINE 3578
#define ER_WINDOW_NO_SUCH_WINDOW 3579
#define ER_WINDOW_CIRCULARITY_IN_WINDOW_GRAPH 3580
#define ER_WINDOW_NO_CHILD_PARTITIONING 3581
#define ER_WINDOW_NO_INHERIT_FRAME 3582
#define ER_WINDOW_NO_REDEFINE_ORDER_BY 3583
#define ER_WINDOW_FRAME_START_ILLEGAL 3584
#define ER_WINDOW_FRAME_END_ILLEGAL 3585
#define ER_WINDOW_FRAME_ILLEGAL 3586
#define ER_WINDOW_RANGE_FRAME_ORDER_TYPE 3587
#define ER_WINDOW_RANGE_FRAME_TEMPORAL_TYPE 3588
#define ER_WINDOW_RANGE_FRAME_NUMERIC_TYPE 3589
#define ER_WINDOW_RANGE_BOUND_NOT_CONSTANT 3590
#define ER_WINDOW_DUPLICATE_NAME 3591
#define ER_WINDOW_ILLEGAL_ORDER_BY 3592
#define ER_WINDOW_INVALID_WINDOW_FUNC_USE 3593
#define ER_WINDOW_INVALID_WINDOW_FUNC_ALIAS_USE 3594
#define ER_WINDOW_NESTED_WINDOW_FUNC_USE_IN_WINDOW_SPEC 3595
#define ER_WINDOW_ROWS_INTERVAL_USE 3596
#define ER_WINDOW_NO_GROUP_ORDER_UNUSED 3597
#define ER_WINDOW_EXPLAIN_JSON 3598
#define ER_WINDOW_FUNCTION_IGNORES_FRAME 3599
#define ER_WL9236_NOW_UNUSED 3600
#define ER_INVALID_NO_OF_ARGS 3601
#define ER_FIELD_IN_GROUPING_NOT_GROUP_BY 3602
#define ER_TOO_LONG_TABLESPACE_COMMENT 3603
#define ER_ENGINE_CANT_DROP_TABLE 3604
#define ER_ENGINE_CANT_DROP_MISSING_TABLE 3605
#define ER_TABLESPACE_DUP_FILENAME 3606
#define ER_DB_DROP_RMDIR2 3607
#define ER_IMP_NO_FILES_MATCHED 3608
#define ER_IMP_SCHEMA_DOES_NOT_EXIST 3609
#define ER_IMP_TABLE_ALREADY_EXISTS 3610
#define ER_IMP_INCOMPATIBLE_MYSQLD_VERSION 3611
#define ER_IMP_INCOMPATIBLE_DD_VERSION 3612
#define ER_IMP_INCOMPATIBLE_SDI_VERSION 3613
#define ER_WARN_INVALID_HINT 3614
#define ER_VAR_DOES_NOT_EXIST 3615
#define ER_LONGITUDE_OUT_OF_RANGE 3616
#define ER_LATITUDE_OUT_OF_RANGE 3617
#define ER_NOT_IMPLEMENTED_FOR_GEOGRAPHIC_SRS 3618
#define ER_ILLEGAL_PRIVILEGE_LEVEL 3619
#define ER_NO_SYSTEM_VIEW_ACCESS 3620
#define ER_COMPONENT_FILTER_FLABBERGASTED 3621
#define ER_PART_EXPR_TOO_LONG 3622
#define ER_UDF_DROP_DYNAMICALLY_REGISTERED 3623
#define ER_UNABLE_TO_STORE_COLUMN_STATISTICS 3624
#define ER_UNABLE_TO_UPDATE_COLUMN_STATISTICS 3625
#define ER_UNABLE_TO_DROP_COLUMN_STATISTICS 3626
#define ER_UNABLE_TO_BUILD_HISTOGRAM 3627
#define ER_MANDATORY_ROLE 3628
#define ER_MISSING_TABLESPACE_FILE 3629
#define ER_PERSIST_ONLY_ACCESS_DENIED_ERROR 3630
#define ER_CMD_NEED_SUPER 3631
#define ER_PATH_IN_DATADIR 3632
#define ER_CLONE_DDL_IN_PROGRESS 3633
#define ER_CLONE_TOO_MANY_CONCURRENT_CLONES 3634
#define ER_APPLIER_LOG_EVENT_VALIDATION_ERROR 3635
#define ER_CTE_MAX_RECURSION_DEPTH 3636
#define ER_NOT_HINT_UPDATABLE_VARIABLE 3637
#define ER_CREDENTIALS_CONTRADICT_TO_HISTORY 3638
#define ER_WARNING_PASSWORD_HISTORY_CLAUSES_VOID 3639
#define ER_CLIENT_DOES_NOT_SUPPORT 3640
#define ER_I_S_SKIPPED_TABLESPACE 3641
#define ER_TABLESPACE_ENGINE_MISMATCH 3642
#define ER_WRONG_SRID_FOR_COLUMN 3643
#define ER_CANNOT_ALTER_SRID_DUE_TO_INDEX 3644
#define ER_WARN_BINLOG_PARTIAL_UPDATES_DISABLED 3645
//#define OBSOLETE_ER_WARN_BINLOG_V1_ROW_EVENTS_DISABLED 3646
#define ER_WARN_BINLOG_PARTIAL_UPDATES_SUGGESTS_PARTIAL_IMAGES 3647
#define ER_COULD_NOT_APPLY_JSON_DIFF 3648
#define ER_CORRUPTED_JSON_DIFF 3649
#define ER_RESOURCE_GROUP_EXISTS 3650
#define ER_RESOURCE_GROUP_NOT_EXISTS 3651
#define ER_INVALID_VCPU_ID 3652
#define ER_INVALID_VCPU_RANGE 3653
#define ER_INVALID_THREAD_PRIORITY 3654
#define ER_DISALLOWED_OPERATION 3655
#define ER_RESOURCE_GROUP_BUSY 3656
#define ER_RESOURCE_GROUP_DISABLED 3657
#define ER_FEATURE_UNSUPPORTED 3658
#define ER_ATTRIBUTE_IGNORED 3659
#define ER_INVALID_THREAD_ID 3660
#define ER_RESOURCE_GROUP_BIND_FAILED 3661
#define ER_INVALID_USE_OF_FORCE_OPTION 3662
#define ER_GROUP_REPLICATION_COMMAND_FAILURE 3663
#define ER_SDI_OPERATION_FAILED 3664
#define ER_MISSING_JSON_TABLE_VALUE 3665
#define ER_WRONG_JSON_TABLE_VALUE 3666
#define ER_TF_MUST_HAVE_ALIAS 3667
#define ER_TF_FORBIDDEN_JOIN_TYPE 3668
#define ER_JT_VALUE_OUT_OF_RANGE 3669
#define ER_JT_MAX_NESTED_PATH 3670
#define ER_PASSWORD_EXPIRATION_NOT_SUPPORTED_BY_AUTH_METHOD 3671
#define ER_INVALID_GEOJSON_CRS_NOT_TOP_LEVEL 3672
#define ER_BAD_NULL_ERROR_NOT_IGNORED 3673
#define WARN_USELESS_SPATIAL_INDEX 3674
#define ER_DISK_FULL_NOWAIT 3675
#define ER_PARSE_ERROR_IN_DIGEST_FN 3676
#define ER_UNDISCLOSED_PARSE_ERROR_IN_DIGEST_FN 3677
#define ER_SCHEMA_DIR_EXISTS 3678
#define ER_SCHEMA_DIR_MISSING 3679
#define ER_SCHEMA_DIR_CREATE_FAILED 3680
#define ER_SCHEMA_DIR_UNKNOWN 3681
#define ER_ONLY_IMPLEMENTED_FOR_SRID_0_AND_4326 3682
//#define OBSOLETE_ER_BINLOG_EXPIRE_LOG_DAYS_AND_SECS_USED_TOGETHER 3683
#define ER_REGEXP_BUFFER_OVERFLOW 3684
#define ER_REGEXP_ILLEGAL_ARGUMENT 3685
#define ER_REGEXP_INDEX_OUTOFBOUNDS_ERROR 3686
#define ER_REGEXP_INTERNAL_ERROR 3687
#define ER_REGEXP_RULE_SYNTAX 3688
#define ER_REGEXP_BAD_ESCAPE_SEQUENCE 3689
#define ER_REGEXP_UNIMPLEMENTED 3690
#define ER_REGEXP_MISMATCHED_PAREN 3691
#define ER_REGEXP_BAD_INTERVAL 3692
#define ER_REGEXP_MAX_LT_MIN 3693
#define ER_REGEXP_INVALID_BACK_REF 3694
#define ER_REGEXP_LOOK_BEHIND_LIMIT 3695
#define ER_REGEXP_MISSING_CLOSE_BRACKET 3696
#define ER_REGEXP_INVALID_RANGE 3697
#define ER_REGEXP_STACK_OVERFLOW 3698
#define ER_REGEXP_TIME_OUT 3699
#define ER_REGEXP_PATTERN_TOO_BIG 3700
#define ER_CANT_SET_ERROR_LOG_SERVICE 3701
#define ER_EMPTY_PIPELINE_FOR_ERROR_LOG_SERVICE 3702
#define ER_COMPONENT_FILTER_DIAGNOSTICS 3703
#define ER_NOT_IMPLEMENTED_FOR_CARTESIAN_SRS 3704
#define ER_NOT_IMPLEMENTED_FOR_PROJECTED_SRS 3705
#define ER_NONPOSITIVE_RADIUS 3706
#define ER_RESTART_SERVER_FAILED 3707
#define ER_SRS_MISSING_MANDATORY_ATTRIBUTE 3708
#define ER_SRS_MULTIPLE_ATTRIBUTE_DEFINITIONS 3709
#define ER_SRS_NAME_CANT_BE_EMPTY_OR_WHITESPACE 3710
#define ER_SRS_ORGANIZATION_CANT_BE_EMPTY_OR_WHITESPACE 3711
#define ER_SRS_ID_ALREADY_EXISTS 3712
#define ER_WARN_SRS_ID_ALREADY_EXISTS 3713
#define ER_CANT_MODIFY_SRID_0 3714
#define ER_WARN_RESERVED_SRID_RANGE 3715
#define ER_CANT_MODIFY_SRS_USED_BY_COLUMN 3716
#define ER_SRS_INVALID_CHARACTER_IN_ATTRIBUTE 3717
#define ER_SRS_ATTRIBUTE_STRING_TOO_LONG 3718
#define ER_DEPRECATED_UTF8_ALIAS 3719
#define ER_DEPRECATED_NATIONAL 3720
#define ER_INVALID_DEFAULT_UTF8MB4_COLLATION 3721
#define ER_UNABLE_TO_COLLECT_LOG_STATUS 3722
#define ER_RESERVED_TABLESPACE_NAME 3723
#define ER_UNABLE_TO_SET_OPTION 3724
#define ER_REPLICA_POSSIBLY_DIVERGED_AFTER_DDL 3725
#define ER_SRS_NOT_GEOGRAPHIC 3726
#define ER_POLYGON_TOO_LARGE 3727
#define ER_SPATIAL_UNIQUE_INDEX 3728
#define ER_INDEX_TYPE_NOT_SUPPORTED_FOR_SPATIAL_INDEX 3729
#define ER_FK_CANNOT_DROP_PARENT 3730
#define ER_GEOMETRY_PARAM_LONGITUDE_OUT_OF_RANGE 3731
#define ER_GEOMETRY_PARAM_LATITUDE_OUT_OF_RANGE 3732
#define ER_FK_CANNOT_USE_VIRTUAL_COLUMN 3733
#define ER_FK_NO_COLUMN_PARENT 3734
#define ER_CANT_SET_ERROR_SUPPRESSION_LIST 3735
#define ER_SRS_GEOGCS_INVALID_AXES 3736
#define ER_SRS_INVALID_SEMI_MAJOR_AXIS 3737
#define ER_SRS_INVALID_INVERSE_FLATTENING 3738
#define ER_SRS_INVALID_ANGULAR_UNIT 3739
#define ER_SRS_INVALID_PRIME_MERIDIAN 3740
#define ER_TRANSFORM_SOURCE_SRS_NOT_SUPPORTED 3741
#define ER_TRANSFORM_TARGET_SRS_NOT_SUPPORTED 3742
#define ER_TRANSFORM_SOURCE_SRS_MISSING_TOWGS84 3743
#define ER_TRANSFORM_TARGET_SRS_MISSING_TOWGS84 3744
#define ER_TEMP_TABLE_PREVENTS_SWITCH_SESSION_BINLOG_FORMAT 3745
#define ER_TEMP_TABLE_PREVENTS_SWITCH_GLOBAL_BINLOG_FORMAT 3746
#define ER_RUNNING_APPLIER_PREVENTS_SWITCH_GLOBAL_BINLOG_FORMAT 3747
#define ER_CLIENT_GTID_UNSAFE_CREATE_DROP_TEMP_TABLE_IN_TRX_IN_SBR 3748
//#define OBSOLETE_ER_XA_CANT_CREATE_MDL_BACKUP 3749
#define ER_TABLE_WITHOUT_PK 3750
#define ER_WARN_DATA_TRUNCATED_FUNCTIONAL_INDEX 3751
#define ER_WARN_DATA_OUT_OF_RANGE_FUNCTIONAL_INDEX 3752
#define ER_FUNCTIONAL_INDEX_ON_JSON_OR_GEOMETRY_FUNCTION 3753
#define ER_FUNCTIONAL_INDEX_REF_AUTO_INCREMENT 3754
#define ER_CANNOT_DROP_COLUMN_FUNCTIONAL_INDEX 3755
#define ER_FUNCTIONAL_INDEX_PRIMARY_KEY 3756
#define ER_FUNCTIONAL_INDEX_ON_LOB 3757
#define ER_FUNCTIONAL_INDEX_FUNCTION_IS_NOT_ALLOWED 3758
#define ER_FULLTEXT_FUNCTIONAL_INDEX 3759
#define ER_SPATIAL_FUNCTIONAL_INDEX 3760
#define ER_WRONG_KEY_COLUMN_FUNCTIONAL_INDEX 3761
#define ER_FUNCTIONAL_INDEX_ON_FIELD 3762
#define ER_GENERATED_COLUMN_NAMED_FUNCTION_IS_NOT_ALLOWED 3763
#define ER_GENERATED_COLUMN_ROW_VALUE 3764
#define ER_GENERATED_COLUMN_VARIABLES 3765
#define ER_DEPENDENT_BY_DEFAULT_GENERATED_VALUE 3766
#define ER_DEFAULT_VAL_GENERATED_NON_PRIOR 3767
#define ER_DEFAULT_VAL_GENERATED_REF_AUTO_INC 3768
#define ER_DEFAULT_VAL_GENERATED_FUNCTION_IS_NOT_ALLOWED 3769
#define ER_DEFAULT_VAL_GENERATED_NAMED_FUNCTION_IS_NOT_ALLOWED 3770
#define ER_DEFAULT_VAL_GENERATED_ROW_VALUE 3771
#define ER_DEFAULT_VAL_GENERATED_VARIABLES 3772
#define ER_DEFAULT_AS_VAL_GENERATED 3773
#define ER_UNSUPPORTED_ACTION_ON_DEFAULT_VAL_GENERATED 3774
#define ER_GTID_UNSAFE_ALTER_ADD_COL_WITH_DEFAULT_EXPRESSION 3775
#define ER_FK_CANNOT_CHANGE_ENGINE 3776
#define ER_WARN_DEPRECATED_USER_SET_EXPR 3777
#define ER_WARN_DEPRECATED_UTF8MB3_COLLATION 3778
#define ER_WARN_DEPRECATED_NESTED_COMMENT_SYNTAX 3779
#define ER_FK_INCOMPATIBLE_COLUMNS 3780
#define ER_GR_HOLD_WAIT_TIMEOUT 3781
#define ER_GR_HOLD_KILLED 3782
#define ER_GR_HOLD_MEMBER_STATUS_ERROR 3783
#define ER_RPL_ENCRYPTION_FAILED_TO_FETCH_KEY 3784
#define ER_RPL_ENCRYPTION_KEY_NOT_FOUND 3785
#define ER_RPL_ENCRYPTION_KEYRING_INVALID_KEY 3786
#define ER_RPL_ENCRYPTION_HEADER_ERROR 3787
#define ER_RPL_ENCRYPTION_FAILED_TO_ROTATE_LOGS 3788
#define ER_RPL_ENCRYPTION_KEY_EXISTS_UNEXPECTED 3789
#define ER_RPL_ENCRYPTION_FAILED_TO_GENERATE_KEY 3790
#define ER_RPL_ENCRYPTION_FAILED_TO_STORE_KEY 3791
#define ER_RPL_ENCRYPTION_FAILED_TO_REMOVE_KEY 3792
#define ER_RPL_ENCRYPTION_UNABLE_TO_CHANGE_OPTION 3793
#define ER_RPL_ENCRYPTION_MASTER_KEY_RECOVERY_FAILED 3794
#define ER_SLOW_LOG_MODE_IGNORED_WHEN_NOT_LOGGING_TO_FILE 3795
#define ER_GRP_TRX_CONSISTENCY_NOT_ALLOWED 3796
#define ER_GRP_TRX_CONSISTENCY_BEFORE 3797
#define ER_GRP_TRX_CONSISTENCY_AFTER_ON_TRX_BEGIN 3798
#define ER_GRP_TRX_CONSISTENCY_BEGIN_NOT_ALLOWED 3799
#define ER_FUNCTIONAL_INDEX_ROW_VALUE_IS_NOT_ALLOWED 3800
#define ER_RPL_ENCRYPTION_FAILED_TO_ENCRYPT 3801
#define ER_PAGE_TRACKING_NOT_STARTED 3802
#define ER_PAGE_TRACKING_RANGE_NOT_TRACKED 3803
#define ER_PAGE_TRACKING_CANNOT_PURGE 3804
#define ER_RPL_ENCRYPTION_CANNOT_ROTATE_BINLOG_MASTER_KEY 3805
#define ER_BINLOG_MASTER_KEY_RECOVERY_OUT_OF_COMBINATION 3806
#define ER_BINLOG_MASTER_KEY_ROTATION_FAIL_TO_OPERATE_KEY 3807
#define ER_BINLOG_MASTER_KEY_ROTATION_FAIL_TO_ROTATE_LOGS 3808
#define ER_BINLOG_MASTER_KEY_ROTATION_FAIL_TO_REENCRYPT_LOG 3809
#define ER_BINLOG_MASTER_KEY_ROTATION_FAIL_TO_CLEANUP_UNUSED_KEYS 3810
#define ER_BINLOG_MASTER_KEY_ROTATION_FAIL_TO_CLEANUP_AUX_KEY 3811
#define ER_NON_BOOLEAN_EXPR_FOR_CHECK_CONSTRAINT 3812
#define ER_COLUMN_CHECK_CONSTRAINT_REFERENCES_OTHER_COLUMN 3813
#define ER_CHECK_CONSTRAINT_NAMED_FUNCTION_IS_NOT_ALLOWED 3814
#define ER_CHECK_CONSTRAINT_FUNCTION_IS_NOT_ALLOWED 3815
#define ER_CHECK_CONSTRAINT_VARIABLES 3816
#define ER_CHECK_CONSTRAINT_ROW_VALUE 3817
#define ER_CHECK_CONSTRAINT_REFERS_AUTO_INCREMENT_COLUMN 3818
#define ER_CHECK_CONSTRAINT_VIOLATED 3819
#define ER_CHECK_CONSTRAINT_REFERS_UNKNOWN_COLUMN 3820
#define ER_CHECK_CONSTRAINT_NOT_FOUND 3821
#define ER_CHECK_CONSTRAINT_DUP_NAME 3822
#define ER_CHECK_CONSTRAINT_CLAUSE_USING_FK_REFER_ACTION_COLUMN 3823
#define WARN_UNENCRYPTED_TABLE_IN_ENCRYPTED_DB 3824
#define ER_INVALID_ENCRYPTION_REQUEST 3825
#define ER_CANNOT_SET_TABLE_ENCRYPTION 3826
#define ER_CANNOT_SET_DATABASE_ENCRYPTION 3827
#define ER_CANNOT_SET_TABLESPACE_ENCRYPTION 3828
#define ER_TABLESPACE_CANNOT_BE_ENCRYPTED 3829
#define ER_TABLESPACE_CANNOT_BE_DECRYPTED 3830
#define ER_TABLESPACE_TYPE_UNKNOWN 3831
#define ER_TARGET_TABLESPACE_UNENCRYPTED 3832
#define ER_CANNOT_USE_ENCRYPTION_CLAUSE 3833
#define ER_INVALID_MULTIPLE_CLAUSES 3834
#define ER_UNSUPPORTED_USE_OF_GRANT_AS 3835
#define ER_UKNOWN_AUTH_ID_OR_ACCESS_DENIED_FOR_GRANT_AS 3836
#define ER_DEPENDENT_BY_FUNCTIONAL_INDEX 3837
#define ER_PLUGIN_NOT_EARLY 3838
#define ER_INNODB_REDO_LOG_ARCHIVE_START_SUBDIR_PATH 3839
#define ER_INNODB_REDO_LOG_ARCHIVE_START_TIMEOUT 3840
#define ER_INNODB_REDO_LOG_ARCHIVE_DIRS_INVALID 3841
#define ER_INNODB_REDO_LOG_ARCHIVE_LABEL_NOT_FOUND 3842
#define ER_INNODB_REDO_LOG_ARCHIVE_DIR_EMPTY 3843
#define ER_INNODB_REDO_LOG_ARCHIVE_NO_SUCH_DIR 3844
#define ER_INNODB_REDO_LOG_ARCHIVE_DIR_CLASH 3845
#define ER_INNODB_REDO_LOG_ARCHIVE_DIR_PERMISSIONS 3846
#define ER_INNODB_REDO_LOG_ARCHIVE_FILE_CREATE 3847
#define ER_INNODB_REDO_LOG_ARCHIVE_ACTIVE 3848
#define ER_INNODB_REDO_LOG_ARCHIVE_INACTIVE 3849
#define ER_INNODB_REDO_LOG_ARCHIVE_FAILED 3850
#define ER_INNODB_REDO_LOG_ARCHIVE_SESSION 3851
#define ER_STD_REGEX_ERROR 3852
#define ER_INVALID_JSON_TYPE 3853
#define ER_CANNOT_CONVERT_STRING 3854
#define ER_DEPENDENT_BY_PARTITION_FUNC 3855
#define ER_WARN_DEPRECATED_FLOAT_AUTO_INCREMENT 3856
#define ER_RPL_CANT_STOP_REPLICA_WHILE_LOCKED_BACKUP 3857
#define ER_WARN_DEPRECATED_FLOAT_DIGITS 3858
#define ER_WARN_DEPRECATED_FLOAT_UNSIGNED 3859
#define ER_WARN_DEPRECATED_INTEGER_DISPLAY_WIDTH 3860
#define ER_WARN_DEPRECATED_ZEROFILL 3861
#define ER_CLONE_DONOR 3862
#define ER_CLONE_PROTOCOL 3863
#define ER_CLONE_DONOR_VERSION 3864
#define ER_CLONE_OS 3865
#define ER_CLONE_PLATFORM 3866
#define ER_CLONE_CHARSET 3867
#define ER_CLONE_CONFIG 3868
#define ER_CLONE_SYS_CONFIG 3869
#define ER_CLONE_PLUGIN_MATCH 3870
#define ER_CLONE_LOOPBACK 3871
#define ER_CLONE_ENCRYPTION 3872
#define ER_CLONE_DISK_SPACE 3873
#define ER_CLONE_IN_PROGRESS 3874
#define ER_CLONE_DISALLOWED 3875
#define ER_CANNOT_GRANT_ROLES_TO_ANONYMOUS_USER 3876
#define ER_SECONDARY_ENGINE_PLUGIN 3877
#define ER_SECOND_PASSWORD_CANNOT_BE_EMPTY 3878
#define ER_DB_ACCESS_DENIED 3879
#define ER_DA_AUTH_ID_WITH_SYSTEM_USER_PRIV_IN_MANDATORY_ROLES 3880
#define ER_DA_RPL_GTID_TABLE_CANNOT_OPEN 3881
#define ER_GEOMETRY_IN_UNKNOWN_LENGTH_UNIT 3882
#define ER_DA_PLUGIN_INSTALL_ERROR 3883
#define ER_NO_SESSION_TEMP 3884
#define ER_DA_UNKNOWN_ERROR_NUMBER 3885
#define ER_COLUMN_CHANGE_SIZE 3886
#define ER_REGEXP_INVALID_CAPTURE_GROUP_NAME 3887
#define ER_DA_SSL_LIBRARY_ERROR 3888
#define ER_SECONDARY_ENGINE 3889
#define ER_SECONDARY_ENGINE_DDL 3890
#define ER_INCORRECT_CURRENT_PASSWORD 3891
#define ER_MISSING_CURRENT_PASSWORD 3892
#define ER_CURRENT_PASSWORD_NOT_REQUIRED 3893
#define ER_PASSWORD_CANNOT_BE_RETAINED_ON_PLUGIN_CHANGE 3894
#define ER_CURRENT_PASSWORD_CANNOT_BE_RETAINED 3895
#define ER_PARTIAL_REVOKES_EXIST 3896
#define ER_CANNOT_GRANT_SYSTEM_PRIV_TO_MANDATORY_ROLE 3897
#define ER_XA_REPLICATION_FILTERS 3898
#define ER_UNSUPPORTED_SQL_MODE 3899
#define ER_REGEXP_INVALID_FLAG 3900
#define ER_PARTIAL_REVOKE_AND_DB_GRANT_BOTH_EXISTS 3901
#define ER_UNIT_NOT_FOUND 3902
#define ER_INVALID_JSON_VALUE_FOR_FUNC_INDEX 3903
#define ER_JSON_VALUE_OUT_OF_RANGE_FOR_FUNC_INDEX 3904
#define ER_EXCEEDED_MV_KEYS_NUM 3905
#define ER_EXCEEDED_MV_KEYS_SPACE 3906
#define ER_FUNCTIONAL_INDEX_DATA_IS_TOO_LONG 3907
#define ER_WRONG_MVI_VALUE 3908
#define ER_WARN_FUNC_INDEX_NOT_APPLICABLE 3909
#define ER_GRP_RPL_UDF_ERROR 3910
#define ER_UPDATE_GTID_PURGED_WITH_GR 3911
#define ER_GROUPING_ON_TIMESTAMP_IN_DST 3912
#define ER_TABLE_NAME_CAUSES_TOO_LONG_PATH 3913
#define ER_AUDIT_LOG_INSUFFICIENT_PRIVILEGE 3914
//#define OBSOLETE_ER_AUDIT_LOG_PASSWORD_HAS_BEEN_COPIED 3915
#define ER_DA_GRP_RPL_STARTED_AUTO_REJOIN 3916
#define ER_SYSVAR_CHANGE_DURING_QUERY 3917
#define ER_GLOBSTAT_CHANGE_DURING_QUERY 3918
#define ER_GRP_RPL_MESSAGE_SERVICE_INIT_FAILURE 3919
#define ER_CHANGE_SOURCE_WRONG_COMPRESSION_ALGORITHM_CLIENT 3920
#define ER_CHANGE_SOURCE_WRONG_COMPRESSION_LEVEL_CLIENT 3921
#define ER_WRONG_COMPRESSION_ALGORITHM_CLIENT 3922
#define ER_WRONG_COMPRESSION_LEVEL_CLIENT 3923
#define ER_CHANGE_SOURCE_WRONG_COMPRESSION_ALGORITHM_LIST_CLIENT 3924
#define ER_CLIENT_PRIVILEGE_CHECKS_USER_CANNOT_BE_ANONYMOUS 3925
#define ER_CLIENT_PRIVILEGE_CHECKS_USER_DOES_NOT_EXIST 3926
#define ER_CLIENT_PRIVILEGE_CHECKS_USER_CORRUPT 3927
#define ER_CLIENT_PRIVILEGE_CHECKS_USER_NEEDS_RPL_APPLIER_PRIV 3928
#define ER_WARN_DA_PRIVILEGE_NOT_REGISTERED 3929
#define ER_CLIENT_KEYRING_UDF_KEY_INVALID 3930
#define ER_CLIENT_KEYRING_UDF_KEY_TYPE_INVALID 3931
#define ER_CLIENT_KEYRING_UDF_KEY_TOO_LONG 3932
#define ER_CLIENT_KEYRING_UDF_KEY_TYPE_TOO_LONG 3933
#define ER_JSON_SCHEMA_VALIDATION_ERROR_WITH_DETAILED_REPORT 3934
#define ER_DA_UDF_INVALID_CHARSET_SPECIFIED 3935
#define ER_DA_UDF_INVALID_CHARSET 3936
#define ER_DA_UDF_INVALID_COLLATION 3937
#define ER_DA_UDF_INVALID_EXTENSION_ARGUMENT_TYPE 3938
#define ER_MULTIPLE_CONSTRAINTS_WITH_SAME_NAME 3939
#define ER_CONSTRAINT_NOT_FOUND 3940
#define ER_ALTER_CONSTRAINT_ENFORCEMENT_NOT_SUPPORTED 3941
#define ER_TABLE_VALUE_CONSTRUCTOR_MUST_HAVE_COLUMNS 3942
#define ER_TABLE_VALUE_CONSTRUCTOR_CANNOT_HAVE_DEFAULT 3943
#define ER_CLIENT_QUERY_FAILURE_INVALID_NON_ROW_FORMAT 3944
#define ER_REQUIRE_ROW_FORMAT_INVALID_VALUE 3945
#define ER_FAILED_TO_DETERMINE_IF_ROLE_IS_MANDATORY 3946
#define ER_FAILED_TO_FETCH_MANDATORY_ROLE_LIST 3947
#define ER_CLIENT_LOCAL_FILES_DISABLED 3948
#define ER_IMP_INCOMPATIBLE_CFG_VERSION 3949
#define ER_DA_OOM 3950
#define ER_DA_UDF_INVALID_ARGUMENT_TO_SET_CHARSET 3951
#define ER_DA_UDF_INVALID_RETURN_TYPE_TO_SET_CHARSET 3952
#define ER_MULTIPLE_INTO_CLAUSES 3953
#define ER_MISPLACED_INTO 3954
#define ER_USER_ACCESS_DENIED_FOR_USER_ACCOUNT_BLOCKED_BY_PASSWORD_LOCK 3955
#define ER_WARN_DEPRECATED_YEAR_UNSIGNED 3956
#define ER_CLONE_NETWORK_PACKET 3957
#define ER_SDI_OPERATION_FAILED_MISSING_RECORD 3958
#define ER_DEPENDENT_BY_CHECK_CONSTRAINT 3959
#define ER_GRP_OPERATION_NOT_ALLOWED_GR_MUST_STOP 3960
#define ER_WARN_DEPRECATED_JSON_TABLE_ON_ERROR_ON_EMPTY 3961
#define ER_WARN_DEPRECATED_INNER_INTO 3962
#define ER_WARN_DEPRECATED_VALUES_FUNCTION_ALWAYS_NULL 3963
#define ER_WARN_DEPRECATED_SQL_CALC_FOUND_ROWS 3964
#define ER_WARN_DEPRECATED_FOUND_ROWS 3965
#define ER_MISSING_JSON_VALUE 3966
#define ER_MULTIPLE_JSON_VALUES 3967
#define ER_HOSTNAME_TOO_LONG 3968
//#define OBSOLETE_ER_WARN_CLIENT_DEPRECATED_PARTITION_PREFIX_KEY 3969
#define ER_GROUP_REPLICATION_USER_EMPTY_MSG 3970
#define ER_GROUP_REPLICATION_USER_MANDATORY_MSG 3971
#define ER_GROUP_REPLICATION_PASSWORD_LENGTH 3972
#define ER_SUBQUERY_TRANSFORM_REJECTED 3973
#define ER_DA_GRP_RPL_RECOVERY_ENDPOINT_FORMAT 3974
#define ER_DA_GRP_RPL_RECOVERY_ENDPOINT_INVALID 3975
#define ER_WRONG_VALUE_FOR_VAR_PLUS_ACTIONABLE_PART 3976
#define ER_STATEMENT_NOT_ALLOWED_AFTER_START_TRANSACTION 3977
#define ER_FOREIGN_KEY_WITH_ATOMIC_CREATE_SELECT 3978
#define ER_NOT_ALLOWED_WITH_START_TRANSACTION 3979
#define ER_INVALID_JSON_ATTRIBUTE 3980
#define ER_ENGINE_ATTRIBUTE_NOT_SUPPORTED 3981
#define ER_INVALID_USER_ATTRIBUTE_JSON 3982
#define ER_INNODB_REDO_DISABLED 3983
#define ER_INNODB_REDO_ARCHIVING_ENABLED 3984
#define ER_MDL_OUT_OF_RESOURCES 3985
#define ER_IMPLICIT_COMPARISON_FOR_JSON 3986
#define ER_FUNCTION_DOES_NOT_SUPPORT_CHARACTER_SET 3987
#define ER_IMPOSSIBLE_STRING_CONVERSION 3988
#define ER_SCHEMA_READ_ONLY 3989
#define ER_RPL_ASYNC_RECONNECT_GTID_MODE_OFF 3990
#define ER_RPL_ASYNC_RECONNECT_AUTO_POSITION_OFF 3991
#define ER_DISABLE_GTID_MODE_REQUIRES_ASYNC_RECONNECT_OFF 3992
#define ER_DISABLE_AUTO_POSITION_REQUIRES_ASYNC_RECONNECT_OFF 3993
#define ER_INVALID_PARAMETER_USE 3994
#define ER_CHARACTER_SET_MISMATCH 3995
#define ER_WARN_VAR_VALUE_CHANGE_NOT_SUPPORTED 3996
#define ER_INVALID_TIME_ZONE_INTERVAL 3997
#define ER_INVALID_CAST 3998
#define ER_HYPERGRAPH_NOT_SUPPORTED_YET 3999
#define ER_WARN_HYPERGRAPH_EXPERIMENTAL 4000
#define ER_DA_NO_ERROR_LOG_PARSER_CONFIGURED 4001
#define ER_DA_ERROR_LOG_TABLE_DISABLED 4002
#define ER_DA_ERROR_LOG_MULTIPLE_FILTERS 4003
#define ER_DA_CANT_OPEN_ERROR_LOG 4004
#define ER_USER_REFERENCED_AS_DEFINER 4005
#define ER_CANNOT_USER_REFERENCED_AS_DEFINER 4006
#define ER_REGEX_NUMBER_TOO_BIG 4007
#define ER_SPVAR_NONINTEGER_TYPE 4008
#define WARN_UNSUPPORTED_ACL_TABLES_READ 4009
#define ER_BINLOG_UNSAFE_ACL_TABLE_READ_IN_DML_DDL 4010
#define ER_STOP_REPLICA_MONITOR_IO_THREAD_TIMEOUT 4011
#define ER_STARTING_REPLICA_MONITOR_IO_THREAD 4012
#define ER_CANT_USE_ANONYMOUS_TO_GTID_WITH_GTID_MODE_NOT_ON 4013
#define ER_CANT_COMBINE_ANONYMOUS_TO_GTID_AND_AUTOPOSITION 4014
#define ER_ASSIGN_GTIDS_TO_ANONYMOUS_TRANSACTIONS_REQUIRES_GTID_MODE_ON 4015
#define ER_SQL_REPLICA_SKIP_COUNTER_USED_WITH_GTID_MODE_ON 4016
#define ER_USING_ASSIGN_GTIDS_TO_ANONYMOUS_TRANSACTIONS_AS_LOCAL_OR_UUID 4017
//#define OBSOLETE_ER_SET_GTID_TO_ANON_AND_WAIT_UNTIL_SQL_THD_AFTER_GTIDS 4018
#define ER_CANT_SET_SQL_AFTER_OR_BEFORE_GTIDS_WITH_ANONYMOUS_TO_GTID 4019
#define ER_ANONYMOUS_TO_GTID_UUID_SAME_AS_GROUP_NAME 4020
#define ER_CANT_USE_SAME_UUID_AS_GROUP_NAME 4021
#define ER_GRP_RPL_RECOVERY_CHANNEL_STILL_RUNNING 4022
#define ER_INNODB_INVALID_AUTOEXTEND_SIZE_VALUE 4023
#define ER_INNODB_INCOMPATIBLE_WITH_TABLESPACE 4024
#define ER_INNODB_AUTOEXTEND_SIZE_OUT_OF_RANGE 4025
#define ER_CANNOT_USE_AUTOEXTEND_SIZE_CLAUSE 4026
#define ER_ROLE_GRANTED_TO_ITSELF 4027
#define ER_TABLE_MUST_HAVE_A_VISIBLE_COLUMN 4028
#define ER_INNODB_COMPRESSION_FAILURE 4029
#define ER_WARN_ASYNC_CONN_FAILOVER_NETWORK_NAMESPACE 4030
#define ER_CLIENT_INTERACTION_TIMEOUT 4031
#define ER_INVALID_CAST_TO_GEOMETRY 4032
#define ER_INVALID_CAST_POLYGON_RING_DIRECTION 4033
#define ER_GIS_DIFFERENT_SRIDS_AGGREGATION 4034
#define ER_RELOAD_KEYRING_FAILURE 4035
#define ER_SDI_GET_KEYS_INVALID_TABLESPACE 4036
#define ER_CHANGE_RPL_SRC_WRONG_COMPRESSION_ALGORITHM_SIZE 4037
//#define OBSOLETE_ER_WARN_DEPRECATED_TLS_VERSION_FOR_CHANNEL_CLI 4038
#define ER_CANT_USE_SAME_UUID_AS_VIEW_CHANGE_UUID 4039
#define ER_ANONYMOUS_TO_GTID_UUID_SAME_AS_VIEW_CHANGE_UUID 4040
#define ER_GRP_RPL_VIEW_CHANGE_UUID_FAIL_GET_VARIABLE 4041
#define ER_WARN_ADUIT_LOG_MAX_SIZE_AND_PRUNE_SECONDS 4042
#define ER_WARN_ADUIT_LOG_MAX_SIZE_CLOSE_TO_ROTATE_ON_SIZE 4043
#define ER_KERBEROS_CREATE_USER 4044
#define ER_INSTALL_PLUGIN_CONFLICT_CLIENT 4045
#define ER_DA_ERROR_LOG_COMPONENT_FLUSH_FAILED 4046
#define ER_WARN_SQL_AFTER_MTS_GAPS_GAP_NOT_CALCULATED 4047
#define ER_INVALID_ASSIGNMENT_TARGET 4048
#define ER_OPERATION_NOT_ALLOWED_ON_GR_SECONDARY 4049
#define ER_GRP_RPL_FAILOVER_CHANNEL_STATUS_PROPAGATION 4050
#define ER_WARN_AUDIT_LOG_FORMAT_UNIX_TIMESTAMP_ONLY_WHEN_JSON 4051
#define ER_INVALID_MFA_PLUGIN_SPECIFIED 4052
#define ER_IDENTIFIED_BY_UNSUPPORTED 4053
#define ER_INVALID_PLUGIN_FOR_REGISTRATION 4054
#define ER_PLUGIN_REQUIRES_REGISTRATION 4055
#define ER_MFA_METHOD_EXISTS 4056
#define ER_MFA_METHOD_NOT_EXISTS 4057
#define ER_AUTHENTICATION_POLICY_MISMATCH 4058
#define ER_PLUGIN_REGISTRATION_DONE 4059
#define ER_INVALID_USER_FOR_REGISTRATION 4060
#define ER_USER_REGISTRATION_FAILED 4061
#define ER_MFA_METHODS_INVALID_ORDER 4062
#define ER_MFA_METHODS_IDENTICAL 4063
#define ER_INVALID_MFA_OPERATIONS_FOR_PASSWORDLESS_USER 4064
#define ER_CHANGE_REPLICATION_SOURCE_NO_OPTIONS_FOR_GTID_ONLY 4065
#define ER_CHANGE_REP_SOURCE_CANT_DISABLE_REQ_ROW_FORMAT_WITH_GTID_ONLY 4066
#define ER_CHANGE_REP_SOURCE_CANT_DISABLE_AUTO_POSITION_WITH_GTID_ONLY 4067
#define ER_CHANGE_REP_SOURCE_CANT_DISABLE_GTID_ONLY_WITHOUT_POSITIONS 4068
#define ER_CHANGE_REP_SOURCE_CANT_DISABLE_AUTO_POS_WITHOUT_POSITIONS 4069
#define ER_CHANGE_REP_SOURCE_GR_CHANNEL_WITH_GTID_MODE_NOT_ON 4070
#define ER_CANT_USE_GTID_ONLY_WITH_GTID_MODE_NOT_ON 4071
#define ER_WARN_C_DISABLE_GTID_ONLY_WITH_SOURCE_AUTO_POS_INVALID_POS 4072
#define ER_DA_SSL_FIPS_MODE_ERROR 4073
#define ER_VALUE_OUT_OF_RANGE 4074
#define ER_FULLTEXT_WITH_ROLLUP 4075
#define ER_REGEXP_MISSING_RESOURCE 4076
#define ER_WARN_REGEXP_USING_DEFAULT 4077
#define ER_REGEXP_MISSING_FILE 4078
#define ER_WARN_DEPRECATED_COLLATION 4079
#define ER_CONCURRENT_PROCEDURE_USAGE 4080
#define ER_DA_GLOBAL_CONN_LIMIT 4081
#define ER_DA_CONN_LIMIT 4082
#define ER_ALTER_OPERATION_NOT_SUPPORTED_REASON_COLUMN_TYPE_INSTANT 4083
#define ER_WARN_SF_UDF_NAME_COLLISION 4084
#define ER_CANNOT_PURGE_BINLOG_WITH_BACKUP_LOCK 4085
#define ER_TOO_MANY_WINDOWS 4086
#define ER_MYSQLBACKUP_CLIENT_MSG 4087
#define ER_COMMENT_CONTAINS_INVALID_STRING 4088
#define ER_DEFINITION_CONTAINS_INVALID_STRING 4089
#define ER_CANT_EXECUTE_COMMAND_WITH_ASSIGNED_GTID_NEXT 4090
#define ER_XA_TEMP_TABLE 4091
#define ER_INNODB_MAX_ROW_VERSION 4092
//#define OBSOLETE_ER_INNODB_INSTANT_ADD_NOT_SUPPORTED_MAX_SIZE 4093
#define ER_OPERATION_NOT_ALLOWED_WHILE_PRIMARY_CHANGE_IS_RUNNING 4094
#define ER_WARN_DEPRECATED_DATETIME_DELIMITER 4095
#define ER_WARN_DEPRECATED_SUPERFLUOUS_DELIMITER 4096
#define ER_CANNOT_PERSIST_SENSITIVE_VARIABLES 4097
#define ER_WARN_CANNOT_SECURELY_PERSIST_SENSITIVE_VARIABLES 4098
#define ER_WARN_TRG_ALREADY_EXISTS 4099
#define ER_IF_NOT_EXISTS_UNSUPPORTED_TRG_EXISTS_ON_DIFFERENT_TABLE 4100
#define ER_IF_NOT_EXISTS_UNSUPPORTED_UDF_NATIVE_FCT_NAME_COLLISION 4101
#define ER_SET_PASSWORD_AUTH_PLUGIN_ERROR 4102
//#define OBSOLETE_ER_REDUCED_DBLWR_FILE_CORRUPTED 4103
//#define OBSOLETE_ER_REDUCED_DBLWR_PAGE_FOUND 4104
#define ER_SRS_INVALID_LATITUDE_OF_ORIGIN 4105
#define ER_SRS_INVALID_LONGITUDE_OF_ORIGIN 4106
#define ER_SRS_UNUSED_PROJ_PARAMETER_PRESENT 4107
#define ER_GIPK_COLUMN_EXISTS 4108
#define ER_GIPK_FAILED_AUTOINC_COLUMN_EXISTS 4109
#define ER_GIPK_COLUMN_ALTER_NOT_ALLOWED 4110
#define ER_DROP_PK_COLUMN_TO_DROP_GIPK 4111
#define ER_CREATE_SELECT_WITH_GIPK_DISALLOWED_IN_SBR 4112
//#define OBSOLETE_ER_DA_EXPIRE_LOGS_DAYS_IGNORED 4113
#define ER_CTE_RECURSIVE_NOT_UNION 4114
#define ER_COMMAND_BACKEND_FAILED_TO_FETCH_SECURITY_CTX 4115
#define ER_COMMAND_SERVICE_BACKEND_FAILED 4116
#define ER_CLIENT_FILE_PRIVILEGE_FOR_REPLICATION_CHECKS 4117
#define ER_GROUP_REPLICATION_FORCE_MEMBERS_COMMAND_FAILURE 4118
#define ER_WARN_DEPRECATED_IDENT 4119
#define ER_INTERSECT_ALL_MAX_DUPLICATES_EXCEEDED 4120
#define ER_TP_QUERY_THRS_PER_GRP_EXCEEDS_TXN_THR_LIMIT 4121
#define ER_BAD_TIMESTAMP_FORMAT 4122
#define ER_SHAPE_PRIDICTION_UDF 4123
#define ER_SRS_INVALID_HEIGHT 4124
#define ER_SRS_INVALID_SCALING 4125
#define ER_SRS_INVALID_ZONE_WIDTH 4126
#define ER_SRS_INVALID_LATITUDE_POLAR_STERE_VAR_A 4127
#define ER_WARN_DEPRECATED_CLIENT_NO_SCHEMA_OPTION 4128
#define ER_TABLE_NOT_EMPTY 4129
#define ER_TABLE_NO_PRIMARY_KEY 4130
#define ER_TABLE_IN_SHARED_TABLESPACE 4131
#define ER_INDEX_OTHER_THAN_PK 4132
#define ER_LOAD_BULK_DATA_UNSORTED 4133
#define ER_BULK_EXECUTOR_ERROR 4134
#define ER_BULK_READER_LIBCURL_INIT_FAILED 4135
#define ER_BULK_READER_LIBCURL_ERROR 4136
#define ER_BULK_READER_SERVER_ERROR 4137
#define ER_BULK_READER_COMMUNICATION_ERROR 4138
#define ER_BULK_LOAD_DATA_FAILED 4139
#define ER_BULK_LOADER_COLUMN_TOO_BIG_FOR_LEFTOVER_BUFFER 4140
#define ER_BULK_LOADER_COMPONENT_ERROR 4141
#define ER_BULK_LOADER_FILE_CONTAINS_LESS_LINES_THAN_IGNORE_CLAUSE 4142
#define ER_BULK_PARSER_MISSING_ENCLOSED_BY 4143
#define ER_BULK_PARSER_ROW_BUFFER_MAX_TOTAL_COLS_EXCEEDED 4144
#define ER_BULK_PARSER_COPY_BUFFER_SIZE_EXCEEDED 4145
#define ER_BULK_PARSER_UNEXPECTED_END_OF_INPUT 4146
#define ER_BULK_PARSER_UNEXPECTED_ROW_TERMINATOR 4147
#define ER_BULK_PARSER_UNEXPECTED_CHAR_AFTER_ENDING_ENCLOSED_BY 4148
#define ER_BULK_PARSER_UNEXPECTED_CHAR_AFTER_NULL_ESCAPE 4149
#define ER_BULK_PARSER_UNEXPECTED_CHAR_AFTER_COLUMN_TERMINATOR 4150
#define ER_BULK_PARSER_INCOMPLETE_ESCAPE_SEQUENCE 4151
#define ER_LOAD_BULK_DATA_FAILED 4152
#define ER_LOAD_BULK_DATA_WRONG_VALUE_FOR_FIELD 4153
#define ER_LOAD_BULK_DATA_WARN_NULL_TO_NOTNULL 4154
#define ER_REQUIRE_TABLE_PRIMARY_KEY_CHECK_GENERATE_WITH_GR 4155
#define ER_CANT_CHANGE_SYS_VAR_IN_READ_ONLY_MODE 4156
#define ER_INNODB_INSTANT_ADD_DROP_NOT_SUPPORTED_MAX_SIZE 4157
#define ER_INNODB_INSTANT_ADD_NOT_SUPPORTED_MAX_FIELDS 4158
#define ER_CANT_SET_PERSISTED 4159
#define ER_INSTALL_COMPONENT_SET_NULL_VALUE 4160
#define ER_INSTALL_COMPONENT_SET_UNUSED_VALUE 4161
#define ER_WARN_DEPRECATED_USER_DEFINED_COLLATIONS 4162
#define ER_USER_LOCK_OVERLONG_NAME 4163
#define ER_WARN_NO_SPACE_VERSION_COMMENT 4164
#define ER_VALIDATE_PASSWORD_INSUFFICIENT_CHANGED_CHARACTERS 4165
#define ER_WARN_DEPRECATED_WITH_NOTE 4166
#define ER_LANGUAGE_COMPONENT 6000
#define ER_LANGUAGE_COMPONENT_NOT_AVAILABLE 6001
#define ER_LANGUAGE_COMPONENT_UNSUPPORTED_LANGUAGE 6002
#define ER_LANGUAGE_COMPONENT_CANNOT_UNINSTALL 6003
#define ER_SP_NO_ALTER_LANGUAGE 6004
#define ER_EXPLAIN_INTO_ANALYZE_NOT_SUPPORTED 6005
#define ER_EXPLAIN_INTO_IMPLICIT_FORMAT_NOT_SUPPORTED 6006
#define ER_EXPLAIN_INTO_FORMAT_NOT_SUPPORTED 6007
#define ER_NULL_CANT_BE_PERSISTED_FOR_READONLY 6008
#define ER_EXPLAIN_INTO_FOR_CONNECTION_NOT_SUPPORTED 6009
#define ER_INNODB_IMPORT_WRONG_DROPPED_ENUM_LENGTH 6010
#define ER_INNODB_IMPORT_WRONG_NUMBER_OF_INDEXES_ZERO 6011
#define ER_INNODB_IMPORT_WRONG_NUMBER_OF_INDEXES_TOO_HIGH 6012
#define ER_INNODB_IMPORT_DROP_COL_METADATA_MISMATCH 6013
#define ER_INNODB_IMPORT_ENUM_NULL_TERMINATOR_MISSING 6014
#define ER_SIMULATED_INJECTION_ERROR 6015
#define ER_WARN_DEPRECATED_DYNAMIC_PRIV_IN_GRANT 6016
#define ER_BULK_MULTI_READER_OPEN_FILE_FAILED 6017
#define ER_BULK_MULTI_READER_READ_FILE_FAILED 6018
#define ER_BULK_MERGE_INVALID_CHUNK 6019
#define ER_BULK_MERGE_NOT_ALL_CHUNKS_CONSUMED 6020
#define ER_BULK_WRITER_LIBCURL_INIT_FAILED 6021
#define ER_BULK_WRITER_LIBCURL_ERROR 6022
#define ER_BULK_SORTING_LOADER_WRITE 6023
#define ER_BULK_SORTING_LOADER_WAIT 6024
#define ER_BULK_READER_OPEN_FILE_FAILED 6025
#define ER_BULK_LOAD_TABLE_HAS_INSTANT_COLS 6026
#define ER_BULK_LOAD_RESOURCE 6027
#define ER_BULK_LOAD_SECONDARY_ENGINE 6028
#define ER_BULK_READER_ERROR 6029
#define ER_BULK_READER_FILE_DOESNT_EXIST 6030
#define ER_BULK_READER_COULDNT_RESOLVE_HOST 6031
#define ER_START_REPLICA_CHANNEL_INVALID_CONFIGURATION 6032
#define ER_CANNOT_EXECUTE_IN_PRIMARY 6033
#define ER_TOO_MANY_GROUP_BY_MODIFIER_BRANCHES 6034
#define ER_WARN_DEPRECATED_ENGINE_SYNTAX_NO_REPLACEMENT 6035
#define ER_QUALIFY_WITHOUT_WINDOW_FUNCTION 6036
#define ER_SUPPORTED_ONLY_WITH_HYPERGRAPH 6037
#define ER_SPECIFIC_ACCESS_DENIED 6038
#define ER_CANT_SET_GTID_NEXT_TO_AUTOMATIC_TAGGED_WHEN_GTID_MODE_IS_OFF 6039
#define ER_GTID_NEXT_TAG_GTID_MODE_OFF 6040
#define ER_LH_COL_NOT_NULLABLE 6041
#define ER_LH_WARN_COL_MISSING_NOT_NULLABLE 6042
#define ER_LH_COL_IS_EMPTY 6043
#define ER_LH_COL_IS_EMPTY_WARN 6044
#define ER_LH_BAD_VALUE 6045
#define ER_LH_DECIMAL_UNKNOWN_ERR 6046
#define ER_LH_DECIMAL_OOM_ERR 6047
#define ER_LH_WARN_DECIMAL_ROUNDING 6048
#define ER_LH_DECIMAL_PRECISION_EXCEEDS_SCHEMA 6049
#define ER_LH_EXCEEDS_MIN 6050
#define ER_LH_EXCEEDS_MAX 6051
#define ER_LH_WARN_EXCEEDS_MIN_TRUNCATING 6052
#define ER_LH_WARN_EXCEEDS_MAX_TRUNCATING 6053
#define ER_LH_REAL_IS_NAN 6054
#define ER_LH_OUT_OF_RANGE 6055
#define ER_LH_DATETIME_FORMAT 6056
#define ER_LH_WARN_TRUNCATED 6057
#define ER_LH_CANNOT_CONVERT_STRING 6058
#define ER_LH_RESOURCE_PRINCIPAL_ERR 6059
#define ER_LH_AWS_AUTH_ERR 6060
#define ER_LH_CSV_PARSING_ERR 6061
#define ER_LH_COLUMN_MISMATCH_ERR 6062
#define ER_LH_COLUMN_MAX_ERR 6063
#define ER_LH_CHARSET_UNSUPPORTED 6064
#define ER_LH_PARQUET_DECIMAL_CONVERSION_ERR 6065
#define ER_LH_STRING_TOO_LONG 6066
#define ER_LH_RESOURCE_PRINCIPAL_BUCKET_ERR 6067
#define ER_LH_NO_FILES_FOUND 6068
#define ER_LH_EMPTY_FILE 6069
#define ER_LH_DUPLICATE_FILE 6070
#define ER_LH_AVRO_SCHEMA_DEPTH_EXCEEDS_MAX 6071
#define ER_LH_AVRO_HEADER_MISMATCH 6072
#define ER_LH_AVRO_ENUM_CANNOT_CONVERT_CHARSET 6073
#define ER_LH_AVRO_ENUM_MISMATCH 6074
#define ER_LH_AVRO_TYPE_CANNOT_CONVERT 6075
#define ER_LH_AVRO_FILE_ENDS_UNEXPECTEDLY 6076
#define ER_LH_AVRO_FILE_DATA_CORRUPT 6077
#define ER_LH_AVRO_INVALID_UNION 6078
#define ER_LH_AVRO_INVALID_BLOCK_SIZE 6079
#define ER_LH_AVRO_INVALID_BLOCK_RECORD_COUNT 6080
#define ER_LH_FORMAT_HEADER_NO_MAGIC_BYTES 6081
#define ER_LH_AVRO_HEADER_METADATA_ERR 6082
#define ER_LH_AVRO_HEADER_NO_SCHEMA 6083
#define ER_LH_AVRO_NO_CODEC_IN_HEADER 6084
#define ER_LH_AVRO_INVALID_NAME_IN_SCHEMA 6085
#define ER_LH_AVRO_DECODING_ERR 6086
#define ER_LH_PARQUET_NON_UTF8_FILE_ENC 6087
#define ER_LH_PARQUET_SCHEMA_MISMATCH 6088
#define ER_LH_PARQUET_ROW_GROUP_SIZE_EXCEEDS_MAX 6089
#define ER_LH_PARQUET_CANNOT_LOCATE_OFFSET 6090
#define ER_LH_PARQUET_TYPE_CANNOT_CONVERT 6091
#define ER_LH_PARQUET_CANNOT_LOCATE_SCHEMA 6092
#define ER_LH_INFER_SCHEMA_MISMATCH 6093
#define ER_LH_OOM 6094
#define ER_LH_WARN_INFER_SKIPPED_LINES 6095
#define ER_LH_WARN_INFER_SKIPPED_FILES 6096
#define ER_LH_INFER_FILE_HAS_NO_DATA 6097
#define ER_LH_INFER_NO_DATA 6098
#define ER_LH_INFER_NO_FILES 6099
#define ER_LH_WARN_INFER_USE_DEFAULT_COL_NAMES 6100
#define ER_LH_PARQUET_CANNOT_READ_HEADER 6101
#define ER_LH_INFER_WARN_GOT_EXCEPTION 6102
#define ER_LH_AVRO_CANNOT_PARSE_HEADER 6103
#define ER_LH_PARQUET_CANT_OPEN_FILE 6104
#define ER_LH_TOO_LARGE_VALUE_ERR 6105
#define ER_LH_TOO_LARGE_ROW_ERR 6106
#define ER_TABLESAMPLE_PERCENTAGE 6107
#define ER_TABLESAMPLE_ONLY_ON_BASE_TABLES 6108
//#define OBSOLETE_ER_PARAMETER_INDEX_OUT_OF_RANGE 6109
#define ER_RESULT_SIZE_LIMIT_EXCEEDED 6110
#define ER_LANGUAGE_COMPONENT_INTERNAL 6111
#define ER_LANGUAGE_COMPONENT_CONCURRENCY_LIMIT 6112
#define ER_LANGUAGE_COMPONENT_RUNTIME 6113
#define ER_LANGUAGE_COMPONENT_TIMEZONE 6114
#define ER_LANGUAGE_COMPONENT_KEYWORD 6115
#define ER_LANGUAGE_COMPONENT_SET_SYSTEM_VARIABLE 6116
#define ER_LANGUAGE_COMPONENT_UNSUPPORTED_TYPE 6117
#define ER_LANGUAGE_COMPONENT_CONVERSION 6118
#define ER_WARN_SP_STATEMENT_PARTIALLY_EXECUTED 6119
#define ER_STMT_EXECUTION_NOT_ALLOWED_WITHIN_SP_OR_TRG_OR_UDF 6120
#define ER_LH_JSON_PARSING 6121
#define ER_ENGINE_CANNOT_BE_DEFAULT 6122
#define ER_PARTITION_PREFIX_KEY_NOT_SUPPORTED 6123
#define ER_WARN_DEPRECATED_NON_STANDARD_KEY 6124
#define ER_FK_NO_UNIQUE_INDEX_PARENT 6125
#define ER_ACCESS_DENIED_NO_PROXY_GRANT 6126
#define ER_ACCESS_DENIED_NO_PROXY 6127
#define ER_LH_USER_DATA_ACCESS_FAILED 6128
#define ER_BULK_READER_ZSTD_ERROR 6129
#define ER_BULK_PARSER_ERROR 6130
#define ER_LH_INVALID_JSON_FILE_FORMAT_SCHEMA 6131
#define ER_LH_INFER_JSON_INVALID_SCHEMA 6132
#define ER_LH_JSON_FILE_FORMAT_WARN_INFER_SCHEMA 6133
#define ER_NON_SCALAR_USED_AS_KEY 6134
#define ER_INCOMPATIBLE_TYPE_AGG 6135
#define ER_DATA_INCOMPATIBLE_WITH_VECTOR 6136
#define ER_EXCEEDS_VECTOR_MAX_DIMENSIONS 6137
#define ER_TO_VECTOR_CONVERSION 6138
#define ER_EXTERNAL_UNSUPPORTED_INDEX_ALGORITHM 6139
#define ER_TP_CANNOT_DISABLE_MTL_WITH_DL 6140
#define ER_PARSER_TRACE 10000
#define ER_BOOTSTRAP_CANT_THREAD 10001
#define ER_TRIGGER_INVALID_VALUE 10002
#define ER_OPT_WRONG_TREE 10003
#define ER_DD_FAILSAFE 10004
#define ER_DD_NO_WRITES_NO_REPOPULATION 10005
#define ER_DD_VERSION_FOUND 10006
#define ER_DD_VERSION_INSTALLED 10007
#define ER_DD_VERSION_UNSUPPORTED 10008
//#define OBSOLETE_ER_LOG_SYSLOG_FACILITY_FAIL 10009
#define ER_LOG_SYSLOG_CANNOT_OPEN 10010
#define ER_LOG_SLOW_CANNOT_OPEN 10011
#define ER_LOG_GENERAL_CANNOT_OPEN 10012
#define ER_LOG_CANNOT_WRITE 10013
//#define OBSOLETE_ER_RPL_ZOMBIE_ENCOUNTERED 10014
#define ER_RPL_GTID_TABLE_CANNOT_OPEN 10015
#define ER_SYSTEM_SCHEMA_NOT_FOUND 10016
#define ER_DD_INIT_UPGRADE_FAILED 10017
#define ER_VIEW_UNKNOWN_CHARSET_OR_COLLATION 10018
#define ER_DD_VIEW_CANT_ALLOC_CHARSET 10019
#define ER_DD_INIT_FAILED 10020
#define ER_DD_UPDATING_PLUGIN_MD_FAILED 10021
//#define OBSOLETE_ER_DD_POPULATING_TABLES_FAILED 10022
#define ER_DD_VIEW_CANT_CREATE 10023
#define ER_DD_METADATA_NOT_FOUND 10024
#define ER_DD_CACHE_NOT_EMPTY_AT_SHUTDOWN 10025
#define ER_DD_OBJECT_REMAINS 10026
#define ER_DD_OBJECT_REMAINS_IN_RELEASER 10027
#define ER_DD_OBJECT_RELEASER_REMAINS 10028
#define ER_DD_CANT_GET_OBJECT_KEY 10029
#define ER_DD_CANT_CREATE_OBJECT_KEY 10030
#define ER_CANT_CREATE_HANDLE_MGR_THREAD 10031
#define ER_RPL_REPO_HAS_GAPS 10032
#define ER_INVALID_VALUE_FOR_ENFORCE_GTID_CONSISTENCY 10033
#define ER_CHANGED_ENFORCE_GTID_CONSISTENCY 10034
#define ER_CHANGED_GTID_MODE 10035
#define ER_DISABLED_STORAGE_ENGINE_AS_DEFAULT 10036
#define ER_DEBUG_SYNC_HIT 10037
#define ER_DEBUG_SYNC_EXECUTED 10038
#define ER_DEBUG_SYNC_THREAD_MAX 10039
#define ER_DEBUG_SYNC_OOM 10040
#define ER_CANT_INIT_TC_LOG 10041
#define ER_EVENT_CANT_INIT_QUEUE 10042
#define ER_EVENT_PURGING_QUEUE 10043
#define ER_EVENT_LAST_EXECUTION 10044
#define ER_EVENT_MESSAGE_STACK 10045
#define ER_EVENT_EXECUTION_FAILED 10046
#define ER_CANT_INIT_SCHEDULER_THREAD 10047
#define ER_SCHEDULER_STOPPED 10048
#define ER_CANT_CREATE_SCHEDULER_THREAD 10049
#define ER_SCHEDULER_WAITING 10050
#define ER_SCHEDULER_STARTED 10051
#define ER_SCHEDULER_STOPPING_FAILED_TO_GET_EVENT 10052
#define ER_SCHEDULER_STOPPING_FAILED_TO_CREATE_WORKER 10053
#define ER_SCHEDULER_KILLING 10054
#define ER_UNABLE_TO_RESOLVE_IP 10055
#define ER_UNABLE_TO_RESOLVE_HOSTNAME 10056
#define ER_HOSTNAME_RESEMBLES_IPV4 10057
#define ER_HOSTNAME_DOESNT_RESOLVE_TO 10058
#define ER_ADDRESSES_FOR_HOSTNAME_HEADER 10059
#define ER_ADDRESSES_FOR_HOSTNAME_LIST_ITEM 10060
#define ER_TRG_WITHOUT_DEFINER 10061
#define ER_TRG_NO_CLIENT_CHARSET 10062
#define ER_PARSING_VIEW 10063
#define ER_COMPONENTS_INFRASTRUCTURE_BOOTSTRAP 10064
#define ER_COMPONENTS_INFRASTRUCTURE_SHUTDOWN 10065
#define ER_COMPONENTS_PERSIST_LOADER_BOOTSTRAP 10066
#define ER_DEPART_WITH_GRACE 10067
#define ER_CA_SELF_SIGNED 10068
#define ER_SSL_LIBRARY_ERROR 10069
#define ER_NO_THD_NO_UUID 10070
#define ER_UUID_SALT 10071
#define ER_UUID_IS 10072
#define ER_UUID_INVALID 10073
#define ER_UUID_SCRUB 10074
#define ER_CREATING_NEW_UUID 10075
#define ER_CANT_CREATE_UUID 10076
#define ER_UNKNOWN_UNSUPPORTED_STORAGE_ENGINE 10077
#define ER_SECURE_AUTH_VALUE_UNSUPPORTED 10078
#define ER_INVALID_INSTRUMENT 10079
#define ER_INNODB_MANDATORY 10080
//#define OBSOLETE_ER_INNODB_CANNOT_BE_IGNORED 10081
//#define OBSOLETE_ER_OLD_PASSWORDS_NO_MIDDLE_GROUND 10082
#define ER_VERBOSE_REQUIRES_HELP 10083
#define ER_POINTLESS_WITHOUT_SLOWLOG 10084
#define ER_WASTEFUL_NET_BUFFER_SIZE 10085
#define ER_DEPRECATED_TIMESTAMP_IMPLICIT_DEFAULTS 10086
#define ER_FT_BOOL_SYNTAX_INVALID 10087
#define ER_CREDENTIALLESS_AUTO_USER_BAD 10088
#define ER_CONNECTION_HANDLING_OOM 10089
#define ER_THREAD_HANDLING_OOM 10090
#define ER_CANT_CREATE_TEST_FILE 10091
#define ER_CANT_CREATE_PID_FILE 10092
#define ER_CANT_REMOVE_PID_FILE 10093
#define ER_CANT_CREATE_SHUTDOWN_THREAD 10094
#define ER_SEC_FILE_PRIV_CANT_ACCESS_DIR 10095
#define ER_SEC_FILE_PRIV_IGNORED 10096
#define ER_SEC_FILE_PRIV_EMPTY 10097
#define ER_SEC_FILE_PRIV_NULL 10098
#define ER_SEC_FILE_PRIV_DIRECTORY_INSECURE 10099
#define ER_SEC_FILE_PRIV_CANT_STAT 10100
#define ER_SEC_FILE_PRIV_DIRECTORY_PERMISSIONS 10101
#define ER_SEC_FILE_PRIV_ARGUMENT_TOO_LONG 10102
#define ER_CANT_CREATE_NAMED_PIPES_THREAD 10103
#define ER_CANT_CREATE_TCPIP_THREAD 10104
#define ER_CANT_CREATE_SHM_THREAD 10105
#define ER_CANT_CREATE_INTERRUPT_THREAD 10106
#define ER_WRITABLE_CONFIG_REMOVED 10107
#define ER_CORE_VALUES 10108
#define ER_WRONG_DATETIME_SPEC 10109
#define ER_RPL_BINLOG_FILTERS_OOM 10110
#define ER_KEYCACHE_OOM 10111
#define ER_CONFIRMING_THE_FUTURE 10112
#define ER_BACK_IN_TIME 10113
#define ER_FUTURE_DATE 10114
#define ER_UNSUPPORTED_DATE 10115
#define ER_STARTING_AS 10116
#define ER_SHUTTING_DOWN_REPLICA_THREADS 10117
#define ER_DISCONNECTING_REMAINING_CLIENTS 10118
#define ER_ABORTING 10119
#define ER_BINLOG_END 10120
#define ER_CALL_ME_LOCALHOST 10121
#define ER_USER_REQUIRES_ROOT 10122
#define ER_REALLY_RUN_AS_ROOT 10123
#define ER_USER_WHAT_USER 10124
#define ER_TRANSPORTS_WHAT_TRANSPORTS 10125
#define ER_FAIL_SETGID 10126
#define ER_FAIL_SETUID 10127
#define ER_FAIL_SETREGID 10128
#define ER_FAIL_SETREUID 10129
#define ER_FAIL_CHROOT 10130
#define ER_WIN_LISTEN_BUT_HOW 10131
#define ER_NOT_RIGHT_NOW 10132
#define ER_FIXING_CLIENT_CHARSET 10133
#define ER_OOM 10134
#define ER_FAILED_TO_LOCK_MEM 10135
#define ER_MYINIT_FAILED 10136
#define ER_BEG_INITFILE 10137
#define ER_END_INITFILE 10138
#define ER_CHANGED_MAX_OPEN_FILES 10139
#define ER_CANT_INCREASE_MAX_OPEN_FILES 10140
#define ER_CHANGED_MAX_CONNECTIONS 10141
#define ER_CHANGED_TABLE_OPEN_CACHE 10142
#define ER_THE_USER_ABIDES 10143
#define ER_RPL_CANT_ADD_DO_TABLE 10144
#define ER_RPL_CANT_ADD_IGNORE_TABLE 10145
#define ER_TRACK_VARIABLES_BOGUS 10146
#define ER_EXCESS_ARGUMENTS 10147
#define ER_VERBOSE_HINT 10148
#define ER_CANT_READ_ERRMSGS 10149
#define ER_CANT_INIT_DBS 10150
#define ER_LOG_OUTPUT_CONTRADICTORY 10151
#define ER_NO_CSV_NO_LOG_TABLES 10152
#define ER_RPL_REWRITEDB_MISSING_ARROW 10153
#define ER_RPL_REWRITEDB_EMPTY_FROM 10154
#define ER_RPL_REWRITEDB_EMPTY_TO 10155
#define ER_LOG_FILES_GIVEN_LOG_OUTPUT_IS_TABLE 10156
#define ER_LOG_FILE_INVALID 10157
#define ER_LOWER_CASE_TABLE_NAMES_CS_DD_ON_CI_FS_UNSUPPORTED 10158
#define ER_LOWER_CASE_TABLE_NAMES_USING_2 10159
#define ER_LOWER_CASE_TABLE_NAMES_USING_0 10160
#define ER_NEED_LOG_BIN 10161
#define ER_NEED_FILE_INSTEAD_OF_DIR 10162
#define ER_LOG_BIN_BETTER_WITH_NAME 10163
#define ER_BINLOG_NEEDS_SERVERID 10164
#define ER_RPL_CANT_MAKE_PATHS 10165
#define ER_CANT_INITIALIZE_GTID 10166
#define ER_CANT_INITIALIZE_EARLY_PLUGINS 10167
#define ER_CANT_INITIALIZE_BUILTIN_PLUGINS 10168
#define ER_CANT_INITIALIZE_DYNAMIC_PLUGINS 10169
#define ER_PERFSCHEMA_INIT_FAILED 10170
#define ER_STACKSIZE_UNEXPECTED 10171
//#define OBSOLETE_ER_CANT_SET_DATADIR 10172
#define ER_CANT_STAT_DATADIR 10173
#define ER_CANT_CHOWN_DATADIR 10174
#define ER_CANT_SET_UP_PERSISTED_VALUES 10175
#define ER_CANT_SAVE_GTIDS 10176
//#define OBSOLETE_ER_AUTH_CANT_SET_DEFAULT_PLUGIN 10177
#define ER_CANT_JOIN_SHUTDOWN_THREAD 10178
#define ER_CANT_HASH_DO_AND_IGNORE_RULES 10179
#define ER_CANT_OPEN_CA 10180
#define ER_CANT_ACCESS_CAPATH 10181
#define ER_SSL_TRYING_DATADIR_DEFAULTS 10182
#define ER_AUTO_OPTIONS_FAILED 10183
#define ER_CANT_INIT_TIMER 10184
#define ER_SERVERID_TOO_LARGE 10185
#define ER_DEFAULT_SE_UNAVAILABLE 10186
#define ER_CANT_OPEN_ERROR_LOG 10187
#define ER_INVALID_ERROR_LOG_NAME 10188
#define ER_RPL_INFINITY_DENIED 10189
#define ER_RPL_INFINITY_IGNORED 10190
//#define OBSOLETE_ER_NDB_TABLES_NOT_READY 10191
#define ER_TABLE_CHECK_INTACT 10192
#define ER_DD_TABLESPACE_NOT_FOUND 10193
#define ER_DD_TRG_CONNECTION_COLLATION_MISSING 10194
#define ER_DD_TRG_DB_COLLATION_MISSING 10195
#define ER_DD_TRG_DEFINER_OOM 10196
#define ER_DD_TRG_FILE_UNREADABLE 10197
#define ER_TRG_CANT_PARSE 10198
#define ER_DD_TRG_CANT_ADD 10199
#define ER_DD_CANT_RESOLVE_VIEW 10200
#define ER_DD_VIEW_WITHOUT_DEFINER 10201
#define ER_PLUGIN_INIT_FAILED 10202
#define ER_RPL_TRX_DELEGATES_INIT_FAILED 10203
#define ER_RPL_BINLOG_STORAGE_DELEGATES_INIT_FAILED 10204
#define ER_RPL_BINLOG_TRANSMIT_DELEGATES_INIT_FAILED 10205
#define ER_RPL_BINLOG_RELAY_DELEGATES_INIT_FAILED 10206
#define ER_RPL_PLUGIN_FUNCTION_FAILED 10207
#define ER_SQL_HA_READ_FAILED 10208
#define ER_SR_BOGUS_VALUE 10209
#define ER_SR_INVALID_CONTEXT 10210
#define ER_READING_TABLE_FAILED 10211
#define ER_DES_FILE_WRONG_KEY 10212
//#define OBSOLETE_ER_CANT_SET_PERSISTED 10213
#define ER_JSON_PARSE_ERROR 10214
#define ER_CONFIG_OPTION_WITHOUT_GROUP 10215
#define ER_VALGRIND_DO_QUICK_LEAK_CHECK 10216
#define ER_VALGRIND_COUNT_LEAKS 10217
#define ER_LOAD_DATA_INFILE_FAILED_IN_UNEXPECTED_WAY 10218
#define ER_UNKNOWN_ERROR_NUMBER 10219
#define ER_UDF_CANT_ALLOC_FOR_STRUCTURES 10220
#define ER_UDF_CANT_ALLOC_FOR_FUNCTION 10221
#define ER_UDF_INVALID_ROW_IN_FUNCTION_TABLE 10222
#define ER_UDF_CANT_OPEN_FUNCTION_TABLE 10223
#define ER_XA_RECOVER_FOUND_TRX_IN_SE 10224
#define ER_XA_RECOVER_FOUND_XA_TRX 10225
//#define OBSOLETE_ER_XA_IGNORING_XID 10226
//#define OBSOLETE_ER_XA_COMMITTING_XID 10227
//#define OBSOLETE_ER_XA_ROLLING_BACK_XID 10228
#define ER_XA_STARTING_RECOVERY 10229
#define ER_XA_NO_MULTI_2PC_HEURISTIC_RECOVER 10230
#define ER_XA_RECOVER_EXPLANATION 10231
#define ER_XA_RECOVERY_DONE 10232
#define ER_TRX_GTID_COLLECT_REJECT 10233
#define ER_SQL_AUTHOR_DEFAULT_ROLES_FAIL 10234
#define ER_SQL_USER_TABLE_CREATE_WARNING 10235
#define ER_SQL_USER_TABLE_ALTER_WARNING 10236
#define ER_ROW_IN_WRONG_PARTITION_PLEASE_REPAIR 10237
#define ER_MYISAM_CRASHED_ERROR_IN_THREAD 10238
#define ER_MYISAM_CRASHED_ERROR_IN 10239
#define ER_TOO_MANY_STORAGE_ENGINES 10240
#define ER_SE_TYPECODE_CONFLICT 10241
#define ER_TRX_WRITE_SET_OOM 10242
#define ER_HANDLERTON_OOM 10243
#define ER_CONN_SHM_LISTENER 10244
#define ER_CONN_SHM_CANT_CREATE_SERVICE 10245
#define ER_CONN_SHM_CANT_CREATE_CONNECTION 10246
#define ER_CONN_PIP_CANT_CREATE_EVENT 10247
#define ER_CONN_PIP_CANT_CREATE_PIPE 10248
#define ER_CONN_PER_THREAD_NO_THREAD 10249
#define ER_CONN_TCP_NO_SOCKET 10250
#define ER_CONN_TCP_CREATED 10251
#define ER_CONN_TCP_ADDRESS 10252
#define ER_CONN_TCP_IPV6_AVAILABLE 10253
#define ER_CONN_TCP_IPV6_UNAVAILABLE 10254
#define ER_CONN_TCP_ERROR_WITH_STRERROR 10255
#define ER_CONN_TCP_CANT_RESOLVE_HOSTNAME 10256
#define ER_CONN_TCP_IS_THERE_ANOTHER_USING_PORT 10257
#define ER_CONN_UNIX_IS_THERE_ANOTHER_USING_SOCKET 10258
#define ER_CONN_UNIX_PID_CLAIMED_SOCKET_FILE 10259
#define ER_CONN_TCP_CANT_RESET_V6ONLY 10260
#define ER_CONN_TCP_BIND_RETRY 10261
#define ER_CONN_TCP_BIND_FAIL 10262
#define ER_CONN_TCP_IP_NOT_LOGGED 10263
#define ER_CONN_TCP_RESOLVE_INFO 10264
#define ER_CONN_TCP_START_FAIL 10265
#define ER_CONN_TCP_LISTEN_FAIL 10266
#define ER_CONN_UNIX_PATH_TOO_LONG 10267
#define ER_CONN_UNIX_LOCK_FILE_FAIL 10268
#define ER_CONN_UNIX_NO_FD 10269
#define ER_CONN_UNIX_NO_BIND_NO_START 10270
#define ER_CONN_UNIX_LISTEN_FAILED 10271
#define ER_CONN_UNIX_LOCK_FILE_GIVING_UP 10272
#define ER_CONN_UNIX_LOCK_FILE_CANT_CREATE 10273
#define ER_CONN_UNIX_LOCK_FILE_CANT_OPEN 10274
#define ER_CONN_UNIX_LOCK_FILE_CANT_READ 10275
#define ER_CONN_UNIX_LOCK_FILE_EMPTY 10276
#define ER_CONN_UNIX_LOCK_FILE_PIDLESS 10277
#define ER_CONN_UNIX_LOCK_FILE_CANT_WRITE 10278
#define ER_CONN_UNIX_LOCK_FILE_CANT_DELETE 10279
#define ER_CONN_UNIX_LOCK_FILE_CANT_SYNC 10280
#define ER_CONN_UNIX_LOCK_FILE_CANT_CLOSE 10281
#define ER_CONN_SOCKET_SELECT_FAILED 10282
#define ER_CONN_SOCKET_ACCEPT_FAILED 10283
#define ER_AUTH_RSA_CANT_FIND 10284
#define ER_AUTH_RSA_CANT_PARSE 10285
#define ER_AUTH_RSA_CANT_READ 10286
#define ER_AUTH_RSA_FILES_NOT_FOUND 10287
#define ER_CONN_ATTR_TRUNCATED 10288
#define ER_X509_CIPHERS_MISMATCH 10289
#define ER_X509_ISSUER_MISMATCH 10290
#define ER_X509_SUBJECT_MISMATCH 10291
#define ER_AUTH_CANT_ACTIVATE_ROLE 10292
#define ER_X509_NEEDS_RSA_PRIVKEY 10293
#define ER_X509_CANT_WRITE_KEY 10294
#define ER_X509_CANT_CHMOD_KEY 10295
#define ER_X509_CANT_READ_CA_KEY 10296
#define ER_X509_CANT_READ_CA_CERT 10297
#define ER_X509_CANT_CREATE_CERT 10298
#define ER_X509_CANT_WRITE_CERT 10299
#define ER_AUTH_CANT_CREATE_RSA_PAIR 10300
#define ER_AUTH_CANT_WRITE_PRIVKEY 10301
#define ER_AUTH_CANT_WRITE_PUBKEY 10302
#define ER_AUTH_SSL_CONF_PREVENTS_CERT_GENERATION 10303
#define ER_AUTH_USING_EXISTING_CERTS 10304
#define ER_AUTH_CERTS_SAVED_TO_DATADIR 10305
#define ER_AUTH_CERT_GENERATION_DISABLED 10306
#define ER_AUTH_RSA_CONF_PREVENTS_KEY_GENERATION 10307
#define ER_AUTH_KEY_GENERATION_SKIPPED_PAIR_PRESENT 10308
#define ER_AUTH_KEYS_SAVED_TO_DATADIR 10309
#define ER_AUTH_KEY_GENERATION_DISABLED 10310
#define ER_AUTHCACHE_PROXIES_PRIV_SKIPPED_NEEDS_RESOLVE 10311
#define ER_AUTHCACHE_PLUGIN_MISSING 10312
#define ER_AUTHCACHE_PLUGIN_CONFIG 10313
//#define OBSOLETE_ER_AUTHCACHE_ROLE_TABLES_DODGY 10314
#define ER_AUTHCACHE_USER_SKIPPED_NEEDS_RESOLVE 10315
#define ER_AUTHCACHE_USER_TABLE_DODGY 10316
#define ER_AUTHCACHE_USER_IGNORED_DEPRECATED_PASSWORD 10317
#define ER_AUTHCACHE_USER_IGNORED_NEEDS_PLUGIN 10318
#define ER_AUTHCACHE_USER_IGNORED_INVALID_PASSWORD 10319
#define ER_AUTHCACHE_EXPIRED_PASSWORD_UNSUPPORTED 10320
#define ER_NO_SUPER_WITHOUT_USER_PLUGIN 10321
#define ER_AUTHCACHE_DB_IGNORED_EMPTY_NAME 10322
#define ER_AUTHCACHE_DB_SKIPPED_NEEDS_RESOLVE 10323
#define ER_AUTHCACHE_DB_ENTRY_LOWERCASED_REVOKE_WILL_FAIL 10324
#define ER_AUTHCACHE_TABLE_PROXIES_PRIV_MISSING 10325
#define ER_AUTHCACHE_CANT_OPEN_AND_LOCK_PRIVILEGE_TABLES 10326
#define ER_AUTHCACHE_CANT_INIT_GRANT_SUBSYSTEM 10327
#define ER_AUTHCACHE_PROCS_PRIV_SKIPPED_NEEDS_RESOLVE 10328
#define ER_AUTHCACHE_PROCS_PRIV_ENTRY_IGNORED_BAD_ROUTINE_TYPE 10329
#define ER_AUTHCACHE_TABLES_PRIV_SKIPPED_NEEDS_RESOLVE 10330
#define ER_USER_NOT_IN_EXTRA_USERS_BINLOG_POSSIBLY_INCOMPLETE 10331
#define ER_DD_SCHEMA_NOT_FOUND 10332
#define ER_DD_TABLE_NOT_FOUND 10333
#define ER_DD_SE_INIT_FAILED 10334
#define ER_DD_ABORTING_PARTIAL_UPGRADE 10335
#define ER_DD_FRM_EXISTS_FOR_TABLE 10336
#define ER_DD_CREATED_FOR_UPGRADE 10337
#define ER_ERRMSG_CANT_FIND_FILE 10338
//#define OBSOLETE_ER_ERRMSG_LOADING_55_STYLE 10339
#define ER_ERRMSG_MISSING_IN_FILE 10340
#define ER_ERRMSG_OOM 10341
#define ER_ERRMSG_CANT_READ 10342
#define ER_TABLE_INCOMPATIBLE_DECIMAL_FIELD 10343
#define ER_TABLE_INCOMPATIBLE_YEAR_FIELD 10344
#define ER_INVALID_CHARSET_AND_DEFAULT_IS_MB 10345
#define ER_TABLE_WRONG_KEY_DEFINITION 10346
#define ER_CANT_OPEN_FRM_FILE 10347
#define ER_CANT_READ_FRM_FILE 10348
#define ER_TABLE_CREATED_WITH_DIFFERENT_VERSION 10349
#define ER_VIEW_UNPARSABLE 10350
#define ER_FILE_TYPE_UNKNOWN 10351
#define ER_INVALID_INFO_IN_FRM 10352
#define ER_CANT_OPEN_AND_LOCK_PRIVILEGE_TABLES 10353
#define ER_AUDIT_PLUGIN_DOES_NOT_SUPPORT_AUDIT_AUTH_EVENTS 10354
#define ER_AUDIT_PLUGIN_HAS_INVALID_DATA 10355
#define ER_TZ_OOM_INITIALIZING_TIME_ZONES 10356
#define ER_TZ_CANT_OPEN_AND_LOCK_TIME_ZONE_TABLE 10357
#define ER_TZ_OOM_LOADING_LEAP_SECOND_TABLE 10358
#define ER_TZ_TOO_MANY_LEAPS_IN_LEAP_SECOND_TABLE 10359
#define ER_TZ_ERROR_LOADING_LEAP_SECOND_TABLE 10360
#define ER_TZ_UNKNOWN_OR_ILLEGAL_DEFAULT_TIME_ZONE 10361
#define ER_TZ_CANT_FIND_DESCRIPTION_FOR_TIME_ZONE 10362
#define ER_TZ_CANT_FIND_DESCRIPTION_FOR_TIME_ZONE_ID 10363
#define ER_TZ_TRANSITION_TYPE_TABLE_TYPE_TOO_LARGE 10364
#define ER_TZ_TRANSITION_TYPE_TABLE_ABBREVIATIONS_EXCEED_SPACE 10365
#define ER_TZ_TRANSITION_TYPE_TABLE_LOAD_ERROR 10366
#define ER_TZ_TRANSITION_TABLE_TOO_MANY_TRANSITIONS 10367
#define ER_TZ_TRANSITION_TABLE_BAD_TRANSITION_TYPE 10368
#define ER_TZ_TRANSITION_TABLE_LOAD_ERROR 10369
#define ER_TZ_NO_TRANSITION_TYPES_IN_TIME_ZONE 10370
#define ER_TZ_OOM_LOADING_TIME_ZONE_DESCRIPTION 10371
#define ER_TZ_CANT_BUILD_MKTIME_MAP 10372
#define ER_TZ_OOM_WHILE_LOADING_TIME_ZONE 10373
#define ER_TZ_OOM_WHILE_SETTING_TIME_ZONE 10374
#define ER_REPLICA_SQL_THREAD_STOPPED_UNTIL_CONDITION_BAD 10375
#define ER_REPLICA_SQL_THREAD_STOPPED_UNTIL_POSITION_REACHED 10376
#define ER_REPLICA_SQL_THREAD_STOPPED_BEFORE_GTIDS_ALREADY_APPLIED 10377
#define ER_REPLICA_SQL_THREAD_STOPPED_BEFORE_GTIDS_REACHED 10378
#define ER_REPLICA_SQL_THREAD_STOPPED_AFTER_GTIDS_REACHED 10379
#define ER_REPLICA_SQL_THREAD_STOPPED_GAP_TRX_PROCESSED 10380
#define ER_GROUP_REPLICATION_PLUGIN_NOT_INSTALLED 10381
#define ER_GTID_ALREADY_ADDED_BY_USER 10382
#define ER_FAILED_TO_DELETE_FROM_GTID_EXECUTED_TABLE 10383
#define ER_FAILED_TO_COMPRESS_GTID_EXECUTED_TABLE 10384
#define ER_FAILED_TO_COMPRESS_GTID_EXECUTED_TABLE_OOM 10385
#define ER_FAILED_TO_INIT_THREAD_ATTR_FOR_GTID_TABLE_COMPRESSION 10386
#define ER_FAILED_TO_CREATE_GTID_TABLE_COMPRESSION_THREAD 10387
#define ER_FAILED_TO_JOIN_GTID_TABLE_COMPRESSION_THREAD 10388
#define ER_NPIPE_FAILED_TO_INIT_SECURITY_DESCRIPTOR 10389
#define ER_NPIPE_FAILED_TO_SET_SECURITY_DESCRIPTOR 10390
#define ER_NPIPE_PIPE_ALREADY_IN_USE 10391
//#define OBSOLETE_ER_NDB_SLAVE_SAW_EPOCH_LOWER_THAN_PREVIOUS_ON_START 10392
//#define OBSOLETE_ER_NDB_SLAVE_SAW_EPOCH_LOWER_THAN_PREVIOUS 10393
//#define OBSOLETE_ER_NDB_SLAVE_SAW_ALREADY_COMMITTED_EPOCH 10394
//#define OBSOLETE_ER_NDB_SLAVE_PREVIOUS_EPOCH_NOT_COMMITTED 10395
//#define OBSOLETE_ER_NDB_SLAVE_MISSING_DATA_FOR_TIMESTAMP_COLUMN 10396
//#define OBSOLETE_ER_NDB_SLAVE_LOGGING_EXCEPTIONS_TO 10397
//#define OBSOLETE_ER_NDB_SLAVE_LOW_EPOCH_RESOLUTION 10398
//#define OBSOLETE_ER_NDB_INFO_FOUND_UNEXPECTED_FIELD_TYPE 10399
//#define OBSOLETE_ER_NDB_INFO_FAILED_TO_CREATE_NDBINFO 10400
//#define OBSOLETE_ER_NDB_INFO_FAILED_TO_INIT_NDBINFO 10401
//#define OBSOLETE_ER_NDB_CLUSTER_WRONG_NUMBER_OF_FUNCTION_ARGUMENTS 10402
//#define OBSOLETE_ER_NDB_CLUSTER_SCHEMA_INFO 10403
//#define OBSOLETE_ER_NDB_CLUSTER_GENERIC_MESSAGE 10404
#define ER_RPL_CANT_OPEN_INFO_TABLE 10405
#define ER_RPL_CANT_SCAN_INFO_TABLE 10406
#define ER_RPL_CORRUPTED_INFO_TABLE 10407
#define ER_RPL_CORRUPTED_KEYS_IN_INFO_TABLE 10408
#define ER_RPL_WORKER_ID_IS 10409
#define ER_RPL_INCONSISTENT_TIMESTAMPS_IN_TRX 10410
#define ER_RPL_INCONSISTENT_SEQUENCE_NO_IN_TRX 10411
//#define OBSOLETE_ER_RPL_CHANNELS_REQUIRE_TABLES_AS_INFO_REPOSITORIES 10412
#define ER_RPL_CHANNELS_REQUIRE_NON_ZERO_SERVER_ID 10413
//#define OBSOLETE_ER_RPL_REPO_SHOULD_BE_TABLE 10414
#define ER_RPL_ERROR_CREATING_CONNECTION_METADATA 10415
//#define OBSOLETE_ER_RPL_ERROR_CHANGING_CONNECTION_METADATA_REPO_TYPE 10416
//#define OBSOLETE_ER_RPL_CHANGING_AM_TYPE_FAILED_DUE_TO_GAPS 10417
#define ER_RPL_ERROR_CREATING_APPLIER_METADATA 10418
//#define OBSOLETE_ER_RPL_ERROR_CHANGING_APPLIER_METADATA_REPO_TYPE 10419
#define ER_RPL_FAILED_TO_DELETE_FROM_REPLICA_WORKERS_INFO_REPOSITORY 10420
#define ER_RPL_FAILED_TO_RESET_STATE_IN_REPLICA_INFO_REPOSITORY 10421
//#define OBSOLETE_ER_RPL_ERROR_CHECKING_REPOSITORY 10422
#define ER_RPL_REPLICA_GENERIC_MESSAGE 10423
#define ER_RPL_REPLICA_COULD_NOT_CREATE_CHANNEL_LIST 10424
#define ER_RPL_MULTISOURCE_REQUIRES_TABLE_TYPE_REPOSITORIES 10425
#define ER_RPL_REPLICA_FAILED_TO_INIT_A_CONNECTION_METADATA_STRUCTURE 10426
#define ER_RPL_REPLICA_FAILED_TO_INIT_CONNECTION_METADATA_STRUCTURE 10427
#define ER_RPL_REPLICA_FAILED_TO_CREATE_CHANNEL_FROM_CONNECTION_METADATA 10428
#define ER_RPL_FAILED_TO_CREATE_NEW_INFO_FILE 10429
#define ER_RPL_FAILED_TO_CREATE_CACHE_FOR_INFO_FILE 10430
#define ER_RPL_FAILED_TO_OPEN_INFO_FILE 10431
#define ER_RPL_GTID_MEMORY_FINALLY_AVAILABLE 10432
#define ER_SERVER_COST_UNKNOWN_COST_CONSTANT 10433
#define ER_SERVER_COST_INVALID_COST_CONSTANT 10434
#define ER_ENGINE_COST_UNKNOWN_COST_CONSTANT 10435
#define ER_ENGINE_COST_UNKNOWN_STORAGE_ENGINE 10436
#define ER_ENGINE_COST_INVALID_DEVICE_TYPE_FOR_SE 10437
#define ER_ENGINE_COST_INVALID_CONST_CONSTANT_FOR_SE_AND_DEVICE 10438
#define ER_SERVER_COST_FAILED_TO_READ 10439
#define ER_ENGINE_COST_FAILED_TO_READ 10440
#define ER_FAILED_TO_OPEN_COST_CONSTANT_TABLES 10441
#define ER_RPL_UNSUPPORTED_UNIGNORABLE_EVENT_IN_STREAM 10442
#define ER_RPL_GTID_LOG_EVENT_IN_STREAM 10443
#define ER_RPL_UNEXPECTED_BEGIN_IN_STREAM 10444
#define ER_RPL_UNEXPECTED_COMMIT_ROLLBACK_OR_XID_LOG_EVENT_IN_STREAM 10445
#define ER_RPL_UNEXPECTED_XA_ROLLBACK_IN_STREAM 10446
#define ER_EVENT_EXECUTION_FAILED_CANT_AUTHENTICATE_USER 10447
#define ER_EVENT_EXECUTION_FAILED_USER_LOST_EVEN_PRIVILEGE 10448
#define ER_EVENT_ERROR_DURING_COMPILATION 10449
#define ER_EVENT_DROPPING 10450
//#define OBSOLETE_ER_NDB_SCHEMA_GENERIC_MESSAGE 10451
#define ER_RPL_INCOMPATIBLE_DECIMAL_IN_RBR 10452
#define ER_INIT_ROOT_WITHOUT_PASSWORD 10453
#define ER_INIT_GENERATING_TEMP_PASSWORD_FOR_ROOT 10454
#define ER_INIT_CANT_OPEN_BOOTSTRAP_FILE 10455
#define ER_INIT_BOOTSTRAP_COMPLETE 10456
#define ER_INIT_DATADIR_NOT_EMPTY_WONT_INITIALIZE 10457
#define ER_INIT_DATADIR_EXISTS_WONT_INITIALIZE 10458
#define ER_INIT_DATADIR_EXISTS_AND_PATH_TOO_LONG_WONT_INITIALIZE 10459
#define ER_INIT_DATADIR_EXISTS_AND_NOT_WRITABLE_WONT_INITIALIZE 10460
#define ER_INIT_CREATING_DD 10461
#define ER_RPL_BINLOG_STARTING_DUMP 10462
#define ER_RPL_BINLOG_SOURCE_SENDS_HEARTBEAT 10463
#define ER_RPL_BINLOG_SKIPPING_REMAINING_HEARTBEAT_INFO 10464
#define ER_RPL_BINLOG_SOURCE_USES_CHECKSUM_AND_REPLICA_CANT 10465
//#define OBSOLETE_ER_NDB_QUERY_FAILED 10466
#define ER_KILLING_THREAD 10467
#define ER_DETACHING_SESSION_LEFT_BY_PLUGIN 10468
#define ER_CANT_DETACH_SESSION_LEFT_BY_PLUGIN 10469
#define ER_DETACHED_SESSIONS_LEFT_BY_PLUGIN 10470
#define ER_FAILED_TO_DECREMENT_NUMBER_OF_THREADS 10471
#define ER_PLUGIN_DID_NOT_DEINITIALIZE_THREADS 10472
#define ER_KILLED_THREADS_OF_PLUGIN 10473
//#define OBSOLETE_ER_NDB_SLAVE_MAX_REPLICATED_EPOCH_UNKNOWN 10474
//#define OBSOLETE_ER_NDB_SLAVE_MAX_REPLICATED_EPOCH_SET_TO 10475
//#define OBSOLETE_ER_NDB_NODE_ID_AND_MANAGEMENT_SERVER_INFO 10476
//#define OBSOLETE_ER_NDB_DISCONNECT_INFO 10477
//#define OBSOLETE_ER_NDB_COLUMN_DEFAULTS_DIFFER 10478
//#define OBSOLETE_ER_NDB_COLUMN_SHOULD_NOT_HAVE_NATIVE_DEFAULT 10479
//#define OBSOLETE_ER_NDB_FIELD_INFO 10480
//#define OBSOLETE_ER_NDB_COLUMN_INFO 10481
//#define OBSOLETE_ER_NDB_OOM_IN_FIX_UNIQUE_INDEX_ATTR_ORDER 10482
//#define OBSOLETE_ER_NDB_SLAVE_MALFORMED_EVENT_RECEIVED_ON_TABLE 10483
//#define OBSOLETE_ER_NDB_SLAVE_CONFLICT_FUNCTION_REQUIRES_ROLE 10484
//#define OBSOLETE_ER_NDB_SLAVE_CONFLICT_TRANSACTION_IDS 10485
//#define OBSOLETE_ER_NDB_SLAVE_BINLOG_MISSING_INFO_FOR_CONFLICT_DETECTION 10486
//#define OBSOLETE_ER_NDB_ERROR_IN_READAUTOINCREMENTVALUE 10487
//#define OBSOLETE_ER_NDB_FOUND_UNCOMMITTED_AUTOCOMMIT 10488
//#define OBSOLETE_ER_NDB_SLAVE_TOO_MANY_RETRIES 10489
//#define OBSOLETE_ER_NDB_SLAVE_ERROR_IN_UPDATE_CREATE_INFO 10490
//#define OBSOLETE_ER_NDB_SLAVE_CANT_ALLOCATE_TABLE_SHARE 10491
//#define OBSOLETE_ER_NDB_BINLOG_ERROR_INFO_FROM_DA 10492
//#define OBSOLETE_ER_NDB_BINLOG_CREATE_TABLE_EVENT 10493
//#define OBSOLETE_ER_NDB_BINLOG_FAILED_CREATE_TABLE_EVENT_OPERATIONS 10494
//#define OBSOLETE_ER_NDB_BINLOG_RENAME_EVENT 10495
//#define OBSOLETE_ER_NDB_BINLOG_FAILED_CREATE_DURING_RENAME 10496
//#define OBSOLETE_ER_NDB_UNEXPECTED_RENAME_TYPE 10497
//#define OBSOLETE_ER_NDB_ERROR_IN_GET_AUTO_INCREMENT 10498
//#define OBSOLETE_ER_NDB_CREATING_SHARE_IN_OPEN 10499
//#define OBSOLETE_ER_NDB_TABLE_OPENED_READ_ONLY 10500
//#define OBSOLETE_ER_NDB_INITIALIZE_GIVEN_CLUSTER_PLUGIN_DISABLED 10501
//#define OBSOLETE_ER_NDB_BINLOG_FORMAT_CHANGED_FROM_STMT_TO_MIXED 10502
//#define OBSOLETE_ER_NDB_TRAILING_SHARE_RELEASED_BY_CLOSE_CACHED_TABLES 10503
//#define OBSOLETE_ER_NDB_SHARE_ALREADY_EXISTS 10504
//#define OBSOLETE_ER_NDB_HANDLE_TRAILING_SHARE_INFO 10505
//#define OBSOLETE_ER_NDB_CLUSTER_GET_SHARE_INFO 10506
//#define OBSOLETE_ER_NDB_CLUSTER_REAL_FREE_SHARE_INFO 10507
//#define OBSOLETE_ER_NDB_CLUSTER_REAL_FREE_SHARE_DROP_FAILED 10508
//#define OBSOLETE_ER_NDB_CLUSTER_FREE_SHARE_INFO 10509
//#define OBSOLETE_ER_NDB_CLUSTER_MARK_SHARE_DROPPED_INFO 10510
//#define OBSOLETE_ER_NDB_CLUSTER_MARK_SHARE_DROPPED_DESTROYING_SHARE 10511
//#define OBSOLETE_ER_NDB_CLUSTER_OOM_THD_NDB 10512
//#define OBSOLETE_ER_NDB_BINLOG_NDB_TABLES_INITIALLY_READ_ONLY 10513
//#define OBSOLETE_ER_NDB_UTIL_THREAD_OOM 10514
//#define OBSOLETE_ER_NDB_ILLEGAL_VALUE_FOR_NDB_RECV_THREAD_CPU_MASK 10515
//#define OBSOLETE_ER_NDB_TOO_MANY_CPUS_IN_NDB_RECV_THREAD_CPU_MASK 10516
#define ER_DBUG_CHECK_SHARES_OPEN 10517
#define ER_DBUG_CHECK_SHARES_INFO 10518
#define ER_DBUG_CHECK_SHARES_DROPPED 10519
#define ER_INVALID_OR_OLD_TABLE_OR_DB_NAME 10520
#define ER_TC_RECOVERING_AFTER_CRASH_USING 10521
#define ER_TC_CANT_AUTO_RECOVER_WITH_TC_HEURISTIC_RECOVER 10522
#define ER_TC_BAD_MAGIC_IN_TC_LOG 10523
#define ER_TC_NEED_N_SE_SUPPORTING_2PC_FOR_RECOVERY 10524
#define ER_TC_RECOVERY_FAILED_THESE_ARE_YOUR_OPTIONS 10525
#define ER_TC_HEURISTIC_RECOVERY_MODE 10526
#define ER_TC_HEURISTIC_RECOVERY_FAILED 10527
#define ER_TC_RESTART_WITHOUT_TC_HEURISTIC_RECOVER 10528
#define ER_RPL_REPLICA_FAILED_TO_CREATE_OR_RECOVER_INFO_REPOSITORIES 10529
#define ER_RPL_REPLICA_AUTO_POSITION_IS_1_AND_GTID_MODE_IS_OFF 10530
#define ER_RPL_REPLICA_CANT_START_REPLICA_FOR_CHANNEL 10531
#define ER_RPL_REPLICA_CANT_STOP_REPLICA_FOR_CHANNEL 10532
#define ER_RPL_RECOVERY_NO_ROTATE_EVENT_FROM_SOURCE 10533
#define ER_RPL_RECOVERY_ERROR_READ_RELAY_LOG 10534
//#define OBSOLETE_ER_RPL_RECOVERY_ERROR_FREEING_IO_CACHE 10535
#define ER_RPL_RECOVERY_SKIPPED_GROUP_REPLICATION_CHANNEL 10536
#define ER_RPL_RECOVERY_ERROR 10537
#define ER_RPL_RECOVERY_IO_ERROR_READING_RELAY_LOG_INDEX 10538
#define ER_RPL_RECOVERY_FILE_SOURCE_POS_INFO 10539
#define ER_RPL_RECOVERY_REPLICATE_SAME_SERVER_ID_REQUIRES_POSITION 10540
#define ER_RPL_MTA_RECOVERY_STARTING_COORDINATOR 10541
#define ER_RPL_MTA_RECOVERY_FAILED_TO_START_COORDINATOR 10542
#define ER_RPL_MTA_AUTOMATIC_RECOVERY_FAILED 10543
#define ER_RPL_MTA_RECOVERY_CANT_OPEN_RELAY_LOG 10544
#define ER_RPL_MTA_RECOVERY_SUCCESSFUL 10545
#define ER_RPL_SERVER_ID_MISSING 10546
#define ER_RPL_CANT_CREATE_REPLICA_THREAD 10547
#define ER_RPL_REPLICA_IO_THREAD_WAS_KILLED 10548
//#define OBSOLETE_ER_RPL_REPLICA_SOURCE_UUID_HAS_CHANGED 10549
#define ER_RPL_REPLICA_USES_CHECKSUM_AND_SOURCE_PRE_50 10550
#define ER_RPL_REPLICA_SECONDS_BEHIND_SOURCE_DUBIOUS 10551
#define ER_RPL_REPLICA_CANT_FLUSH_CONNECTION_METADATA_REPOS 10552
#define ER_RPL_REPLICA_REPORT_HOST_TOO_LONG 10553
#define ER_RPL_REPLICA_REPORT_USER_TOO_LONG 10554
#define ER_RPL_REPLICA_REPORT_PASSWORD_TOO_LONG 10555
#define ER_RPL_REPLICA_ERROR_RETRYING 10556
#define ER_RPL_REPLICA_ERROR_READING_FROM_SERVER 10557
#define ER_RPL_REPLICA_DUMP_THREAD_KILLED_BY_SOURCE 10558
#define ER_RPL_MTA_STATISTICS 10559
#define ER_RPL_MTA_RECOVERY_COMPLETE 10560
#define ER_RPL_REPLICA_CANT_INIT_RELAY_LOG_POSITION 10561
//#define OBSOLETE_ER_RPL_REPLICA_CONNECTED_TO_SOURCE_REPLICATION_STARTED 10562
#define ER_RPL_REPLICA_IO_THREAD_KILLED 10563
#define ER_RPL_REPLICA_IO_THREAD_CANT_REGISTER_ON_SOURCE 10564
#define ER_RPL_REPLICA_FORCING_TO_RECONNECT_IO_THREAD 10565
#define ER_RPL_REPLICA_ERROR_REQUESTING_BINLOG_DUMP 10566
#define ER_RPL_LOG_ENTRY_EXCEEDS_REPLICA_MAX_ALLOWED_PACKET 10567
#define ER_RPL_REPLICA_STOPPING_AS_SOURCE_OOM 10568
#define ER_RPL_REPLICA_IO_THREAD_ABORTED_WAITING_FOR_RELAY_LOG_SPACE 10569
#define ER_RPL_REPLICA_IO_THREAD_EXITING 10570
#define ER_RPL_REPLICA_CANT_INITIALIZE_REPLICA_WORKER 10571
#define ER_RPL_MTA_GROUP_RECOVERY_APPLIER_METADATA_FOR_WORKER 10572
#define ER_RPL_ERROR_LOOKING_FOR_LOG 10573
#define ER_RPL_MTA_GROUP_RECOVERY_APPLIER_METADATA 10574
#define ER_RPL_CANT_FIND_FOLLOWUP_FILE 10575
#define ER_RPL_MTA_CHECKPOINT_PERIOD_DIFFERS_FROM_CNT 10576
#define ER_RPL_REPLICA_WORKER_THREAD_CREATION_FAILED 10577
#define ER_RPL_REPLICA_WORKER_THREAD_CREATION_FAILED_WITH_ERRNO 10578
#define ER_RPL_REPLICA_FAILED_TO_INIT_PARTITIONS_HASH 10579
//#define OBSOLETE_ER_RPL_SLAVE_NDB_TABLES_NOT_AVAILABLE 10580
#define ER_RPL_REPLICA_SQL_THREAD_STARTING 10581
#define ER_RPL_REPLICA_SKIP_COUNTER_EXECUTED 10582
#define ER_RPL_REPLICA_ADDITIONAL_ERROR_INFO_FROM_DA 10583
#define ER_RPL_REPLICA_ERROR_INFO_FROM_DA 10584
#define ER_RPL_REPLICA_ERROR_LOADING_USER_DEFINED_LIBRARY 10585
#define ER_RPL_REPLICA_ERROR_RUNNING_QUERY 10586
#define ER_RPL_REPLICA_SQL_THREAD_EXITING 10587
#define ER_RPL_REPLICA_READ_INVALID_EVENT_FROM_SOURCE 10588
#define ER_RPL_REPLICA_QUEUE_EVENT_FAILED_INVALID_CONFIGURATION 10589
#define ER_RPL_REPLICA_IO_THREAD_DETECTED_UNEXPECTED_EVENT_SEQUENCE 10590
#define ER_RPL_REPLICA_CANT_USE_CHARSET 10591
#define ER_RPL_REPLICA_CONNECTED_TO_SOURCE_REPLICATION_RESUMED 10592
#define ER_RPL_REPLICA_NEXT_LOG_IS_ACTIVE 10593
#define ER_RPL_REPLICA_NEXT_LOG_IS_INACTIVE 10594
#define ER_RPL_REPLICA_SQL_THREAD_IO_ERROR_READING_EVENT 10595
#define ER_RPL_REPLICA_ERROR_READING_RELAY_LOG_EVENTS 10596
#define ER_REPLICA_CHANGE_SOURCE_TO_EXECUTED 10597
#define ER_RPL_REPLICA_NEW_C_M_NEEDS_REPOS_TYPE_OTHER_THAN_FILE 10598
#define ER_RPL_FAILED_TO_STAT_LOG_IN_INDEX 10599
#define ER_RPL_LOG_NOT_FOUND_WHILE_COUNTING_RELAY_LOG_SPACE 10600
#define ER_REPLICA_CANT_USE_TEMPDIR 10601
#define ER_RPL_RELAY_LOG_NEEDS_FILE_NOT_DIRECTORY 10602
#define ER_RPL_RELAY_LOG_INDEX_NEEDS_FILE_NOT_DIRECTORY 10603
#define ER_RPL_PLEASE_USE_OPTION_RELAY_LOG 10604
#define ER_RPL_OPEN_INDEX_FILE_FAILED 10605
#define ER_RPL_CANT_INITIALIZE_GTID_SETS_IN_AM_INIT_INFO 10606
#define ER_RPL_CANT_OPEN_LOG_IN_AM_INIT_INFO 10607
#define ER_RPL_ERROR_WRITING_RELAY_LOG_CONFIGURATION 10608
//#define OBSOLETE_ER_NDB_OOM_GET_NDB_BLOBS_VALUE 10609
//#define OBSOLETE_ER_NDB_THREAD_TIMED_OUT 10610
//#define OBSOLETE_ER_NDB_TABLE_IS_NOT_DISTRIBUTED 10611
//#define OBSOLETE_ER_NDB_CREATING_TABLE 10612
//#define OBSOLETE_ER_NDB_FLUSHING_TABLE_INFO 10613
//#define OBSOLETE_ER_NDB_CLEANING_STRAY_TABLES 10614
//#define OBSOLETE_ER_NDB_DISCOVERED_MISSING_DB 10615
//#define OBSOLETE_ER_NDB_DISCOVERED_REMAINING_DB 10616
//#define OBSOLETE_ER_NDB_CLUSTER_FIND_ALL_DBS_RETRY 10617
//#define OBSOLETE_ER_NDB_CLUSTER_FIND_ALL_DBS_FAIL 10618
//#define OBSOLETE_ER_NDB_SKIPPING_SETUP_TABLE 10619
//#define OBSOLETE_ER_NDB_FAILED_TO_SET_UP_TABLE 10620
//#define OBSOLETE_ER_NDB_MISSING_FRM_DISCOVERING 10621
//#define OBSOLETE_ER_NDB_MISMATCH_IN_FRM_DISCOVERING 10622
//#define OBSOLETE_ER_NDB_BINLOG_CLEANING_UP_SETUP_LEFTOVERS 10623
//#define OBSOLETE_ER_NDB_WAITING_INFO 10624
//#define OBSOLETE_ER_NDB_WAITING_INFO_WITH_MAP 10625
//#define OBSOLETE_ER_NDB_TIMEOUT_WHILE_DISTRIBUTING 10626
//#define OBSOLETE_ER_NDB_NOT_WAITING_FOR_DISTRIBUTING 10627
//#define OBSOLETE_ER_NDB_DISTRIBUTED_INFO 10628
//#define OBSOLETE_ER_NDB_DISTRIBUTION_COMPLETE 10629
//#define OBSOLETE_ER_NDB_SCHEMA_DISTRIBUTION_FAILED 10630
//#define OBSOLETE_ER_NDB_SCHEMA_DISTRIBUTION_REPORTS_SUBSCRIBE 10631
//#define OBSOLETE_ER_NDB_SCHEMA_DISTRIBUTION_REPORTS_UNSUBSCRIBE 10632
//#define OBSOLETE_ER_NDB_BINLOG_CANT_DISCOVER_TABLE_FROM_SCHEMA_EVENT 10633
//#define OBSOLETE_ER_NDB_BINLOG_SIGNALLING_UNKNOWN_VALUE 10634
//#define OBSOLETE_ER_NDB_BINLOG_REPLY_TO 10635
//#define OBSOLETE_ER_NDB_BINLOG_CANT_RELEASE_SLOCK 10636
//#define OBSOLETE_ER_NDB_CANT_FIND_TABLE 10637
//#define OBSOLETE_ER_NDB_DISCARDING_EVENT_NO_OBJ 10638
//#define OBSOLETE_ER_NDB_DISCARDING_EVENT_ID_VERSION_MISMATCH 10639
//#define OBSOLETE_ER_NDB_CLEAR_SLOCK_INFO 10640
//#define OBSOLETE_ER_NDB_BINLOG_SKIPPING_LOCAL_TABLE 10641
//#define OBSOLETE_ER_NDB_BINLOG_ONLINE_ALTER_RENAME 10642
//#define OBSOLETE_ER_NDB_BINLOG_CANT_REOPEN_SHADOW_TABLE 10643
//#define OBSOLETE_ER_NDB_BINLOG_ONLINE_ALTER_RENAME_COMPLETE 10644
//#define OBSOLETE_ER_NDB_BINLOG_SKIPPING_DROP_OF_LOCAL_TABLE 10645
//#define OBSOLETE_ER_NDB_BINLOG_SKIPPING_RENAME_OF_LOCAL_TABLE 10646
//#define OBSOLETE_ER_NDB_BINLOG_SKIPPING_DROP_OF_TABLES 10647
//#define OBSOLETE_ER_NDB_BINLOG_GOT_DIST_PRIV_EVENT_FLUSHING_PRIVILEGES 10648
//#define OBSOLETE_ER_NDB_BINLOG_GOT_SCHEMA_EVENT 10649
//#define OBSOLETE_ER_NDB_BINLOG_SKIPPING_OLD_SCHEMA_OPERATION 10650
//#define OBSOLETE_ER_NDB_CLUSTER_FAILURE 10651
//#define OBSOLETE_ER_NDB_TABLES_INITIALLY_READ_ONLY_ON_RECONNECT 10652
//#define OBSOLETE_ER_NDB_IGNORING_UNKNOWN_EVENT 10653
//#define OBSOLETE_ER_NDB_BINLOG_OPENING_INDEX 10654
//#define OBSOLETE_ER_NDB_BINLOG_CANT_LOCK_NDB_BINLOG_INDEX 10655
//#define OBSOLETE_ER_NDB_BINLOG_INJECTING_RANDOM_WRITE_FAILURE 10656
//#define OBSOLETE_ER_NDB_BINLOG_CANT_WRITE_TO_NDB_BINLOG_INDEX 10657
//#define OBSOLETE_ER_NDB_BINLOG_WRITING_TO_NDB_BINLOG_INDEX 10658
//#define OBSOLETE_ER_NDB_BINLOG_CANT_COMMIT_TO_NDB_BINLOG_INDEX 10659
//#define OBSOLETE_ER_NDB_BINLOG_WRITE_INDEX_FAILED_AFTER_KILL 10660
//#define OBSOLETE_ER_NDB_BINLOG_USING_SERVER_ID_0_SLAVES_WILL_NOT 10661
//#define OBSOLETE_ER_NDB_SERVER_ID_RESERVED_OR_TOO_LARGE 10662
//#define OBSOLETE_ER_NDB_BINLOG_REQUIRES_V2_ROW_EVENTS 10663
//#define OBSOLETE_ER_NDB_BINLOG_STATUS_FORCING_FULL_USE_WRITE 10664
//#define OBSOLETE_ER_NDB_BINLOG_GENERIC_MESSAGE 10665
//#define OBSOLETE_ER_NDB_CONFLICT_GENERIC_MESSAGE 10666
//#define OBSOLETE_ER_NDB_TRANS_DEPENDENCY_TRACKER_ERROR 10667
//#define OBSOLETE_ER_NDB_CONFLICT_FN_PARSE_ERROR 10668
//#define OBSOLETE_ER_NDB_CONFLICT_FN_SETUP_ERROR 10669
//#define OBSOLETE_ER_NDB_BINLOG_FAILED_TO_GET_TABLE 10670
//#define OBSOLETE_ER_NDB_BINLOG_NOT_LOGGING 10671
//#define OBSOLETE_ER_NDB_BINLOG_CREATE_TABLE_EVENT_FAILED 10672
//#define OBSOLETE_ER_NDB_BINLOG_CREATE_TABLE_EVENT_INFO 10673
//#define OBSOLETE_ER_NDB_BINLOG_DISCOVER_TABLE_EVENT_INFO 10674
//#define OBSOLETE_ER_NDB_BINLOG_BLOB_REQUIRES_PK 10675
//#define OBSOLETE_ER_NDB_BINLOG_CANT_CREATE_EVENT_IN_DB 10676
//#define OBSOLETE_ER_NDB_BINLOG_CANT_CREATE_EVENT_IN_DB_AND_CANT_DROP 10677
//#define OBSOLETE_ER_NDB_BINLOG_CANT_CREATE_EVENT_IN_DB_DROPPED 10678
//#define OBSOLETE_ER_NDB_BINLOG_DISCOVER_REUSING_OLD_EVENT_OPS 10679
//#define OBSOLETE_ER_NDB_BINLOG_CREATING_NDBEVENTOPERATION_FAILED 10680
//#define OBSOLETE_ER_NDB_BINLOG_CANT_CREATE_BLOB 10681
//#define OBSOLETE_ER_NDB_BINLOG_NDBEVENT_EXECUTE_FAILED 10682
//#define OBSOLETE_ER_NDB_CREATE_EVENT_OPS_LOGGING_INFO 10683
//#define OBSOLETE_ER_NDB_BINLOG_CANT_DROP_EVENT_FROM_DB 10684
//#define OBSOLETE_ER_NDB_TIMED_OUT_IN_DROP_TABLE 10685
//#define OBSOLETE_ER_NDB_BINLOG_UNHANDLED_ERROR_FOR_TABLE 10686
//#define OBSOLETE_ER_NDB_BINLOG_CLUSTER_FAILURE 10687
//#define OBSOLETE_ER_NDB_BINLOG_UNKNOWN_NON_DATA_EVENT 10688
//#define OBSOLETE_ER_NDB_BINLOG_INJECTOR_DISCARDING_ROW_EVENT_METADATA 10689
//#define OBSOLETE_ER_NDB_REMAINING_OPEN_TABLES 10690
//#define OBSOLETE_ER_NDB_REMAINING_OPEN_TABLE_INFO 10691
//#define OBSOLETE_ER_NDB_COULD_NOT_GET_APPLY_STATUS_SHARE 10692
//#define OBSOLETE_ER_NDB_BINLOG_SERVER_SHUTDOWN_DURING_NDB_CLUSTER_START 10693
//#define OBSOLETE_ER_NDB_BINLOG_CLUSTER_RESTARTED_RESET_MASTER_SUGGESTED 10694
//#define OBSOLETE_ER_NDB_BINLOG_CLUSTER_HAS_RECONNECTED 10695
//#define OBSOLETE_ER_NDB_BINLOG_STARTING_LOG_AT_EPOCH 10696
//#define OBSOLETE_ER_NDB_BINLOG_NDB_TABLES_WRITABLE 10697
//#define OBSOLETE_ER_NDB_BINLOG_SHUTDOWN_DETECTED 10698
//#define OBSOLETE_ER_NDB_BINLOG_LOST_SCHEMA_CONNECTION_WAITING 10699
//#define OBSOLETE_ER_NDB_BINLOG_LOST_SCHEMA_CONNECTION_CONTINUING 10700
//#define OBSOLETE_ER_NDB_BINLOG_ERROR_HANDLING_SCHEMA_EVENT 10701
//#define OBSOLETE_ER_NDB_BINLOG_CANT_INJECT_APPLY_STATUS_WRITE_ROW 10702
//#define OBSOLETE_ER_NDB_BINLOG_ERROR_DURING_GCI_ROLLBACK 10703
//#define OBSOLETE_ER_NDB_BINLOG_ERROR_DURING_GCI_COMMIT 10704
//#define OBSOLETE_ER_NDB_BINLOG_LATEST_TRX_IN_EPOCH_NOT_IN_BINLOG 10705
//#define OBSOLETE_ER_NDB_BINLOG_RELEASING_EXTRA_SHARE_REFERENCES 10706
//#define OBSOLETE_ER_NDB_BINLOG_REMAINING_OPEN_TABLES 10707
//#define OBSOLETE_ER_NDB_BINLOG_REMAINING_OPEN_TABLE_INFO 10708
#define ER_TREE_CORRUPT_PARENT_SHOULD_POINT_AT_PARENT 10709
#define ER_TREE_CORRUPT_ROOT_SHOULD_BE_BLACK 10710
#define ER_TREE_CORRUPT_2_CONSECUTIVE_REDS 10711
#define ER_TREE_CORRUPT_RIGHT_IS_LEFT 10712
#define ER_TREE_CORRUPT_INCORRECT_BLACK_COUNT 10713
#define ER_WRONG_COUNT_FOR_ORIGIN 10714
#define ER_WRONG_COUNT_FOR_KEY 10715
#define ER_WRONG_COUNT_OF_ELEMENTS 10716
#define ER_RPL_ERROR_READING_REPLICA_WORKER_CONFIGURATION 10717
//#define OBSOLETE_ER_RPL_ERROR_WRITING_SLAVE_WORKER_CONFIGURATION 10718
#define ER_RPL_FAILED_TO_OPEN_RELAY_LOG 10719
#define ER_RPL_WORKER_CANT_READ_RELAY_LOG 10720
#define ER_RPL_WORKER_CANT_FIND_NEXT_RELAY_LOG 10721
#define ER_RPL_MTA_REPLICA_COORDINATOR_HAS_WAITED 10722
#define ER_BINLOG_FAILED_TO_WRITE_DROP_FOR_TEMP_TABLES 10723
#define ER_BINLOG_OOM_WRITING_DELETE_WHILE_OPENING_HEAP_TABLE 10724
#define ER_FAILED_TO_REPAIR_TABLE 10725
#define ER_FAILED_TO_REMOVE_TEMP_TABLE 10726
#define ER_SYSTEM_TABLE_NOT_TRANSACTIONAL 10727
#define ER_RPL_ERROR_WRITING_SOURCE_CONFIGURATION 10728
#define ER_RPL_ERROR_READING_SOURCE_CONFIGURATION 10729
#define ER_RPL_SSL_INFO_IN_CONNECTION_METADATA_IGNORED 10730
#define ER_PLUGIN_FAILED_DEINITIALIZATION 10731
#define ER_PLUGIN_HAS_NONZERO_REFCOUNT_AFTER_DEINITIALIZATION 10732
#define ER_PLUGIN_SHUTTING_DOWN_PLUGIN 10733
#define ER_PLUGIN_REGISTRATION_FAILED 10734
#define ER_PLUGIN_CANT_OPEN_PLUGIN_TABLE 10735
#define ER_PLUGIN_CANT_LOAD 10736
#define ER_PLUGIN_LOAD_PARAMETER_TOO_LONG 10737
#define ER_PLUGIN_FORCING_SHUTDOWN 10738
#define ER_PLUGIN_HAS_NONZERO_REFCOUNT_AFTER_SHUTDOWN 10739
#define ER_PLUGIN_UNKNOWN_VARIABLE_TYPE 10740
#define ER_PLUGIN_VARIABLE_SET_READ_ONLY 10741
#define ER_PLUGIN_VARIABLE_MISSING_NAME 10742
#define ER_PLUGIN_VARIABLE_NOT_ALLOCATED_THREAD_LOCAL 10743
#define ER_PLUGIN_OOM 10744
#define ER_PLUGIN_BAD_OPTIONS 10745
#define ER_PLUGIN_PARSING_OPTIONS_FAILED 10746
#define ER_PLUGIN_DISABLED 10747
#define ER_PLUGIN_HAS_CONFLICTING_SYSTEM_VARIABLES 10748
#define ER_PLUGIN_CANT_SET_PERSISTENT_OPTIONS 10749
#define ER_MY_NET_WRITE_FAILED_FALLING_BACK_ON_STDERR 10750
#define ER_RETRYING_REPAIR_WITHOUT_QUICK 10751
#define ER_RETRYING_REPAIR_WITH_KEYCACHE 10752
#define ER_FOUND_ROWS_WHILE_REPAIRING 10753
#define ER_ERROR_DURING_OPTIMIZE_TABLE 10754
#define ER_ERROR_ENABLING_KEYS 10755
#define ER_CHECKING_TABLE 10756
#define ER_RECOVERING_TABLE 10757
#define ER_CANT_CREATE_TABLE_SHARE_FROM_FRM 10758
#define ER_CANT_LOCK_TABLE 10759
#define ER_CANT_ALLOC_TABLE_OBJECT 10760
#define ER_CANT_CREATE_HANDLER_OBJECT_FOR_TABLE 10761
#define ER_CANT_SET_HANDLER_REFERENCE_FOR_TABLE 10762
#define ER_CANT_LOCK_TABLESPACE 10763
#define ER_CANT_UPGRADE_GENERATED_COLUMNS_TO_DD 10764
#define ER_DD_ERROR_CREATING_ENTRY 10765
#define ER_DD_CANT_FETCH_TABLE_DATA 10766
#define ER_DD_CANT_FIX_SE_DATA 10767
#define ER_DD_CANT_CREATE_SP 10768
#define ER_CANT_OPEN_DB_OPT_USING_DEFAULT_CHARSET 10769
#define ER_CANT_CREATE_CACHE_FOR_DB_OPT 10770
#define ER_CANT_IDENTIFY_CHARSET_USING_DEFAULT 10771
#define ER_DB_OPT_NOT_FOUND_USING_DEFAULT_CHARSET 10772
#define ER_EVENT_CANT_GET_TIMEZONE_FROM_FIELD 10773
#define ER_EVENT_CANT_FIND_TIMEZONE 10774
#define ER_EVENT_CANT_GET_CHARSET 10775
#define ER_EVENT_CANT_GET_COLLATION 10776
#define ER_EVENT_CANT_OPEN_TABLE_MYSQL_EVENT 10777
#define ER_CANT_PARSE_STORED_ROUTINE_BODY 10778
#define ER_CANT_OPEN_TABLE_MYSQL_PROC 10779
#define ER_CANT_READ_TABLE_MYSQL_PROC 10780
#define ER_FILE_EXISTS_DURING_UPGRADE 10781
#define ER_CANT_OPEN_DATADIR_AFTER_UPGRADE_FAILURE 10782
#define ER_CANT_SET_PATH_FOR 10783
#define ER_CANT_OPEN_DIR 10784
//#define OBSOLETE_ER_NDB_CLUSTER_CONNECTION_POOL_NODEIDS 10785
//#define OBSOLETE_ER_NDB_CANT_PARSE_NDB_CLUSTER_CONNECTION_POOL_NODEIDS 10786
//#define OBSOLETE_ER_NDB_INVALID_CLUSTER_CONNECTION_POOL_NODEIDS 10787
//#define OBSOLETE_ER_NDB_DUPLICATE_CLUSTER_CONNECTION_POOL_NODEIDS 10788
//#define OBSOLETE_ER_NDB_POOL_SIZE_CLUSTER_CONNECTION_POOL_NODEIDS 10789
//#define OBSOLETE_ER_NDB_NODEID_NOT_FIRST_CONNECTION_POOL_NODEIDS 10790
//#define OBSOLETE_ER_NDB_USING_NODEID 10791
//#define OBSOLETE_ER_NDB_CANT_ALLOC_GLOBAL_NDB_CLUSTER_CONNECTION 10792
//#define OBSOLETE_ER_NDB_CANT_ALLOC_GLOBAL_NDB_OBJECT 10793
//#define OBSOLETE_ER_NDB_USING_NODEID_LIST 10794
//#define OBSOLETE_ER_NDB_CANT_ALLOC_NDB_CLUSTER_CONNECTION 10795
//#define OBSOLETE_ER_NDB_STARTING_CONNECT_THREAD 10796
//#define OBSOLETE_ER_NDB_NODE_INFO 10797
//#define OBSOLETE_ER_NDB_CANT_START_CONNECT_THREAD 10798
//#define OBSOLETE_ER_NDB_GENERIC_ERROR 10799
//#define OBSOLETE_ER_NDB_CPU_MASK_TOO_SHORT 10800
#define ER_EVENT_ERROR_CREATING_QUERY_TO_WRITE_TO_BINLOG 10801
#define ER_EVENT_SCHEDULER_ERROR_LOADING_FROM_DB 10802
#define ER_EVENT_SCHEDULER_ERROR_GETTING_EVENT_OBJECT 10803
#define ER_EVENT_SCHEDULER_GOT_BAD_DATA_FROM_TABLE 10804
#define ER_EVENT_CANT_GET_LOCK_FOR_DROPPING_EVENT 10805
#define ER_EVENT_UNABLE_TO_DROP_EVENT 10806
//#define OBSOLETE_ER_BINLOG_ATTACHING_THREAD_MEMORY_FINALLY_AVAILABLE 10807
#define ER_BINLOG_CANT_RESIZE_CACHE 10808
#define ER_BINLOG_FILE_BEING_READ_NOT_PURGED 10809
#define ER_BINLOG_IO_ERROR_READING_HEADER 10810
//#define OBSOLETE_ER_BINLOG_CANT_OPEN_LOG 10811
//#define OBSOLETE_ER_BINLOG_CANT_CREATE_CACHE_FOR_LOG 10812
#define ER_BINLOG_FILE_EXTENSION_NUMBER_EXHAUSTED 10813
#define ER_BINLOG_FILE_NAME_TOO_LONG 10814
#define ER_BINLOG_FILE_EXTENSION_NUMBER_RUNNING_LOW 10815
#define ER_BINLOG_CANT_OPEN_FOR_LOGGING 10816
#define ER_BINLOG_FAILED_TO_SYNC_INDEX_FILE 10817
#define ER_BINLOG_ERROR_READING_GTIDS_FROM_RELAY_LOG 10818
#define ER_BINLOG_EVENTS_READ_FROM_APPLIER_METADATA 10819
#define ER_BINLOG_ERROR_READING_GTIDS_FROM_BINARY_LOG 10820
#define ER_BINLOG_EVENTS_READ_FROM_BINLOG_INFO 10821
#define ER_BINLOG_CANT_GENERATE_NEW_FILE_NAME 10822
#define ER_BINLOG_FAILED_TO_SYNC_INDEX_FILE_IN_OPEN 10823
#define ER_BINLOG_CANT_USE_FOR_LOGGING 10824
#define ER_BINLOG_FAILED_TO_CLOSE_INDEX_FILE_WHILE_REBUILDING 10825
#define ER_BINLOG_FAILED_TO_DELETE_INDEX_FILE_WHILE_REBUILDING 10826
#define ER_BINLOG_FAILED_TO_RENAME_INDEX_FILE_WHILE_REBUILDING 10827
#define ER_BINLOG_FAILED_TO_OPEN_INDEX_FILE_AFTER_REBUILDING 10828
#define ER_BINLOG_CANT_APPEND_LOG_TO_TMP_INDEX 10829
#define ER_BINLOG_CANT_LOCATE_OLD_BINLOG_OR_RELAY_LOG_FILES 10830
#define ER_BINLOG_CANT_DELETE_FILE 10831
#define ER_BINLOG_CANT_SET_TMP_INDEX_NAME 10832
#define ER_BINLOG_FAILED_TO_OPEN_TEMPORARY_INDEX_FILE 10833
//#define OBSOLETE_ER_BINLOG_ERROR_GETTING_NEXT_LOG_FROM_INDEX 10834
#define ER_BINLOG_CANT_OPEN_TMP_INDEX 10835
#define ER_BINLOG_CANT_COPY_INDEX_TO_TMP 10836
#define ER_BINLOG_CANT_CLOSE_TMP_INDEX 10837
#define ER_BINLOG_CANT_MOVE_TMP_TO_INDEX 10838
#define ER_BINLOG_PURGE_LOGS_CALLED_WITH_FILE_NOT_IN_INDEX 10839
#define ER_BINLOG_PURGE_LOGS_CANT_SYNC_INDEX_FILE 10840
#define ER_BINLOG_PURGE_LOGS_CANT_COPY_TO_REGISTER_FILE 10841
#define ER_BINLOG_PURGE_LOGS_CANT_FLUSH_REGISTER_FILE 10842
#define ER_BINLOG_PURGE_LOGS_CANT_UPDATE_INDEX_FILE 10843
#define ER_BINLOG_PURGE_LOGS_FAILED_TO_PURGE_LOG 10844
#define ER_BINLOG_FAILED_TO_SET_PURGE_INDEX_FILE_NAME 10845
#define ER_BINLOG_FAILED_TO_OPEN_REGISTER_FILE 10846
#define ER_BINLOG_FAILED_TO_REINIT_REGISTER_FILE 10847
#define ER_BINLOG_FAILED_TO_READ_REGISTER_FILE 10848
#define ER_CANT_STAT_FILE 10849
#define ER_BINLOG_CANT_DELETE_LOG_FILE_DOES_INDEX_MATCH_FILES 10850
#define ER_BINLOG_CANT_DELETE_FILE_AND_READ_BINLOG_INDEX 10851
#define ER_BINLOG_FAILED_TO_DELETE_LOG_FILE 10852
#define ER_BINLOG_LOGGING_INCIDENT_TO_STOP_REPLICAS 10853
#define ER_BINLOG_CANT_FIND_LOG_IN_INDEX 10854
#define ER_BINLOG_RECOVERING_AFTER_CRASH_USING 10855
#define ER_BINLOG_CANT_OPEN_CRASHED_BINLOG 10856
#define ER_BINLOG_CANT_TRIM_CRASHED_BINLOG 10857
#define ER_BINLOG_CRASHED_BINLOG_TRIMMED 10858
#define ER_BINLOG_CANT_CLEAR_IN_USE_FLAG_FOR_CRASHED_BINLOG 10859
#define ER_BINLOG_FAILED_TO_RUN_AFTER_SYNC_HOOK 10860
#define ER_TURNING_LOGGING_OFF_FOR_THE_DURATION 10861
#define ER_BINLOG_FAILED_TO_RUN_AFTER_FLUSH_HOOK 10862
//#define OBSOLETE_ER_BINLOG_CRASH_RECOVERY_FAILED 10863
#define ER_BINLOG_WARNING_SUPPRESSED 10864
#define ER_NDB_LOG_ENTRY 10865
#define ER_NDB_LOG_ENTRY_WITH_PREFIX 10866
//#define OBSOLETE_ER_NDB_BINLOG_CANT_CREATE_PURGE_THD 10867
#define ER_INNODB_UNKNOWN_COLLATION 10868
#define ER_INNODB_INVALID_LOG_GROUP_HOME_DIR 10869
#define ER_INNODB_INVALID_INNODB_UNDO_DIRECTORY 10870
#define ER_INNODB_ILLEGAL_COLON_IN_POOL 10871
#define ER_INNODB_INVALID_PAGE_SIZE 10872
#define ER_INNODB_DIRTY_WATER_MARK_NOT_LOW 10873
#define ER_INNODB_IO_CAPACITY_EXCEEDS_MAX 10874
#define ER_INNODB_FILES_SAME 10875
#define ER_INNODB_UNREGISTERED_TRX_ACTIVE 10876
#define ER_INNODB_CLOSING_CONNECTION_ROLLS_BACK 10877
#define ER_INNODB_TRX_XLATION_TABLE_OOM 10878
#define ER_INNODB_CANT_FIND_INDEX_IN_INNODB_DD 10879
#define ER_INNODB_INDEX_COLUMN_INFO_UNLIKE_MYSQLS 10880
//#define OBSOLETE_ER_INNODB_CANT_OPEN_TABLE 10881
#define ER_INNODB_CANT_BUILD_INDEX_XLATION_TABLE_FOR 10882
#define ER_INNODB_PK_NOT_IN_MYSQL 10883
#define ER_INNODB_PK_ONLY_IN_MYSQL 10884
#define ER_INNODB_CLUSTERED_INDEX_PRIVATE 10885
//#define OBSOLETE_ER_INNODB_PARTITION_TABLE_LOWERCASED 10886
#define ER_ERRMSG_REPLACEMENT_DODGY 10887
#define ER_ERRMSG_REPLACEMENTS_FAILED 10888
#define ER_NPIPE_CANT_CREATE 10889
#define ER_PARTITION_MOVE_CREATED_DUPLICATE_ROW_PLEASE_FIX 10890
#define ER_AUDIT_CANT_ABORT_COMMAND 10891
#define ER_AUDIT_CANT_ABORT_EVENT 10892
#define ER_AUDIT_WARNING 10893
//#define OBSOLETE_ER_NDB_NUMBER_OF_CHANNELS 10894
//#define OBSOLETE_ER_NDB_REPLICA_PARALLEL_WORKERS 10895
//#define OBSOLETE_ER_NDB_DISTRIBUTING_ERR 10896
#define ER_RPL_REPLICA_INSECURE_CHANGE_SOURCE 10897
//#define OBSOLETE_ER_RPL_SLAVE_FLUSH_RELAY_LOGS_NOT_ALLOWED 10898
#define ER_RPL_REPLICA_INCORRECT_CHANNEL 10899
#define ER_FAILED_TO_FIND_DL_ENTRY 10900
#define ER_FAILED_TO_OPEN_SHARED_LIBRARY 10901
#define ER_THREAD_PRIORITY_IGNORED 10902
#define ER_BINLOG_CACHE_SIZE_TOO_LARGE 10903
#define ER_BINLOG_STMT_CACHE_SIZE_TOO_LARGE 10904
#define ER_FAILED_TO_GENERATE_UNIQUE_LOGFILE 10905
#define ER_FAILED_TO_READ_FILE 10906
#define ER_FAILED_TO_WRITE_TO_FILE 10907
#define ER_BINLOG_UNSAFE_MESSAGE_AND_STATEMENT 10908
#define ER_FORCE_CLOSE_THREAD 10909
#define ER_SERVER_SHUTDOWN_COMPLETE 10910
#define ER_RPL_CANT_HAVE_SAME_BASENAME 10911
#define ER_RPL_GTID_MODE_REQUIRES_ENFORCE_GTID_CONSISTENCY_ON 10912
#define ER_WARN_NO_SERVERID_SPECIFIED 10913
#define ER_ABORTING_USER_CONNECTION 10914
#define ER_SQL_MODE_MERGED_WITH_STRICT_MODE 10915
#define ER_GTID_PURGED_WAS_UPDATED 10916
#define ER_GTID_EXECUTED_WAS_UPDATED 10917
#define ER_DEPRECATE_MSG_WITH_REPLACEMENT 10918
#define ER_TRG_CREATION_CTX_NOT_SET 10919
#define ER_FILE_HAS_OLD_FORMAT 10920
#define ER_VIEW_CREATION_CTX_NOT_SET 10921
//#define OBSOLETE_ER_TABLE_NAME_CAUSES_TOO_LONG_PATH 10922
#define ER_TABLE_UPGRADE_REQUIRED 10923
#define ER_GET_ERRNO_FROM_STORAGE_ENGINE 10924
#define ER_ACCESS_DENIED_ERROR_WITHOUT_PASSWORD 10925
#define ER_ACCESS_DENIED_ERROR_WITH_PASSWORD 10926
#define ER_ACCESS_DENIED_FOR_USER_ACCOUNT_LOCKED 10927
//#define OBSOLETE_ER_MUST_CHANGE_EXPIRED_PASSWORD 10928
#define ER_SYSTEM_TABLES_NOT_SUPPORTED_BY_STORAGE_ENGINE 10929
//#define OBSOLETE_ER_FILESORT_TERMINATED 10930
#define ER_SERVER_STARTUP_MSG 10931
#define ER_FAILED_TO_FIND_LOCALE_NAME 10932
#define ER_FAILED_TO_FIND_COLLATION_NAME 10933
#define ER_SERVER_OUT_OF_RESOURCES 10934
#define ER_SERVER_OUTOFMEMORY 10935
#define ER_INVALID_COLLATION_FOR_CHARSET 10936
#define ER_CANT_START_ERROR_LOG_SERVICE 10937
#define ER_CREATING_NEW_UUID_FIRST_START 10938
#define ER_FAILED_TO_GET_ABSOLUTE_PATH 10939
#define ER_PERFSCHEMA_COMPONENTS_INFRASTRUCTURE_BOOTSTRAP 10940
#define ER_PERFSCHEMA_COMPONENTS_INFRASTRUCTURE_SHUTDOWN 10941
#define ER_DUP_FD_OPEN_FAILED 10942
#define ER_SYSTEM_VIEW_INIT_FAILED 10943
#define ER_RESOURCE_GROUP_POST_INIT_FAILED 10944
#define ER_RESOURCE_GROUP_SUBSYSTEM_INIT_FAILED 10945
#define ER_FAILED_START_MYSQLD_DAEMON 10946
#define ER_CANNOT_CHANGE_TO_ROOT_DIR 10947
#define ER_PERSISTENT_PRIVILEGES_BOOTSTRAP 10948
#define ER_BASEDIR_SET_TO 10949
#define ER_RPL_FILTER_ADD_WILD_DO_TABLE_FAILED 10950
#define ER_RPL_FILTER_ADD_WILD_IGNORE_TABLE_FAILED 10951
#define ER_PRIVILEGE_SYSTEM_INIT_FAILED 10952
#define ER_CANNOT_SET_LOG_ERROR_SERVICES 10953
#define ER_PERFSCHEMA_TABLES_INIT_FAILED 10954
//#define OBSOLETE_ER_TX_EXTRACTION_ALGORITHM_FOR_BINLOG_TX_DEPEDENCY 10955
//#define OBSOLETE_ER_INVALID_REPLICATION_TIMESTAMPS 10956
//#define OBSOLETE_ER_RPL_TIMESTAMPS_RETURNED_TO_NORMAL 10957
#define ER_BINLOG_FILE_OPEN_FAILED 10958
#define ER_BINLOG_EVENT_WRITE_TO_STMT_CACHE_FAILED 10959
#define ER_REPLICA_RELAY_LOG_TRUNCATE_INFO 10960
#define ER_REPLICA_RELAY_LOG_PURGE_FAILED 10961
#define ER_RPL_REPLICA_FILTER_CREATE_FAILED 10962
#define ER_RPL_REPLICA_GLOBAL_FILTERS_COPY_FAILED 10963
#define ER_RPL_REPLICA_RESET_FILTER_OPTIONS 10964
#define ER_MISSING_GRANT_SYSTEM_TABLE 10965
#define ER_MISSING_ACL_SYSTEM_TABLE 10966
#define ER_ANONYMOUS_AUTH_ID_NOT_ALLOWED_IN_MANDATORY_ROLES 10967
#define ER_UNKNOWN_AUTH_ID_IN_MANDATORY_ROLE 10968
#define ER_WRITE_ROW_TO_PARTITION_FAILED 10969
#define ER_RESOURCE_GROUP_METADATA_UPDATE_SKIPPED 10970
#define ER_FAILED_TO_PERSIST_RESOURCE_GROUP_METADATA 10971
#define ER_FAILED_TO_DESERIALIZE_RESOURCE_GROUP 10972
#define ER_FAILED_TO_UPDATE_RESOURCE_GROUP 10973
#define ER_RESOURCE_GROUP_VALIDATION_FAILED 10974
#define ER_FAILED_TO_ALLOCATE_MEMORY_FOR_RESOURCE_GROUP 10975
#define ER_FAILED_TO_ALLOCATE_MEMORY_FOR_RESOURCE_GROUP_HASH 10976
#define ER_FAILED_TO_ADD_RESOURCE_GROUP_TO_MAP 10977
#define ER_RESOURCE_GROUP_IS_DISABLED 10978
#define ER_FAILED_TO_APPLY_RESOURCE_GROUP_CONTROLLER 10979
#define ER_FAILED_TO_ACQUIRE_LOCK_ON_RESOURCE_GROUP 10980
#define ER_PFS_NOTIFICATION_FUNCTION_REGISTER_FAILED 10981
#define ER_RES_GRP_SET_THR_AFFINITY_FAILED 10982
#define ER_RES_GRP_SET_THR_AFFINITY_TO_CPUS_FAILED 10983
#define ER_RES_GRP_THD_UNBIND_FROM_CPU_FAILED 10984
#define ER_RES_GRP_SET_THREAD_PRIORITY_FAILED 10985
#define ER_RES_GRP_FAILED_TO_DETERMINE_NICE_CAPABILITY 10986
#define ER_RES_GRP_FAILED_TO_GET_THREAD_HANDLE 10987
#define ER_RES_GRP_GET_THREAD_PRIO_NOT_SUPPORTED 10988
#define ER_RES_GRP_FAILED_DETERMINE_CPU_COUNT 10989
#define ER_RES_GRP_FEATURE_NOT_AVAILABLE 10990
#define ER_RES_GRP_INVALID_THREAD_PRIORITY 10991
#define ER_RES_GRP_SOLARIS_PROCESSOR_BIND_TO_CPUID_FAILED 10992
#define ER_RES_GRP_SOLARIS_PROCESSOR_BIND_TO_THREAD_FAILED 10993
#define ER_RES_GRP_SOLARIS_PROCESSOR_AFFINITY_FAILED 10994
#define ER_DD_UPGRADE_RENAME_IDX_STATS_FILE_FAILED 10995
#define ER_DD_UPGRADE_DD_OPEN_FAILED 10996
#define ER_DD_UPGRADE_FAILED_TO_FETCH_TABLESPACES 10997
#define ER_DD_UPGRADE_FAILED_TO_ACQUIRE_TABLESPACE 10998
#define ER_DD_UPGRADE_FAILED_TO_RESOLVE_TABLESPACE_ENGINE 10999
#define ER_FAILED_TO_CREATE_SDI_FOR_TABLESPACE 11000
#define ER_FAILED_TO_STORE_SDI_FOR_TABLESPACE 11001
#define ER_DD_UPGRADE_FAILED_TO_FETCH_TABLES 11002
#define ER_DD_UPGRADE_DD_POPULATED 11003
#define ER_DD_UPGRADE_INFO_FILE_OPEN_FAILED 11004
#define ER_DD_UPGRADE_INFO_FILE_CLOSE_FAILED 11005
#define ER_DD_UPGRADE_TABLESPACE_MIGRATION_FAILED 11006
#define ER_DD_UPGRADE_FAILED_TO_CREATE_TABLE_STATS 11007
#define ER_DD_UPGRADE_TABLE_STATS_MIGRATE_COMPLETED 11008
#define ER_DD_UPGRADE_FAILED_TO_CREATE_INDEX_STATS 11009
#define ER_DD_UPGRADE_INDEX_STATS_MIGRATE_COMPLETED 11010
#define ER_DD_UPGRADE_FAILED_FIND_VALID_DATA_DIR 11011
#define ER_DD_UPGRADE_START 11012
#define ER_DD_UPGRADE_FAILED_INIT_DD_SE 11013
#define ER_DD_UPGRADE_FOUND_PARTIALLY_UPGRADED_DD_ABORT 11014
#define ER_DD_UPGRADE_FOUND_PARTIALLY_UPGRADED_DD_CONTINUE 11015
#define ER_DD_UPGRADE_SE_LOGS_FAILED 11016
#define ER_DD_UPGRADE_SDI_INFO_UPDATE_FAILED 11017
#define ER_SKIP_UPDATING_METADATA_IN_SE_RO_MODE 11018
#define ER_CREATED_SYSTEM_WITH_VERSION 11019
#define ER_UNKNOWN_ERROR_DETECTED_IN_SE 11020
#define ER_READ_LOG_EVENT_FAILED 11021
#define ER_ROW_DATA_TOO_BIG_TO_WRITE_IN_BINLOG 11022
#define ER_FAILED_TO_CONSTRUCT_DROP_EVENT_QUERY 11023
#define ER_FAILED_TO_BINLOG_DROP_EVENT 11024
#define ER_FAILED_TO_START_REPLICA_THREAD 11025
#define ER_RPL_IO_THREAD_KILLED 11026
#define ER_REPLICA_RECONNECT_FAILED 11027
#define ER_REPLICA_KILLED_AFTER_RECONNECT 11028
#define ER_REPLICA_NOT_STARTED_ON_SOME_CHANNELS 11029
#define ER_FAILED_TO_ADD_RPL_FILTER 11030
#define ER_PER_CHANNEL_RPL_FILTER_CONF_FOR_GRP_RPL 11031
#define ER_RPL_FILTERS_NOT_ATTACHED_TO_CHANNEL 11032
#define ER_FAILED_TO_BUILD_DO_AND_IGNORE_TABLE_HASHES 11033
#define ER_CLONE_PLUGIN_NOT_LOADED_TRACE 11034
#define ER_CLONE_HANDLER_EXIST_TRACE 11035
#define ER_CLONE_CREATE_HANDLER_FAIL_TRACE 11036
#define ER_CYCLE_TIMER_IS_NOT_AVAILABLE 11037
#define ER_NANOSECOND_TIMER_IS_NOT_AVAILABLE 11038
#define ER_MICROSECOND_TIMER_IS_NOT_AVAILABLE 11039
#define ER_PFS_MALLOC_ARRAY_OVERFLOW 11040
#define ER_PFS_MALLOC_ARRAY_OOM 11041
#define ER_INNODB_FAILED_TO_FIND_IDX_WITH_KEY_NO 11042
#define ER_INNODB_FAILED_TO_FIND_IDX 11043
#define ER_INNODB_FAILED_TO_FIND_IDX_FROM_DICT_CACHE 11044
#define ER_INNODB_ACTIVE_INDEX_CHANGE_FAILED 11045
#define ER_INNODB_DIFF_IN_REF_LEN 11046
#define ER_WRONG_TYPE_FOR_COLUMN_PREFIX_IDX_FLD 11047
#define ER_INNODB_CANNOT_CREATE_TABLE 11048
#define ER_INNODB_INTERNAL_INDEX 11049
#define ER_INNODB_IDX_CNT_MORE_THAN_DEFINED_IN_MYSQL 11050
#define ER_INNODB_IDX_CNT_FEWER_THAN_DEFINED_IN_MYSQL 11051
#define ER_INNODB_IDX_COLUMN_CNT_DIFF 11052
#define ER_INNODB_USE_MONITOR_GROUP_NAME 11053
#define ER_INNODB_MONITOR_DEFAULT_VALUE_NOT_DEFINED 11054
#define ER_INNODB_MONITOR_IS_ENABLED 11055
#define ER_INNODB_INVALID_MONITOR_COUNTER_NAME 11056
#define ER_WIN_LOAD_LIBRARY_FAILED 11057
#define ER_PARTITION_HANDLER_ADMIN_MSG 11058
#define ER_RPL_AM_INIT_INFO_MSG 11059
#define ER_DD_UPGRADE_TABLE_INTACT_ERROR 11060
#define ER_SERVER_INIT_COMPILED_IN_COMMANDS 11061
#define ER_MYISAM_CHECK_METHOD_ERROR 11062
#define ER_MYISAM_CRASHED_ERROR 11063
#define ER_WAITPID_FAILED 11064
#define ER_FAILED_TO_FIND_MYSQLD_STATUS 11065
#define ER_INNODB_ERROR_LOGGER_MSG 11066
#define ER_INNODB_ERROR_LOGGER_FATAL_MSG 11067
#define ER_DEPRECATED_SYNTAX_WITH_REPLACEMENT 11068
#define ER_DEPRECATED_SYNTAX_NO_REPLACEMENT 11069
#define ER_DEPRECATE_MSG_NO_REPLACEMENT 11070
#define ER_LOG_PRINTF_MSG 11071
#define ER_BINLOG_LOGGING_NOT_POSSIBLE 11072
#define ER_FAILED_TO_SET_PERSISTED_OPTIONS 11073
#define ER_COMPONENTS_FAILED_TO_ACQUIRE_SERVICE_IMPLEMENTATION 11074
#define ER_RES_GRP_INVALID_VCPU_RANGE 11075
#define ER_RES_GRP_INVALID_VCPU_ID 11076
#define ER_ERROR_DURING_FLUSH_LOG_COMMIT_PHASE 11077
#define ER_DROP_DATABASE_FAILED_RMDIR_MANUALLY 11078
//#define OBSOLETE_ER_EXPIRE_LOGS_DAYS_IGNORED 11079
#define ER_BINLOG_MALFORMED_OR_OLD_RELAY_LOG 11080
#define ER_DD_UPGRADE_VIEW_COLUMN_NAME_TOO_LONG 11081
#define ER_TABLE_NEEDS_DUMP_UPGRADE 11082
#define ER_DD_UPGRADE_FAILED_TO_UPDATE_VER_NO_IN_TABLESPACE 11083
#define ER_KEYRING_MIGRATION_FAILED 11084
#define ER_KEYRING_MIGRATION_SUCCESSFUL 11085
#define ER_RESTART_RECEIVED_INFO 11086
#define ER_LCTN_CHANGED 11087
#define ER_DD_INITIALIZE 11088
#define ER_DD_RESTART 11089
#define ER_DD_UPGRADE 11090
#define ER_DD_UPGRADE_OFF 11091
#define ER_DD_UPGRADE_VERSION_NOT_SUPPORTED 11092
#define ER_DD_UPGRADE_SCHEMA_UNAVAILABLE 11093
#define ER_DD_MINOR_DOWNGRADE 11094
#define ER_DD_MINOR_DOWNGRADE_VERSION_NOT_SUPPORTED 11095
#define ER_DD_NO_VERSION_FOUND 11096
#define ER_THREAD_POOL_NOT_SUPPORTED_ON_PLATFORM 11097
#define ER_THREAD_POOL_SIZE_TOO_LOW 11098
#define ER_THREAD_POOL_SIZE_TOO_HIGH 11099
#define ER_THREAD_POOL_ALGORITHM_INVALID 11100
#define ER_THREAD_POOL_INVALID_STALL_LIMIT 11101
#define ER_THREAD_POOL_INVALID_PRIO_KICKUP_TIMER 11102
#define ER_THREAD_POOL_MAX_UNUSED_THREADS_INVALID 11103
#define ER_THREAD_POOL_CON_HANDLER_INIT_FAILED 11104
#define ER_THREAD_POOL_INIT_FAILED 11105
//#define OBSOLETE_ER_THREAD_POOL_PLUGIN_STARTED 11106
#define ER_THREAD_POOL_CANNOT_SET_THREAD_SPECIFIC_DATA 11107
#define ER_THREAD_POOL_FAILED_TO_CREATE_CONNECT_HANDLER_THD 11108
#define ER_THREAD_POOL_FAILED_TO_CREATE_THD_AND_AUTH_CONN 11109
#define ER_THREAD_POOL_FAILED_PROCESS_CONNECT_EVENT 11110
#define ER_THREAD_POOL_FAILED_TO_CREATE_POOL 11111
#define ER_THREAD_POOL_RATE_LIMITED_ERROR_MSGS 11112
#define ER_TRHEAD_POOL_LOW_LEVEL_INIT_FAILED 11113
#define ER_THREAD_POOL_LOW_LEVEL_REARM_FAILED 11114
#define ER_THREAD_POOL_BUFFER_TOO_SMALL 11115
#define ER_MECAB_NOT_SUPPORTED 11116
#define ER_MECAB_NOT_VERIFIED 11117
#define ER_MECAB_CREATING_MODEL 11118
#define ER_MECAB_FAILED_TO_CREATE_MODEL 11119
#define ER_MECAB_FAILED_TO_CREATE_TRIGGER 11120
#define ER_MECAB_UNSUPPORTED_CHARSET 11121
#define ER_MECAB_CHARSET_LOADED 11122
#define ER_MECAB_PARSE_FAILED 11123
#define ER_MECAB_OOM_WHILE_PARSING_TEXT 11124
#define ER_MECAB_CREATE_LATTICE_FAILED 11125
#define ER_SEMISYNC_TRACE_ENTER_FUNC 11126
#define ER_SEMISYNC_TRACE_EXIT_WITH_INT_EXIT_CODE 11127
#define ER_SEMISYNC_TRACE_EXIT_WITH_BOOL_EXIT_CODE 11128
#define ER_SEMISYNC_TRACE_EXIT 11129
#define ER_SEMISYNC_RPL_INIT_FOR_TRX 11130
#define ER_SEMISYNC_FAILED_TO_ALLOCATE_TRX_NODE 11131
#define ER_SEMISYNC_BINLOG_WRITE_OUT_OF_ORDER 11132
#define ER_SEMISYNC_INSERT_LOG_INFO_IN_ENTRY 11133
#define ER_SEMISYNC_PROBE_LOG_INFO_IN_ENTRY 11134
#define ER_SEMISYNC_CLEARED_ALL_ACTIVE_TRANSACTION_NODES 11135
#define ER_SEMISYNC_CLEARED_ACTIVE_TRANSACTION_TILL_POS 11136
#define ER_SEMISYNC_REPLY_MAGIC_NO_ERROR 11137
#define ER_SEMISYNC_REPLY_PKT_LENGTH_TOO_SMALL 11138
#define ER_SEMISYNC_REPLY_BINLOG_FILE_TOO_LARGE 11139
#define ER_SEMISYNC_SERVER_REPLY 11140
#define ER_SEMISYNC_FUNCTION_CALLED_TWICE 11141
#define ER_SEMISYNC_RPL_ENABLED_ON_SOURCE 11142
#define ER_SEMISYNC_SOURCE_OOM 11143
#define ER_SEMISYNC_DISABLED_ON_SOURCE 11144
#define ER_SEMISYNC_FORCED_SHUTDOWN 11145
#define ER_SEMISYNC_SOURCE_GOT_REPLY_AT_POS 11146
#define ER_SEMISYNC_SOURCE_SIGNAL_ALL_WAITING_THREADS 11147
#define ER_SEMISYNC_SOURCE_TRX_WAIT_POS 11148
#define ER_SEMISYNC_BINLOG_REPLY_IS_AHEAD 11149
#define ER_SEMISYNC_MOVE_BACK_WAIT_POS 11150
#define ER_SEMISYNC_INIT_WAIT_POS 11151
#define ER_SEMISYNC_WAIT_TIME_FOR_BINLOG_SENT 11152
#define ER_SEMISYNC_WAIT_FOR_BINLOG_TIMEDOUT 11153
#define ER_SEMISYNC_WAIT_TIME_ASSESSMENT_FOR_COMMIT_TRX_FAILED 11154
#define ER_SEMISYNC_RPL_SWITCHED_OFF 11155
#define ER_SEMISYNC_RPL_SWITCHED_ON 11156
#define ER_SEMISYNC_NO_SPACE_IN_THE_PKT 11157
#define ER_SEMISYNC_SYNC_HEADER_UPDATE_INFO 11158
#define ER_SEMISYNC_FAILED_TO_INSERT_TRX_NODE 11159
#define ER_SEMISYNC_TRX_SKIPPED_AT_POS 11160
#define ER_SEMISYNC_SOURCE_FAILED_ON_NET_FLUSH 11161
#define ER_SEMISYNC_RECEIVED_ACK_IS_SMALLER 11162
#define ER_SEMISYNC_ADD_ACK_TO_SLOT 11163
#define ER_SEMISYNC_UPDATE_EXISTING_REPLICA_ACK 11164
#define ER_SEMISYNC_FAILED_TO_START_ACK_RECEIVER_THD 11165
#define ER_SEMISYNC_STARTING_ACK_RECEIVER_THD 11166
#define ER_SEMISYNC_FAILED_TO_WAIT_ON_DUMP_SOCKET 11167
#define ER_SEMISYNC_STOPPING_ACK_RECEIVER_THREAD 11168
#define ER_SEMISYNC_FAILED_REGISTER_REPLICA_TO_RECEIVER 11169
#define ER_SEMISYNC_START_BINLOG_DUMP_TO_REPLICA 11170
#define ER_SEMISYNC_STOP_BINLOG_DUMP_TO_REPLICA 11171
#define ER_SEMISYNC_UNREGISTER_TRX_OBSERVER_FAILED 11172
#define ER_SEMISYNC_UNREGISTER_BINLOG_STORAGE_OBSERVER_FAILED 11173
#define ER_SEMISYNC_UNREGISTER_BINLOG_TRANSMIT_OBSERVER_FAILED 11174
#define ER_SEMISYNC_UNREGISTERED_REPLICATOR 11175
#define ER_SEMISYNC_SOCKET_FD_TOO_LARGE 11176
#define ER_SEMISYNC_REPLICA_REPLY 11177
#define ER_SEMISYNC_MISSING_MAGIC_NO_FOR_SEMISYNC_PKT 11178
#define ER_SEMISYNC_REPLICA_START 11179
#define ER_SEMISYNC_REPLICA_REPLY_WITH_BINLOG_INFO 11180
#define ER_SEMISYNC_REPLICA_NET_FLUSH_REPLY_FAILED 11181
#define ER_SEMISYNC_REPLICA_SEND_REPLY_FAILED 11182
#define ER_SEMISYNC_EXECUTION_FAILED_ON_SOURCE 11183
#define ER_SEMISYNC_NOT_SUPPORTED_BY_SOURCE 11184
#define ER_SEMISYNC_REPLICA_SET_FAILED 11185
#define ER_SEMISYNC_FAILED_TO_STOP_ACK_RECEIVER_THD 11186
#define ER_FIREWALL_FAILED_TO_READ_FIREWALL_TABLES 11187
#define ER_FIREWALL_FAILED_TO_REG_DYNAMIC_PRIVILEGES 11188
#define ER_FIREWALL_RECORDING_STMT_WAS_TRUNCATED 11189
#define ER_FIREWALL_RECORDING_STMT_WITHOUT_TEXT 11190
#define ER_FIREWALL_SUSPICIOUS_STMT 11191
#define ER_FIREWALL_ACCESS_DENIED 11192
#define ER_FIREWALL_SKIPPED_UNKNOWN_USER_MODE 11193
#define ER_FIREWALL_RELOADING_CACHE 11194
#define ER_FIREWALL_RESET_FOR_USER 11195
#define ER_FIREWALL_STATUS_FLUSHED 11196
#define ER_KEYRING_LOGGER_ERROR_MSG 11197
#define ER_AUDIT_LOG_FILTER_IS_NOT_INSTALLED 11198
#define ER_AUDIT_LOG_SWITCHING_TO_INCLUDE_LIST 11199
#define ER_AUDIT_LOG_CANNOT_SET_LOG_POLICY_WITH_OTHER_POLICIES 11200
#define ER_AUDIT_LOG_ONLY_INCLUDE_LIST_USED 11201
#define ER_AUDIT_LOG_INDEX_MAP_CANNOT_ACCESS_DIR 11202
#define ER_AUDIT_LOG_WRITER_RENAME_FILE_FAILED 11203
#define ER_AUDIT_LOG_WRITER_DEST_FILE_ALREADY_EXISTS 11204
#define ER_AUDIT_LOG_WRITER_RENAME_FILE_FAILED_REMOVE_FILE_MANUALLY 11205
#define ER_AUDIT_LOG_WRITER_INCOMPLETE_FILE_RENAMED 11206
#define ER_AUDIT_LOG_WRITER_FAILED_TO_WRITE_TO_FILE 11207
#define ER_AUDIT_LOG_EC_WRITER_FAILED_TO_INIT_ENCRYPTION 11208
#define ER_AUDIT_LOG_EC_WRITER_FAILED_TO_INIT_COMPRESSION 11209
#define ER_AUDIT_LOG_EC_WRITER_FAILED_TO_CREATE_FILE 11210
#define ER_AUDIT_LOG_RENAME_LOG_FILE_BEFORE_FLUSH 11211
#define ER_AUDIT_LOG_FILTER_RESULT_MSG 11212
#define ER_AUDIT_LOG_JSON_READER_FAILED_TO_PARSE 11213
#define ER_AUDIT_LOG_JSON_READER_BUF_TOO_SMALL 11214
#define ER_AUDIT_LOG_JSON_READER_FAILED_TO_OPEN_FILE 11215
#define ER_AUDIT_LOG_JSON_READER_FILE_PARSING_ERROR 11216
//#define OBSOLETE_ER_AUDIT_LOG_FILTER_INVALID_COLUMN_COUNT 11217
//#define OBSOLETE_ER_AUDIT_LOG_FILTER_INVALID_COLUMN_DEFINITION 11218
#define ER_AUDIT_LOG_FILTER_FAILED_TO_STORE_TABLE_FLDS 11219
#define ER_AUDIT_LOG_FILTER_FAILED_TO_UPDATE_TABLE 11220
#define ER_AUDIT_LOG_FILTER_FAILED_TO_INSERT_INTO_TABLE 11221
#define ER_AUDIT_LOG_FILTER_FAILED_TO_DELETE_FROM_TABLE 11222
#define ER_AUDIT_LOG_FILTER_FAILED_TO_INIT_TABLE_FOR_READ 11223
#define ER_AUDIT_LOG_FILTER_FAILED_TO_READ_TABLE 11224
#define ER_AUDIT_LOG_FILTER_FAILED_TO_CLOSE_TABLE_AFTER_READING 11225
#define ER_AUDIT_LOG_FILTER_USER_AND_HOST_CANNOT_BE_EMPTY 11226
#define ER_AUDIT_LOG_FILTER_FLD_FILTERNAME_CANNOT_BE_EMPTY 11227
#define ER_VALIDATE_PWD_DICT_FILE_NOT_SPECIFIED 11228
#define ER_VALIDATE_PWD_DICT_FILE_NOT_LOADED 11229
#define ER_VALIDATE_PWD_DICT_FILE_TOO_BIG 11230
#define ER_VALIDATE_PWD_FAILED_TO_READ_DICT_FILE 11231
#define ER_VALIDATE_PWD_FAILED_TO_GET_FLD_FROM_SECURITY_CTX 11232
#define ER_VALIDATE_PWD_FAILED_TO_GET_SECURITY_CTX 11233
#define ER_VALIDATE_PWD_LENGTH_CHANGED 11234
#define ER_REWRITER_QUERY_ERROR_MSG 11235
#define ER_REWRITER_QUERY_FAILED 11236
#define ER_XPLUGIN_STARTUP_FAILED 11237
//#define OBSOLETE_ER_XPLUGIN_SERVER_EXITING 11238
//#define OBSOLETE_ER_XPLUGIN_SERVER_EXITED 11239
#define ER_XPLUGIN_USING_SSL_CONF_FROM_SERVER 11240
#define ER_XPLUGIN_USING_SSL_CONF_FROM_MYSQLX 11241
#define ER_XPLUGIN_FAILED_TO_USE_SSL_CONF 11242
#define ER_XPLUGIN_USING_SSL_FOR_TLS_CONNECTION 11243
#define ER_XPLUGIN_REFERENCE_TO_SECURE_CONN_WITH_XPLUGIN 11244
#define ER_XPLUGIN_ERROR_MSG 11245
#define ER_SHA_PWD_FAILED_TO_PARSE_AUTH_STRING 11246
#define ER_SHA_PWD_FAILED_TO_GENERATE_MULTI_ROUND_HASH 11247
#define ER_SHA_PWD_AUTH_REQUIRES_RSA_OR_SSL 11248
#define ER_SHA_PWD_RSA_KEY_TOO_LONG 11249
#define ER_PLUGIN_COMMON_FAILED_TO_OPEN_FILTER_TABLES 11250
#define ER_PLUGIN_COMMON_FAILED_TO_OPEN_TABLE 11251
#define ER_AUTH_LDAP_ERROR_LOGGER_ERROR_MSG 11252
#define ER_CONN_CONTROL_ERROR_MSG 11253
#define ER_GRP_RPL_ERROR_MSG 11254
#define ER_SHA_PWD_SALT_FOR_USER_CORRUPT 11255
#define ER_SYS_VAR_COMPONENT_OOM 11256
#define ER_SYS_VAR_COMPONENT_VARIABLE_SET_READ_ONLY 11257
#define ER_SYS_VAR_COMPONENT_UNKNOWN_VARIABLE_TYPE 11258
#define ER_SYS_VAR_COMPONENT_FAILED_TO_PARSE_VARIABLE_OPTIONS 11259
#define ER_SYS_VAR_COMPONENT_FAILED_TO_MAKE_VARIABLE_PERSISTENT 11260
#define ER_COMPONENT_FILTER_CONFUSED 11261
#define ER_STOP_REPLICA_IO_THREAD_DISK_SPACE 11262
#define ER_LOG_FILE_CANNOT_OPEN 11263
//#define OBSOLETE_ER_UNABLE_TO_COLLECT_LOG_STATUS 11264
//#define OBSOLETE_ER_DEPRECATED_UTF8_ALIAS 11265
//#define OBSOLETE_ER_DEPRECATED_NATIONAL 11266
//#define OBSOLETE_ER_SLAVE_POSSIBLY_DIVERGED_AFTER_DDL 11267
#define ER_PERSIST_OPTION_STATUS 11268
#define ER_NOT_IMPLEMENTED_GET_TABLESPACE_STATISTICS 11269
//#define OBSOLETE_ER_UNABLE_TO_SET_OPTION 11270
//#define OBSOLETE_ER_RESERVED_TABLESPACE_NAME 11271
#define ER_SSL_FIPS_MODE_ERROR 11272
#define ER_CONN_INIT_CONNECT_IGNORED 11273
//#define OBSOLETE_ER_UNSUPPORTED_SQL_MODE 11274
#define ER_REWRITER_OOM 11275
#define ER_REWRITER_TABLE_MALFORMED_ERROR 11276
#define ER_REWRITER_LOAD_FAILED 11277
#define ER_REWRITER_READ_FAILED 11278
#define ER_CONN_CONTROL_EVENT_COORDINATOR_INIT_FAILED 11279
#define ER_CONN_CONTROL_STAT_CONN_DELAY_TRIGGERED_UPDATE_FAILED 11280
#define ER_CONN_CONTROL_STAT_CONN_DELAY_TRIGGERED_RESET_FAILED 11281
#define ER_CONN_CONTROL_INVALID_CONN_DELAY_TYPE 11282
#define ER_CONN_CONTROL_DELAY_ACTION_INIT_FAILED 11283
#define ER_CONN_CONTROL_FAILED_TO_SET_CONN_DELAY 11284
#define ER_CONN_CONTROL_FAILED_TO_UPDATE_CONN_DELAY_HASH 11285
#define ER_XPLUGIN_FORCE_STOP_CLIENT 11286
#define ER_XPLUGIN_MAX_AUTH_ATTEMPTS_REACHED 11287
#define ER_XPLUGIN_BUFFER_PAGE_ALLOC_FAILED 11288
#define ER_XPLUGIN_DETECTED_HANGING_CLIENTS 11289
#define ER_XPLUGIN_FAILED_TO_ACCEPT_CLIENT 11290
#define ER_XPLUGIN_FAILED_TO_SCHEDULE_CLIENT 11291
#define ER_XPLUGIN_FAILED_TO_PREPARE_IO_INTERFACES 11292
#define ER_XPLUGIN_SRV_SESSION_INIT_THREAD_FAILED 11293
#define ER_XPLUGIN_UNABLE_TO_USE_USER_SESSION_ACCOUNT 11294
#define ER_XPLUGIN_REFERENCE_TO_USER_ACCOUNT_DOC_SECTION 11295
#define ER_XPLUGIN_UNEXPECTED_EXCEPTION_DISPATCHING_CMD 11296
#define ER_XPLUGIN_EXCEPTION_IN_TASK_SCHEDULER 11297
#define ER_XPLUGIN_TASK_SCHEDULING_FAILED 11298
#define ER_XPLUGIN_EXCEPTION_IN_EVENT_LOOP 11299
#define ER_XPLUGIN_LISTENER_SETUP_FAILED 11300
#define ER_XPLUING_NET_STARTUP_FAILED 11301
#define ER_XPLUGIN_FAILED_AT_SSL_CONF 11302
//#define OBSOLETE_ER_XPLUGIN_CLIENT_SSL_HANDSHAKE_FAILED 11303
//#define OBSOLETE_ER_XPLUGIN_SSL_HANDSHAKE_WITH_SERVER_FAILED 11304
#define ER_XPLUGIN_FAILED_TO_CREATE_SESSION_FOR_CONN 11305
#define ER_XPLUGIN_FAILED_TO_INITIALIZE_SESSION 11306
#define ER_XPLUGIN_MESSAGE_TOO_LONG 11307
#define ER_XPLUGIN_UNINITIALIZED_MESSAGE 11308
#define ER_XPLUGIN_FAILED_TO_SET_MIN_NUMBER_OF_WORKERS 11309
#define ER_XPLUGIN_UNABLE_TO_ACCEPT_CONNECTION 11310
#define ER_XPLUGIN_ALL_IO_INTERFACES_DISABLED 11311
//#define OBSOLETE_ER_XPLUGIN_INVALID_MSG_DURING_CLIENT_INIT 11312
//#define OBSOLETE_ER_XPLUGIN_CLOSING_CLIENTS_ON_SHUTDOWN 11313
#define ER_XPLUGIN_ERROR_READING_SOCKET 11314
#define ER_XPLUGIN_PEER_DISCONNECTED_WHILE_READING_MSG_BODY 11315
#define ER_XPLUGIN_READ_FAILED_CLOSING_CONNECTION 11316
//#define OBSOLETE_ER_XPLUGIN_INVALID_AUTH_METHOD 11317
//#define OBSOLETE_ER_XPLUGIN_UNEXPECTED_MSG_DURING_AUTHENTICATION 11318
//#define OBSOLETE_ER_XPLUGIN_ERROR_WRITING_TO_CLIENT 11319
//#define OBSOLETE_ER_XPLUGIN_SCHEDULER_STARTED 11320
//#define OBSOLETE_ER_XPLUGIN_SCHEDULER_STOPPED 11321
#define ER_XPLUGIN_LISTENER_SYS_VARIABLE_ERROR 11322
#define ER_XPLUGIN_LISTENER_STATUS_MSG 11323
#define ER_XPLUGIN_RETRYING_BIND_ON_PORT 11324
//#define OBSOLETE_ER_XPLUGIN_SHUTDOWN_TRIGGERED 11325
//#define OBSOLETE_ER_XPLUGIN_USER_ACCOUNT_WITH_ALL_PERMISSIONS 11326
#define ER_XPLUGIN_EXISTING_USER_ACCOUNT_WITH_INCOMPLETE_GRANTS 11327
//#define OBSOLETE_ER_XPLUGIN_SERVER_STARTS_HANDLING_CONNECTIONS 11328
//#define OBSOLETE_ER_XPLUGIN_SERVER_STOPPED_HANDLING_CONNECTIONS 11329
//#define OBSOLETE_ER_XPLUGIN_FAILED_TO_INTERRUPT_SESSION 11330
//#define OBSOLETE_ER_XPLUGIN_CLIENT_RELEASE_TRIGGERED 11331
#define ER_XPLUGIN_IPv6_AVAILABLE 11332
//#define OBSOLETE_ER_XPLUGIN_UNIX_SOCKET_NOT_CONFIGURED 11333
#define ER_XPLUGIN_CLIENT_KILL_MSG 11334
#define ER_XPLUGIN_FAILED_TO_GET_SECURITY_CTX 11335
//#define OBSOLETE_ER_XPLUGIN_FAILED_TO_SWITCH_SECURITY_CTX_TO_ROOT 11336
#define ER_XPLUGIN_FAILED_TO_CLOSE_SQL_SESSION 11337
#define ER_XPLUGIN_FAILED_TO_EXECUTE_ADMIN_CMD 11338
#define ER_XPLUGIN_EMPTY_ADMIN_CMD 11339
#define ER_XPLUGIN_FAILED_TO_GET_SYS_VAR 11340
#define ER_XPLUGIN_FAILED_TO_GET_CREATION_STMT 11341
#define ER_XPLUGIN_FAILED_TO_GET_ENGINE_INFO 11342
//#define OBSOLETE_ER_XPLUGIN_FAIL_TO_GET_RESULT_DATA 11343
//#define OBSOLETE_ER_XPLUGIN_CAPABILITY_EXPIRED_PASSWORD 11344
#define ER_XPLUGIN_FAILED_TO_SET_SO_REUSEADDR_FLAG 11345
#define ER_XPLUGIN_FAILED_TO_OPEN_INTERNAL_SESSION 11346
#define ER_XPLUGIN_FAILED_TO_SWITCH_CONTEXT 11347
#define ER_XPLUGIN_FAILED_TO_UNREGISTER_UDF 11348
//#define OBSOLETE_ER_XPLUGIN_GET_PEER_ADDRESS_FAILED 11349
//#define OBSOLETE_ER_XPLUGIN_CAPABILITY_CLIENT_INTERACTIVE_FAILED 11350
#define ER_XPLUGIN_FAILED_TO_RESET_IPV6_V6ONLY_FLAG 11351
#define ER_KEYRING_INVALID_KEY_TYPE 11352
#define ER_KEYRING_INVALID_KEY_LENGTH 11353
//#define OBSOLETE_ER_KEYRING_FAILED_TO_CREATE_KEYRING_DIR 11354
//#define OBSOLETE_ER_KEYRING_FILE_INIT_FAILED 11355
//#define OBSOLETE_ER_KEYRING_INTERNAL_EXCEPTION_FAILED_FILE_INIT 11356
//#define OBSOLETE_ER_KEYRING_FAILED_TO_GENERATE_KEY 11357
#define ER_KEYRING_CHECK_KEY_FAILED_DUE_TO_INVALID_KEY 11358
#define ER_KEYRING_CHECK_KEY_FAILED_DUE_TO_EMPTY_KEY_ID 11359
#define ER_KEYRING_OPERATION_FAILED_DUE_TO_INTERNAL_ERROR 11360
#define ER_KEYRING_INCORRECT_FILE 11361
#define ER_KEYRING_FOUND_MALFORMED_BACKUP_FILE 11362
#define ER_KEYRING_FAILED_TO_RESTORE_FROM_BACKUP_FILE 11363
#define ER_KEYRING_FAILED_TO_FLUSH_KEYRING_TO_FILE 11364
#define ER_KEYRING_FAILED_TO_GET_FILE_STAT 11365
#define ER_KEYRING_FAILED_TO_REMOVE_FILE 11366
#define ER_KEYRING_FAILED_TO_TRUNCATE_FILE 11367
#define ER_KEYRING_UNKNOWN_ERROR 11368
//#define OBSOLETE_ER_KEYRING_FAILED_TO_SET_KEYRING_FILE_DATA 11369
#define ER_KEYRING_FILE_IO_ERROR 11370
#define ER_KEYRING_FAILED_TO_LOAD_KEYRING_CONTENT 11371
#define ER_KEYRING_FAILED_TO_FLUSH_KEYS_TO_KEYRING 11372
#define ER_KEYRING_FAILED_TO_FLUSH_KEYS_TO_KEYRING_BACKUP 11373
#define ER_KEYRING_KEY_FETCH_FAILED_DUE_TO_EMPTY_KEY_ID 11374
#define ER_KEYRING_FAILED_TO_REMOVE_KEY_DUE_TO_EMPTY_ID 11375
#define ER_KEYRING_OKV_INCORRECT_KEY_VAULT_CONFIGURED 11376
#define ER_KEYRING_OKV_INIT_FAILED_DUE_TO_INCORRECT_CONF 11377
#define ER_KEYRING_OKV_INIT_FAILED_DUE_TO_INTERNAL_ERROR 11378
#define ER_KEYRING_OKV_INVALID_KEY_TYPE 11379
#define ER_KEYRING_OKV_INVALID_KEY_LENGTH_FOR_CIPHER 11380
#define ER_KEYRING_OKV_FAILED_TO_GENERATE_KEY_DUE_TO_INTERNAL_ERROR 11381
#define ER_KEYRING_OKV_FAILED_TO_FIND_SERVER_ENTRY 11382
#define ER_KEYRING_OKV_FAILED_TO_FIND_STANDBY_SERVER_ENTRY 11383
#define ER_KEYRING_OKV_FAILED_TO_PARSE_CONF_FILE 11384
#define ER_KEYRING_OKV_FAILED_TO_LOAD_KEY_UID 11385
#define ER_KEYRING_OKV_FAILED_TO_INIT_SSL_LAYER 11386
#define ER_KEYRING_OKV_FAILED_TO_INIT_CLIENT 11387
#define ER_KEYRING_OKV_CONNECTION_TO_SERVER_FAILED 11388
#define ER_KEYRING_OKV_FAILED_TO_REMOVE_KEY 11389
#define ER_KEYRING_OKV_FAILED_TO_ADD_ATTRIBUTE 11390
#define ER_KEYRING_OKV_FAILED_TO_GENERATE_KEY 11391
#define ER_KEYRING_OKV_FAILED_TO_STORE_KEY 11392
#define ER_KEYRING_OKV_FAILED_TO_ACTIVATE_KEYS 11393
#define ER_KEYRING_OKV_FAILED_TO_FETCH_KEY 11394
#define ER_KEYRING_OKV_FAILED_TO_STORE_OR_GENERATE_KEY 11395
#define ER_KEYRING_OKV_FAILED_TO_RETRIEVE_KEY_SIGNATURE 11396
#define ER_KEYRING_OKV_FAILED_TO_RETRIEVE_KEY 11397
#define ER_KEYRING_OKV_FAILED_TO_LOAD_SSL_TRUST_STORE 11398
#define ER_KEYRING_OKV_FAILED_TO_SET_CERTIFICATE_FILE 11399
#define ER_KEYRING_OKV_FAILED_TO_SET_KEY_FILE 11400
#define ER_KEYRING_OKV_KEY_MISMATCH 11401
//#define OBSOLETE_ER_KEYRING_ENCRYPTED_FILE_INCORRECT_KEYRING_FILE 11402
//#define OBSOLETE_ER_KEYRING_ENCRYPTED_FILE_DECRYPTION_FAILED 11403
//#define OBSOLETE_ER_KEYRING_ENCRYPTED_FILE_FOUND_MALFORMED_BACKUP_FILE 11404
//#define OBSOLETE_ER_KEYRING_ENCRYPTED_FILE_FAILED_TO_RESTORE_KEYRING 11405
//#define OBSOLETE_ER_KEYRING_ENCRYPTED_FILE_FAILED_TO_FLUSH_KEYRING 11406
//#define OBSOLETE_ER_KEYRING_ENCRYPTED_FILE_ENCRYPTION_FAILED 11407
//#define OBSOLETE_ER_KEYRING_ENCRYPTED_FILE_INVALID_KEYRING_DIR 11408
//#define OBSOLETE_ER_KEYRING_ENCRYPTED_FILE_FAILED_TO_CREATE_KEYRING_DIR 11409
//#define OBSOLETE_ER_KEYRING_ENCRYPTED_FILE_PASSWORD_IS_INVALID 11410
//#define OBSOLETE_ER_KEYRING_ENCRYPTED_FILE_PASSWORD_IS_TOO_LONG 11411
//#define OBSOLETE_ER_KEYRING_ENCRYPTED_FILE_INIT_FAILURE 11412
//#define OBSOLETE_ER_KEYRING_ENCRYPTED_FILE_INIT_FAILED_INTERNAL_ERROR 11413
//#define OBSOLETE_ER_KEYRING_ENCRYPTED_FILE_GEN_KEY_FAILED_INTERNAL_ERROR 11414
#define ER_KEYRING_AWS_FAILED_TO_SET_CMK_ID 11415
#define ER_KEYRING_AWS_FAILED_TO_SET_REGION 11416
#define ER_KEYRING_AWS_FAILED_TO_OPEN_CONF_FILE 11417
#define ER_KEYRING_AWS_FAILED_TO_ACCESS_KEY_ID_FROM_CONF_FILE 11418
#define ER_KEYRING_AWS_FAILED_TO_ACCESS_KEY_FROM_CONF_FILE 11419
#define ER_KEYRING_AWS_INVALID_CONF_FILE_PATH 11420
#define ER_KEYRING_AWS_INVALID_DATA_FILE_PATH 11421
#define ER_KEYRING_AWS_FAILED_TO_ACCESS_OR_CREATE_KEYRING_DIR 11422
#define ER_KEYRING_AWS_FAILED_TO_ACCESS_OR_CREATE_KEYRING_DATA_FILE 11423
#define ER_KEYRING_AWS_FAILED_TO_INIT_DUE_TO_INTERNAL_ERROR 11424
#define ER_KEYRING_AWS_FAILED_TO_ACCESS_DATA_FILE 11425
#define ER_KEYRING_AWS_CMK_ID_NOT_SET 11426
#define ER_KEYRING_AWS_FAILED_TO_GET_KMS_CREDENTIAL_FROM_CONF_FILE 11427
#define ER_KEYRING_AWS_INIT_FAILURE 11428
#define ER_KEYRING_AWS_FAILED_TO_INIT_DUE_TO_PLUGIN_INTERNAL_ERROR 11429
#define ER_KEYRING_AWS_INVALID_KEY_LENGTH_FOR_CIPHER 11430
#define ER_KEYRING_AWS_FAILED_TO_GENERATE_KEY_DUE_TO_INTERNAL_ERROR 11431
#define ER_KEYRING_AWS_INCORRECT_FILE 11432
#define ER_KEYRING_AWS_FOUND_MALFORMED_BACKUP_FILE 11433
#define ER_KEYRING_AWS_FAILED_TO_RESTORE_FROM_BACKUP_FILE 11434
#define ER_KEYRING_AWS_FAILED_TO_FLUSH_KEYRING_TO_FILE 11435
#define ER_KEYRING_AWS_INCORRECT_REGION 11436
#define ER_KEYRING_AWS_FAILED_TO_CONNECT_KMS 11437
#define ER_KEYRING_AWS_FAILED_TO_GENERATE_NEW_KEY 11438
#define ER_KEYRING_AWS_FAILED_TO_ENCRYPT_KEY 11439
#define ER_KEYRING_AWS_FAILED_TO_RE_ENCRYPT_KEY 11440
#define ER_KEYRING_AWS_FAILED_TO_DECRYPT_KEY 11441
#define ER_KEYRING_AWS_FAILED_TO_ROTATE_CMK 11442
#define ER_GRP_RPL_GTID_ALREADY_USED 11443
#define ER_GRP_RPL_APPLIER_THD_KILLED 11444
#define ER_GRP_RPL_EVENT_HANDLING_ERROR 11445
#define ER_GRP_RPL_ERROR_GTID_EXECUTION_INFO 11446
//#define OBSOLETE_ER_GRP_RPL_CERTIFICATE_SIZE_ERROR 11447
#define ER_GRP_RPL_CREATE_APPLIER_CACHE_ERROR 11448
#define ER_GRP_RPL_UNBLOCK_WAITING_THD 11449
#define ER_GRP_RPL_APPLIER_PIPELINE_NOT_DISPOSED 11450
#define ER_GRP_RPL_APPLIER_THD_EXECUTION_ABORTED 11451
#define ER_GRP_RPL_APPLIER_EXECUTION_FATAL_ERROR 11452
#define ER_GRP_RPL_ERROR_STOPPING_CHANNELS 11453
#define ER_GRP_RPL_ERROR_SENDING_SINGLE_PRIMARY_MSSG 11454
//#define OBSOLETE_ER_GRP_RPL_UPDATE_TRANS_SNAPSHOT_VER_ERROR 11455
//#define OBSOLETE_ER_GRP_RPL_SIDNO_FETCH_ERROR 11456
#define ER_GRP_RPL_BROADCAST_COMMIT_TRANS_MSSG_FAILED 11457
#define ER_GRP_RPL_GROUP_NAME_PARSE_ERROR 11458
#define ER_GRP_RPL_ADD_GRPSID_TO_GRPGTIDSID_MAP_ERROR 11459
#define ER_GRP_RPL_UPDATE_GRPGTID_EXECUTED_ERROR 11460
#define ER_GRP_RPL_DONOR_TRANS_INFO_ERROR 11461
#define ER_GRP_RPL_SERVER_CONN_ERROR 11462
#define ER_GRP_RPL_ERROR_FETCHING_GTID_EXECUTED_SET 11463
#define ER_GRP_RPL_ADD_GTID_TO_GRPGTID_EXECUTED_ERROR 11464
#define ER_GRP_RPL_ERROR_FETCHING_GTID_SET 11465
#define ER_GRP_RPL_ADD_RETRIEVED_SET_TO_GRP_GTID_EXECUTED_ERROR 11466
#define ER_GRP_RPL_CERTIFICATION_INITIALIZATION_FAILURE 11467
#define ER_GRP_RPL_UPDATE_LAST_CONFLICT_FREE_TRANS_ERROR 11468
#define ER_GRP_RPL_UPDATE_TRANS_SNAPSHOT_REF_VER_ERROR 11469
//#define OBSOLETE_ER_GRP_RPL_FETCH_TRANS_SIDNO_ERROR 11470
//#define OBSOLETE_ER_GRP_RPL_ERROR_VERIFYING_SIDNO 11471
#define ER_GRP_RPL_CANT_GENERATE_GTID 11472
#define ER_GRP_RPL_INVALID_GTID_SET 11473
#define ER_GRP_RPL_UPDATE_GTID_SET_ERROR 11474
#define ER_GRP_RPL_RECEIVED_SET_MISSING_GTIDS 11475
//#define OBSOLETE_ER_GRP_RPL_SKIP_COMPUTATION_TRANS_COMMITTED 11476
#define ER_GRP_RPL_NULL_PACKET 11477
#define ER_GRP_RPL_CANT_READ_GTID 11478
#define ER_GRP_RPL_PROCESS_GTID_SET_ERROR 11479
#define ER_GRP_RPL_PROCESS_INTERSECTION_GTID_SET_ERROR 11480
#define ER_GRP_RPL_SET_STABLE_TRANS_ERROR 11481
#define ER_GRP_RPL_CANT_READ_GRP_GTID_EXTRACTED 11482
#define ER_GRP_RPL_CANT_READ_WRITE_SET_ITEM 11483
#define ER_GRP_RPL_INIT_CERTIFICATION_INFO_FAILURE 11484
#define ER_GRP_RPL_CONFLICT_DETECTION_DISABLED 11485
#define ER_GRP_RPL_MSG_DISCARDED 11486
#define ER_GRP_RPL_MISSING_GRP_RPL_APPLIER 11487
#define ER_GRP_RPL_CERTIFIER_MSSG_PROCESS_ERROR 11488
#define ER_GRP_RPL_SRV_NOT_ONLINE 11489
#define ER_GRP_RPL_SRV_ONLINE 11490
#define ER_GRP_RPL_DISABLE_SRV_READ_MODE_RESTRICTED 11491
#define ER_GRP_RPL_MEM_ONLINE 11492
#define ER_GRP_RPL_MEM_UNREACHABLE 11493
#define ER_GRP_RPL_MEM_REACHABLE 11494
#define ER_GRP_RPL_SRV_BLOCKED 11495
#define ER_GRP_RPL_SRV_BLOCKED_FOR_SECS 11496
#define ER_GRP_RPL_CHANGE_GRP_MEM_NOT_PROCESSED 11497
#define ER_GRP_RPL_MEMBER_CONTACT_RESTORED 11498
#define ER_GRP_RPL_MEMBER_REMOVED 11499
#define ER_GRP_RPL_PRIMARY_MEMBER_LEFT_GRP 11500
#define ER_GRP_RPL_MEMBER_ADDED 11501
#define ER_GRP_RPL_MEMBER_EXIT_PLUGIN_ERROR 11502
#define ER_GRP_RPL_MEMBER_CHANGE 11503
#define ER_GRP_RPL_MEMBER_LEFT_GRP 11504
#define ER_GRP_RPL_MEMBER_EXPELLED 11505
#define ER_GRP_RPL_SESSION_OPEN_FAILED 11506
#define ER_GRP_RPL_NEW_PRIMARY_ELECTED 11507
#define ER_GRP_RPL_DISABLE_READ_ONLY_FAILED 11508
#define ER_GRP_RPL_ENABLE_READ_ONLY_FAILED 11509
#define ER_GRP_RPL_SRV_PRIMARY_MEM 11510
#define ER_GRP_RPL_SRV_SECONDARY_MEM 11511
#define ER_GRP_RPL_NO_SUITABLE_PRIMARY_MEM 11512
#define ER_GRP_RPL_SUPER_READ_ONLY_ACTIVATE_ERROR 11513
#define ER_GRP_RPL_EXCEEDS_AUTO_INC_VALUE 11514
#define ER_GRP_RPL_DATA_NOT_PROVIDED_BY_MEM 11515
#define ER_GRP_RPL_MEMBER_ALREADY_EXISTS 11516
//#define OBSOLETE_ER_GRP_RPL_GRP_CHANGE_INFO_EXTRACT_ERROR 11517
#define ER_GRP_RPL_GTID_EXECUTED_EXTRACT_ERROR 11518
#define ER_GRP_RPL_GTID_SET_EXTRACT_ERROR 11519
#define ER_GRP_RPL_START_FAILED 11520
#define ER_GRP_RPL_MEMBER_VER_INCOMPATIBLE 11521
#define ER_GRP_RPL_TRANS_NOT_PRESENT_IN_GRP 11522
#define ER_GRP_RPL_TRANS_GREATER_THAN_GRP 11523
#define ER_GRP_RPL_MEMBER_VERSION_LOWER_THAN_GRP 11524
#define ER_GRP_RPL_LOCAL_GTID_SETS_PROCESS_ERROR 11525
#define ER_GRP_RPL_MEMBER_TRANS_GREATER_THAN_GRP 11526
#define ER_GRP_RPL_BLOCK_SIZE_DIFF_FROM_GRP 11527
#define ER_GRP_RPL_TRANS_WRITE_SET_EXTRACT_DIFF_FROM_GRP 11528
#define ER_GRP_RPL_MEMBER_CFG_INCOMPATIBLE_WITH_GRP_CFG 11529
#define ER_GRP_RPL_MEMBER_STOP_RPL_CHANNELS_ERROR 11530
#define ER_GRP_RPL_PURGE_APPLIER_LOGS 11531
#define ER_GRP_RPL_RESET_APPLIER_MODULE_LOGS_ERROR 11532
#define ER_GRP_RPL_APPLIER_THD_SETUP_ERROR 11533
#define ER_GRP_RPL_APPLIER_THD_START_ERROR 11534
#define ER_GRP_RPL_APPLIER_THD_STOP_ERROR 11535
#define ER_GRP_RPL_FETCH_TRANS_DATA_FAILED 11536
#define ER_GRP_RPL_REPLICA_IO_THD_PRIMARY_UNKNOWN 11537
#define ER_GRP_RPL_SALVE_IO_THD_ON_SECONDARY_MEMBER 11538
#define ER_GRP_RPL_REPLICA_SQL_THD_PRIMARY_UNKNOWN 11539
#define ER_GRP_RPL_REPLICA_SQL_THD_ON_SECONDARY_MEMBER 11540
#define ER_GRP_RPL_NEEDS_INNODB_TABLE 11541
#define ER_GRP_RPL_PRIMARY_KEY_NOT_DEFINED 11542
#define ER_GRP_RPL_FK_WITH_CASCADE_UNSUPPORTED 11543
#define ER_GRP_RPL_AUTO_INC_RESET 11544
#define ER_GRP_RPL_AUTO_INC_OFFSET_RESET 11545
#define ER_GRP_RPL_AUTO_INC_SET 11546
#define ER_GRP_RPL_AUTO_INC_OFFSET_SET 11547
#define ER_GRP_RPL_FETCH_TRANS_CONTEXT_FAILED 11548
#define ER_GRP_RPL_FETCH_FORMAT_DESC_LOG_EVENT_FAILED 11549
#define ER_GRP_RPL_FETCH_TRANS_CONTEXT_LOG_EVENT_FAILED 11550
#define ER_GRP_RPL_FETCH_SNAPSHOT_VERSION_FAILED 11551
#define ER_GRP_RPL_FETCH_GTID_LOG_EVENT_FAILED 11552
#define ER_GRP_RPL_UPDATE_SERV_CERTIFICATE_FAILED 11553
#define ER_GRP_RPL_ADD_GTID_INFO_WITH_LOCAL_GTID_FAILED 11554
#define ER_GRP_RPL_ADD_GTID_INFO_WITHOUT_LOCAL_GTID_FAILED 11555
#define ER_GRP_RPL_NOTIFY_CERTIFICATION_OUTCOME_FAILED 11556
#define ER_GRP_RPL_ADD_GTID_INFO_WITH_REMOTE_GTID_FAILED 11557
#define ER_GRP_RPL_ADD_GTID_INFO_WITHOUT_REMOTE_GTID_FAILED 11558
#define ER_GRP_RPL_FETCH_VIEW_CHANGE_LOG_EVENT_FAILED 11559
//#define OBSOLETE_ER_GRP_RPL_CONTACT_WITH_SRV_FAILED 11560
//#define OBSOLETE_ER_GRP_RPL_SRV_WAIT_TIME_OUT 11561
#define ER_GRP_RPL_FETCH_LOG_EVENT_FAILED 11562
#define ER_GRP_RPL_START_GRP_RPL_FAILED 11563
#define ER_GRP_RPL_CONN_INTERNAL_PLUGIN_FAIL 11564
#define ER_GRP_RPL_SUPER_READ_ON 11565
#define ER_GRP_RPL_SUPER_READ_OFF 11566
#define ER_GRP_RPL_KILLED_SESSION_ID 11567
#define ER_GRP_RPL_KILLED_FAILED_ID 11568
#define ER_GRP_RPL_INTERNAL_QUERY 11569
#define ER_GRP_RPL_COPY_FROM_EMPTY_STRING 11570
#define ER_GRP_RPL_QUERY_FAIL 11571
#define ER_GRP_RPL_CREATE_SESSION_UNABLE 11572
#define ER_GRP_RPL_MEMBER_NOT_FOUND 11573
#define ER_GRP_RPL_MAXIMUM_CONNECTION_RETRIES_REACHED 11574
#define ER_GRP_RPL_ALL_DONORS_LEFT_ABORT_RECOVERY 11575
#define ER_GRP_RPL_ESTABLISH_RECOVERY_WITH_DONOR 11576
#define ER_GRP_RPL_ESTABLISH_RECOVERY_WITH_ANOTHER_DONOR 11577
#define ER_GRP_RPL_NO_VALID_DONOR 11578
#define ER_GRP_RPL_CONFIG_RECOVERY 11579
#define ER_GRP_RPL_ESTABLISHING_CONN_GRP_REC_DONOR 11580
#define ER_GRP_RPL_CREATE_GRP_RPL_REC_CHANNEL 11581
#define ER_GRP_RPL_DONOR_SERVER_CONN 11582
#define ER_GRP_RPL_CHECK_STATUS_TABLE 11583
#define ER_GRP_RPL_STARTING_GRP_REC 11584
#define ER_GRP_RPL_DONOR_CONN_TERMINATION 11585
#define ER_GRP_RPL_STOPPING_GRP_REC 11586
#define ER_GRP_RPL_PURGE_REC 11587
#define ER_GRP_RPL_UNABLE_TO_KILL_CONN_REC_DONOR_APPLIER 11588
#define ER_GRP_RPL_UNABLE_TO_KILL_CONN_REC_DONOR_FAILOVER 11589
#define ER_GRP_RPL_FAILED_TO_NOTIFY_GRP_MEMBERSHIP_EVENT 11590
#define ER_GRP_RPL_FAILED_TO_BROADCAST_GRP_MEMBERSHIP_NOTIFICATION 11591
#define ER_GRP_RPL_FAILED_TO_BROADCAST_MEMBER_STATUS_NOTIFICATION 11592
#define ER_GRP_RPL_OOM_FAILED_TO_GENERATE_IDENTIFICATION_HASH 11593
#define ER_GRP_RPL_WRITE_IDENT_HASH_BASE64_ENCODING_FAILED 11594
#define ER_GRP_RPL_INVALID_BINLOG_FORMAT 11595
//#define OBSOLETE_ER_GRP_RPL_BINLOG_CHECKSUM_SET 11596
//#define OBSOLETE_ER_GRP_RPL_TRANS_WRITE_SET_EXTRACTION_NOT_SET 11597
#define ER_GRP_RPL_UNSUPPORTED_TRANS_ISOLATION 11598
#define ER_GRP_RPL_CANNOT_EXECUTE_TRANS_WHILE_STOPPING 11599
#define ER_GRP_RPL_CANNOT_EXECUTE_TRANS_WHILE_RECOVERING 11600
#define ER_GRP_RPL_CANNOT_EXECUTE_TRANS_IN_ERROR_STATE 11601
#define ER_GRP_RPL_CANNOT_EXECUTE_TRANS_IN_OFFLINE_MODE 11602
#define ER_GRP_RPL_MULTIPLE_CACHE_TYPE_NOT_SUPPORTED_FOR_SESSION 11603
#define ER_GRP_RPL_FAILED_TO_REINIT_BINLOG_CACHE_FOR_READ 11604
#define ER_GRP_RPL_FAILED_TO_CREATE_TRANS_CONTEXT 11605
#define ER_GRP_RPL_FAILED_TO_EXTRACT_TRANS_WRITE_SET 11606
#define ER_GRP_RPL_FAILED_TO_GATHER_TRANS_WRITE_SET 11607
#define ER_GRP_RPL_TRANS_SIZE_EXCEEDS_LIMIT 11608
//#define OBSOLETE_ER_GRP_RPL_REINIT_OF_INTERNAL_CACHE_FOR_READ_FAILED 11609
//#define OBSOLETE_ER_GRP_RPL_APPENDING_DATA_TO_INTERNAL_CACHE_FAILED 11610
#define ER_GRP_RPL_WRITE_TO_TRANSACTION_MESSAGE_FAILED 11611
#define ER_GRP_RPL_FAILED_TO_REGISTER_TRANS_OUTCOME_NOTIFICTION 11612
#define ER_GRP_RPL_MSG_TOO_LONG_BROADCASTING_TRANS_FAILED 11613
#define ER_GRP_RPL_BROADCASTING_TRANS_TO_GRP_FAILED 11614
#define ER_GRP_RPL_ERROR_WHILE_WAITING_FOR_CONFLICT_DETECTION 11615
//#define OBSOLETE_ER_GRP_RPL_REINIT_OF_INTERNAL_CACHE_FOR_WRITE_FAILED 11616
//#define OBSOLETE_ER_GRP_RPL_FAILED_TO_CREATE_COMMIT_CACHE 11617
//#define OBSOLETE_ER_GRP_RPL_REINIT_OF_COMMIT_CACHE_FOR_WRITE_FAILED 11618
//#define OBSOLETE_ER_GRP_RPL_PREV_REC_SESSION_RUNNING 11619
#define ER_GRP_RPL_FATAL_REC_PROCESS 11620
//#define OBSOLETE_ER_GRP_RPL_WHILE_STOPPING_REP_CHANNEL 11621
#define ER_GRP_RPL_UNABLE_TO_EVALUATE_APPLIER_STATUS 11622
#define ER_GRP_RPL_ONLY_ONE_SERVER_ALIVE 11623
#define ER_GRP_RPL_CERTIFICATION_REC_PROCESS 11624
#define ER_GRP_RPL_UNABLE_TO_ENSURE_EXECUTION_REC 11625
#define ER_GRP_RPL_WHILE_SENDING_MSG_REC 11626
//#define OBSOLETE_ER_GRP_RPL_READ_UNABLE_FOR_SUPER_READ_ONLY 11627
#define ER_GRP_RPL_READ_UNABLE_FOR_READ_ONLY_SUPER_READ_ONLY 11628
#define ER_GRP_RPL_UNABLE_TO_RESET_SERVER_READ_MODE 11629
#define ER_GRP_RPL_UNABLE_TO_CERTIFY_PLUGIN_TRANS 11630
#define ER_GRP_RPL_UNBLOCK_CERTIFIED_TRANS 11631
//#define OBSOLETE_ER_GRP_RPL_SERVER_WORKING_AS_SECONDARY 11632
#define ER_GRP_RPL_FAILED_TO_START_WITH_INVALID_SERVER_ID 11633
#define ER_GRP_RPL_FORCE_MEMBERS_MUST_BE_EMPTY 11634
#define ER_GRP_RPL_PLUGIN_STRUCT_INIT_NOT_POSSIBLE_ON_SERVER_START 11635
#define ER_GRP_RPL_FAILED_TO_ENABLE_SUPER_READ_ONLY_MODE 11636
#define ER_GRP_RPL_FAILED_TO_INIT_COMMUNICATION_ENGINE 11637
#define ER_GRP_RPL_FAILED_TO_START_ON_SECONDARY_WITH_ASYNC_CHANNELS 11638
#define ER_GRP_RPL_FAILED_TO_START_COMMUNICATION_ENGINE 11639
#define ER_GRP_RPL_TIMEOUT_ON_VIEW_AFTER_JOINING_GRP 11640
#define ER_GRP_RPL_FAILED_TO_CALL_GRP_COMMUNICATION_INTERFACE 11641
#define ER_GRP_RPL_MEMBER_SERVER_UUID_IS_INCOMPATIBLE_WITH_GRP 11642
#define ER_GRP_RPL_MEMBER_CONF_INFO 11643
#define ER_GRP_RPL_FAILED_TO_CONFIRM_IF_SERVER_LEFT_GRP 11644
#define ER_GRP_RPL_SERVER_IS_ALREADY_LEAVING 11645
#define ER_GRP_RPL_SERVER_ALREADY_LEFT 11646
#define ER_GRP_RPL_WAITING_FOR_VIEW_UPDATE 11647
#define ER_GRP_RPL_TIMEOUT_RECEIVING_VIEW_CHANGE_ON_SHUTDOWN 11648
#define ER_GRP_RPL_REQUESTING_NON_MEMBER_SERVER_TO_LEAVE 11649
#define ER_GRP_RPL_IS_STOPPING 11650
#define ER_GRP_RPL_IS_STOPPED 11651
#define ER_GRP_RPL_FAILED_TO_ENABLE_READ_ONLY_MODE_ON_SHUTDOWN 11652
#define ER_GRP_RPL_RECOVERY_MODULE_TERMINATION_TIMED_OUT_ON_SHUTDOWN 11653
#define ER_GRP_RPL_APPLIER_TERMINATION_TIMED_OUT_ON_SHUTDOWN 11654
#define ER_GRP_RPL_FAILED_TO_SHUTDOWN_REGISTRY_MODULE 11655
#define ER_GRP_RPL_FAILED_TO_INIT_HANDLER 11656
#define ER_GRP_RPL_FAILED_TO_REGISTER_SERVER_STATE_OBSERVER 11657
#define ER_GRP_RPL_FAILED_TO_REGISTER_TRANS_STATE_OBSERVER 11658
#define ER_GRP_RPL_FAILED_TO_REGISTER_BINLOG_STATE_OBSERVER 11659
#define ER_GRP_RPL_FAILED_TO_START_ON_BOOT 11660
#define ER_GRP_RPL_FAILED_TO_STOP_ON_PLUGIN_UNINSTALL 11661
#define ER_GRP_RPL_FAILED_TO_UNREGISTER_SERVER_STATE_OBSERVER 11662
#define ER_GRP_RPL_FAILED_TO_UNREGISTER_TRANS_STATE_OBSERVER 11663
#define ER_GRP_RPL_FAILED_TO_UNREGISTER_BINLOG_STATE_OBSERVER 11664
#define ER_GRP_RPL_ALL_OBSERVERS_UNREGISTERED 11665
#define ER_GRP_RPL_FAILED_TO_PARSE_THE_GRP_NAME 11666
#define ER_GRP_RPL_FAILED_TO_GENERATE_SIDNO_FOR_GRP 11667
#define ER_GRP_RPL_APPLIER_NOT_STARTED_DUE_TO_RUNNING_PREV_SHUTDOWN 11668
#define ER_GRP_RPL_FAILED_TO_INIT_APPLIER_MODULE 11669
#define ER_GRP_RPL_APPLIER_INITIALIZED 11670
#define ER_GRP_RPL_COMMUNICATION_SSL_CONF_INFO 11671
#define ER_GRP_RPL_ABORTS_AS_SSL_NOT_SUPPORTED_BY_MYSQLD 11672
#define ER_GRP_RPL_SSL_DISABLED 11673
#define ER_GRP_RPL_UNABLE_TO_INIT_COMMUNICATION_ENGINE 11674
#define ER_GRP_RPL_BINLOG_DISABLED 11675
#define ER_GRP_RPL_GTID_MODE_OFF 11676
#define ER_GRP_RPL_LOG_REPLICA_UPDATES_NOT_SET 11677
//#define OBSOLETE_ER_GRP_RPL_INVALID_TRANS_WRITE_SET_EXTRACTION_VALUE 11678
#define ER_GRP_RPL_APPLIER_METADATA_REPO_MUST_BE_TABLE 11679
#define ER_GRP_RPL_CONNECTION_METADATA_REPO_MUST_BE_TABLE 11680
#define ER_GRP_RPL_INCORRECT_TYPE_SET_FOR_PARALLEL_APPLIER 11681
#define ER_GRP_RPL_REPLICA_PRESERVE_COMMIT_ORDER_NOT_SET 11682
#define ER_GRP_RPL_SINGLE_PRIM_MODE_NOT_ALLOWED_WITH_UPDATE_EVERYWHERE 11683
#define ER_GRP_RPL_MODULE_TERMINATE_ERROR 11684
#define ER_GRP_RPL_GRP_NAME_OPTION_MANDATORY 11685
#define ER_GRP_RPL_GRP_NAME_IS_TOO_LONG 11686
#define ER_GRP_RPL_GRP_NAME_IS_NOT_VALID_UUID 11687
#define ER_GRP_RPL_FLOW_CTRL_MIN_QUOTA_GREATER_THAN_MAX_QUOTA 11688
#define ER_GRP_RPL_FLOW_CTRL_MIN_RECOVERY_QUOTA_GREATER_THAN_MAX_QUOTA 11689
#define ER_GRP_RPL_FLOW_CTRL_MAX_QUOTA_SMALLER_THAN_MIN_QUOTAS 11690
#define ER_GRP_RPL_INVALID_SSL_RECOVERY_STRING 11691
//#define OBSOLETE_ER_GRP_RPL_SUPPORTS_ONLY_ONE_FORCE_MEMBERS_SET 11692
//#define OBSOLETE_ER_GRP_RPL_FORCE_MEMBERS_SET_UPDATE_NOT_ALLOWED 11693
#define ER_GRP_RPL_GRP_COMMUNICATION_INIT_WITH_CONF 11694
#define ER_GRP_RPL_UNKNOWN_GRP_RPL_APPLIER_PIPELINE_REQUESTED 11695
#define ER_GRP_RPL_FAILED_TO_BOOTSTRAP_EVENT_HANDLING_INFRASTRUCTURE 11696
#define ER_GRP_RPL_APPLIER_HANDLER_NOT_INITIALIZED 11697
#define ER_GRP_RPL_APPLIER_HANDLER_IS_IN_USE 11698
#define ER_GRP_RPL_APPLIER_HANDLER_ROLE_IS_IN_USE 11699
#define ER_GRP_RPL_FAILED_TO_INIT_APPLIER_HANDLER 11700
#define ER_GRP_RPL_SQL_SERVICE_FAILED_TO_INIT_SESSION_THREAD 11701
#define ER_GRP_RPL_SQL_SERVICE_COMM_SESSION_NOT_INITIALIZED 11702
#define ER_GRP_RPL_SQL_SERVICE_SERVER_SESSION_KILLED 11703
#define ER_GRP_RPL_SQL_SERVICE_FAILED_TO_RUN_SQL_QUERY 11704
#define ER_GRP_RPL_SQL_SERVICE_SERVER_INTERNAL_FAILURE 11705
#define ER_GRP_RPL_SQL_SERVICE_RETRIES_EXCEEDED_ON_SESSION_STATE 11706
#define ER_GRP_RPL_SQL_SERVICE_FAILED_TO_FETCH_SECURITY_CTX 11707
#define ER_GRP_RPL_SQL_SERVICE_SERVER_ACCESS_DENIED_FOR_USER 11708
#define ER_GRP_RPL_SQL_SERVICE_MAX_CONN_ERROR_FROM_SERVER 11709
#define ER_GRP_RPL_SQL_SERVICE_SERVER_ERROR_ON_CONN 11710
#define ER_GRP_RPL_UNREACHABLE_MAJORITY_TIMEOUT_FOR_MEMBER 11711
#define ER_GRP_RPL_SERVER_SET_TO_READ_ONLY_DUE_TO_ERRORS 11712
#define ER_GRP_RPL_GMS_LISTENER_FAILED_TO_LOG_NOTIFICATION 11713
#define ER_GRP_RPL_GRP_COMMUNICATION_ENG_INIT_FAILED 11714
#define ER_GRP_RPL_SET_GRP_COMMUNICATION_ENG_LOGGER_FAILED 11715
#define ER_GRP_RPL_DEBUG_OPTIONS 11716
#define ER_GRP_RPL_INVALID_DEBUG_OPTIONS 11717
#define ER_GRP_RPL_EXIT_GRP_GCS_ERROR 11718
#define ER_GRP_RPL_GRP_MEMBER_OFFLINE 11719
#define ER_GRP_RPL_GCS_INTERFACE_ERROR 11720
#define ER_GRP_RPL_FORCE_MEMBER_VALUE_SET_ERROR 11721
#define ER_GRP_RPL_FORCE_MEMBER_VALUE_SET 11722
#define ER_GRP_RPL_FORCE_MEMBER_VALUE_TIME_OUT 11723
#define ER_GRP_RPL_BROADCAST_COMMIT_MSSG_TOO_BIG 11724
#define ER_GRP_RPL_SEND_STATS_ERROR 11725
#define ER_GRP_RPL_MEMBER_STATS_INFO 11726
#define ER_GRP_RPL_FLOW_CONTROL_STATS 11727
#define ER_GRP_RPL_UNABLE_TO_CONVERT_PACKET_TO_EVENT 11728
#define ER_GRP_RPL_PIPELINE_CREATE_FAILED 11729
#define ER_GRP_RPL_PIPELINE_REINIT_FAILED_WRITE 11730
#define ER_GRP_RPL_UNABLE_TO_CONVERT_EVENT_TO_PACKET 11731
#define ER_GRP_RPL_PIPELINE_FLUSH_FAIL 11732
#define ER_GRP_RPL_PIPELINE_REINIT_FAILED_READ 11733
//#define OBSOLETE_ER_GRP_RPL_STOP_REP_CHANNEL 11734
#define ER_GRP_RPL_GCS_GR_ERROR_MSG 11735
#define ER_GRP_RPL_REPLICA_IO_THREAD_UNBLOCKED 11736
#define ER_GRP_RPL_REPLICA_IO_THREAD_ERROR_OUT 11737
#define ER_GRP_RPL_REPLICA_APPLIER_THREAD_UNBLOCKED 11738
#define ER_GRP_RPL_REPLICA_APPLIER_THREAD_ERROR_OUT 11739
#define ER_LDAP_AUTH_FAILED_TO_CREATE_OR_GET_CONNECTION 11740
#define ER_LDAP_AUTH_DEINIT_FAILED 11741
#define ER_LDAP_AUTH_SKIPPING_USER_GROUP_SEARCH 11742
#define ER_LDAP_AUTH_POOL_DISABLE_MAX_SIZE_ZERO 11743
#define ER_LDAP_AUTH_FAILED_TO_CREATE_LDAP_OBJECT_CREATOR 11744
#define ER_LDAP_AUTH_FAILED_TO_CREATE_LDAP_OBJECT 11745
#define ER_LDAP_AUTH_TLS_CONF 11746
#define ER_LDAP_AUTH_TLS_CONNECTION 11747
#define ER_LDAP_AUTH_CONN_POOL_NOT_CREATED 11748
#define ER_LDAP_AUTH_CONN_POOL_INITIALIZING 11749
#define ER_LDAP_AUTH_CONN_POOL_DEINITIALIZING 11750
#define ER_LDAP_AUTH_ZERO_MAX_POOL_SIZE_UNCHANGED 11751
#define ER_LDAP_AUTH_POOL_REINITIALIZING 11752
#define ER_LDAP_AUTH_FAILED_TO_WRITE_PACKET 11753
#define ER_LDAP_AUTH_SETTING_USERNAME 11754
#define ER_LDAP_AUTH_USER_AUTH_DATA 11755
//#define OBSOLETE_ER_LDAP_AUTH_INFO_FOR_USER 11756
#define ER_LDAP_AUTH_USER_GROUP_SEARCH_INFO 11757
#define ER_LDAP_AUTH_GRP_SEARCH_SPECIAL_HDL 11758
#define ER_LDAP_AUTH_GRP_IS_FULL_DN 11759
#define ER_LDAP_AUTH_USER_NOT_FOUND_IN_ANY_GRP 11760
#define ER_LDAP_AUTH_USER_FOUND_IN_MANY_GRPS 11761
#define ER_LDAP_AUTH_USER_HAS_MULTIPLE_GRP_NAMES 11762
#define ER_LDAP_AUTH_SEARCHED_USER_GRP_NAME 11763
#define ER_LDAP_AUTH_OBJECT_CREATE_TIMESTAMP 11764
#define ER_LDAP_AUTH_CERTIFICATE_NAME 11765
#define ER_LDAP_AUTH_FAILED_TO_POOL_DEINIT 11766
#define ER_LDAP_AUTH_FAILED_TO_INITIALIZE_POOL_IN_RECONSTRUCTING 11767
#define ER_LDAP_AUTH_FAILED_TO_INITIALIZE_POOL_IN_INIT_STATE 11768
#define ER_LDAP_AUTH_FAILED_TO_INITIALIZE_POOL_IN_DEINIT_STATE 11769
#define ER_LDAP_AUTH_FAILED_TO_DEINITIALIZE_POOL_IN_RECONSTRUCT_STATE 11770
#define ER_LDAP_AUTH_FAILED_TO_DEINITIALIZE_NOT_READY_POOL 11771
#define ER_LDAP_AUTH_FAILED_TO_GET_CONNECTION_AS_PLUGIN_NOT_READY 11772
#define ER_LDAP_AUTH_CONNECTION_POOL_INIT_FAILED 11773
#define ER_LDAP_AUTH_MAX_ALLOWED_CONNECTION_LIMIT_HIT 11774
#define ER_LDAP_AUTH_MAX_POOL_SIZE_SET_FAILED 11775
#define ER_LDAP_AUTH_PLUGIN_FAILED_TO_READ_PACKET 11776
#define ER_LDAP_AUTH_CREATING_LDAP_CONNECTION 11777
#define ER_LDAP_AUTH_GETTING_CONNECTION_FROM_POOL 11778
#define ER_LDAP_AUTH_RETURNING_CONNECTION_TO_POOL 11779
#define ER_LDAP_AUTH_SEARCH_USER_GROUP_ATTR_NOT_FOUND 11780
#define ER_LDAP_AUTH_LDAP_INFO_NULL 11781
#define ER_LDAP_AUTH_FREEING_CONNECTION 11782
#define ER_LDAP_AUTH_CONNECTION_PUSHED_TO_POOL 11783
#define ER_LDAP_AUTH_CONNECTION_CREATOR_ENTER 11784
#define ER_LDAP_AUTH_STARTING_TLS 11785
#define ER_LDAP_AUTH_CONNECTION_GET_LDAP_INFO_NULL 11786
#define ER_LDAP_AUTH_DELETING_CONNECTION_KEY 11787
#define ER_LDAP_AUTH_POOLED_CONNECTION_KEY 11788
#define ER_LDAP_AUTH_CREATE_CONNECTION_KEY 11789
#define ER_LDAP_AUTH_COMMUNICATION_HOST_INFO 11790
#define ER_LDAP_AUTH_METHOD_TO_CLIENT 11791
#define ER_LDAP_AUTH_SASL_REQUEST_FROM_CLIENT 11792
#define ER_LDAP_AUTH_SASL_PROCESS_SASL 11793
#define ER_LDAP_AUTH_SASL_BIND_SUCCESS_INFO 11794
#define ER_LDAP_AUTH_STARTED_FOR_USER 11795
#define ER_LDAP_AUTH_DISTINGUISHED_NAME 11796
#define ER_LDAP_AUTH_INIT_FAILED 11797
#define ER_LDAP_AUTH_OR_GROUP_RETRIEVAL_FAILED 11798
#define ER_LDAP_AUTH_USER_GROUP_SEARCH_FAILED 11799
#define ER_LDAP_AUTH_USER_BIND_FAILED 11800
#define ER_LDAP_AUTH_POOL_GET_FAILED_TO_CREATE_CONNECTION 11801
#define ER_LDAP_AUTH_FAILED_TO_CREATE_LDAP_CONNECTION 11802
#define ER_LDAP_AUTH_FAILED_TO_ESTABLISH_TLS_CONNECTION 11803
#define ER_LDAP_AUTH_FAILED_TO_SEARCH_DN 11804
#define ER_LDAP_AUTH_CONNECTION_POOL_REINIT_ENTER 11805
#define ER_SYSTEMD_NOTIFY_PATH_TOO_LONG 11806
#define ER_SYSTEMD_NOTIFY_CONNECT_FAILED 11807
#define ER_SYSTEMD_NOTIFY_WRITE_FAILED 11808
#define ER_FOUND_MISSING_GTIDS 11809
#define ER_PID_FILE_PRIV_DIRECTORY_INSECURE 11810
#define ER_CANT_CHECK_PID_PATH 11811
#define ER_VALIDATE_PWD_STATUS_VAR_REGISTRATION_FAILED 11812
#define ER_VALIDATE_PWD_STATUS_VAR_UNREGISTRATION_FAILED 11813
#define ER_VALIDATE_PWD_DICT_FILE_OPEN_FAILED 11814
#define ER_VALIDATE_PWD_COULD_BE_NULL 11815
#define ER_VALIDATE_PWD_STRING_CONV_TO_LOWERCASE_FAILED 11816
#define ER_VALIDATE_PWD_STRING_CONV_TO_BUFFER_FAILED 11817
#define ER_VALIDATE_PWD_STRING_HANDLER_MEM_ALLOCATION_FAILED 11818
#define ER_VALIDATE_PWD_STRONG_POLICY_DICT_FILE_UNSPECIFIED 11819
#define ER_VALIDATE_PWD_CONVERT_TO_BUFFER_FAILED 11820
#define ER_VALIDATE_PWD_VARIABLE_REGISTRATION_FAILED 11821
#define ER_VALIDATE_PWD_VARIABLE_UNREGISTRATION_FAILED 11822
#define ER_KEYRING_MIGRATION_EXTRA_OPTIONS 11823
//#define OBSOLETE_ER_INVALID_DEFAULT_UTF8MB4_COLLATION 11824
#define ER_IB_MSG_0 11825
#define ER_IB_MSG_1 11826
#define ER_IB_MSG_2 11827
#define ER_IB_MSG_3 11828
#define ER_IB_MSG_4 11829
#define ER_IB_MSG_5 11830
#define ER_IB_MSG_6 11831
#define ER_IB_MSG_7 11832
#define ER_IB_MSG_8 11833
#define ER_IB_MSG_9 11834
#define ER_IB_MSG_10 11835
#define ER_IB_MSG_11 11836
#define ER_IB_MSG_12 11837
#define ER_IB_MSG_13 11838
#define ER_IB_MSG_14 11839
#define ER_IB_MSG_15 11840
#define ER_IB_MSG_16 11841
#define ER_IB_MSG_17 11842
#define ER_IB_MSG_18 11843
#define ER_IB_MSG_19 11844
#define ER_IB_MSG_20 11845
#define ER_IB_MSG_21 11846
#define ER_IB_MSG_22 11847
#define ER_IB_MSG_23 11848
#define ER_IB_MSG_24 11849
#define ER_IB_MSG_25 11850
#define ER_IB_MSG_26 11851
#define ER_IB_MSG_27 11852
#define ER_IB_MSG_28 11853
#define ER_IB_MSG_29 11854
#define ER_IB_MSG_30 11855
//#define OBSOLETE_ER_IB_MSG_31 11856
#define ER_IB_MSG_32 11857
#define ER_IB_MSG_33 11858
#define ER_IB_MSG_34 11859
#define ER_IB_MSG_35 11860
#define ER_IB_MSG_36 11861
#define ER_IB_MSG_37 11862
#define ER_IB_MSG_38 11863
#define ER_IB_MSG_39 11864
#define ER_IB_MSG_40 11865
#define ER_IB_MSG_41 11866
#define ER_IB_MSG_42 11867
#define ER_IB_MSG_43 11868
#define ER_IB_MSG_44 11869
#define ER_IB_MSG_45 11870
#define ER_IB_MSG_46 11871
#define ER_IB_MSG_47 11872
#define ER_IB_MSG_48 11873
#define ER_IB_MSG_49 11874
#define ER_IB_MSG_50 11875
#define ER_IB_MSG_51 11876
#define ER_IB_MSG_52 11877
#define ER_IB_MSG_53 11878
#define ER_IB_MSG_54 11879
#define ER_IB_MSG_55 11880
#define ER_IB_MSG_56 11881
#define ER_IB_MSG_57 11882
#define ER_IB_MSG_58 11883
#define ER_IB_MSG_59 11884
#define ER_IB_MSG_60 11885
#define ER_IB_MSG_61 11886
#define ER_IB_MSG_62 11887
#define ER_IB_MSG_63 11888
#define ER_IB_MSG_64 11889
#define ER_IB_MSG_65 11890
#define ER_IB_MSG_66 11891
#define ER_IB_MSG_67 11892
#define ER_IB_MSG_68 11893
#define ER_IB_MSG_69 11894
#define ER_IB_MSG_70 11895
#define ER_IB_MSG_71 11896
#define ER_IB_MSG_72 11897
#define ER_IB_MSG_73 11898
#define ER_IB_MSG_74 11899
#define ER_IB_MSG_75 11900
#define ER_IB_MSG_76 11901
#define ER_IB_MSG_77 11902
#define ER_IB_MSG_78 11903
#define ER_IB_MSG_79 11904
#define ER_IB_MSG_80 11905
#define ER_IB_MSG_81 11906
#define ER_IB_MSG_82 11907
#define ER_IB_ERR_PAGE_DIRTY_AT_SHUTDOWN 11908
#define ER_IB_MSG_84 11909
#define ER_IB_MSG_85 11910
#define ER_IB_MSG_86 11911
//#define OBSOLETE_ER_IB_MSG_87 11912
//#define OBSOLETE_ER_IB_MSG_88 11913
//#define OBSOLETE_ER_IB_MSG_89 11914
//#define OBSOLETE_ER_IB_MSG_90 11915
//#define OBSOLETE_ER_IB_MSG_91 11916
//#define OBSOLETE_ER_IB_MSG_92 11917
//#define OBSOLETE_ER_IB_MSG_93 11918
//#define OBSOLETE_ER_IB_MSG_94 11919
#define ER_IB_MSG_95 11920
#define ER_IB_MSG_96 11921
#define ER_IB_MSG_97 11922
#define ER_IB_MSG_98 11923
#define ER_IB_MSG_99 11924
#define ER_IB_MSG_100 11925
#define ER_IB_MSG_101 11926
#define ER_IB_MSG_102 11927
#define ER_IB_MSG_103 11928
#define ER_IB_MSG_104 11929
#define ER_IB_MSG_105 11930
#define ER_IB_MSG_106 11931
#define ER_IB_MSG_107 11932
#define ER_IB_MSG_108 11933
#define ER_IB_MSG_109 11934
#define ER_IB_MSG_110 11935
#define ER_IB_MSG_111 11936
#define ER_IB_MSG_112 11937
//#define OBSOLETE_ER_IB_MSG_113 11938
//#define OBSOLETE_ER_IB_MSG_114 11939
//#define OBSOLETE_ER_IB_MSG_115 11940
//#define OBSOLETE_ER_IB_MSG_116 11941
//#define OBSOLETE_ER_IB_MSG_117 11942
//#define OBSOLETE_ER_IB_MSG_118 11943
#define ER_IB_MSG_119 11944
#define ER_IB_MSG_120 11945
#define ER_IB_MSG_121 11946
#define ER_IB_MSG_122 11947
#define ER_IB_MSG_123 11948
#define ER_IB_MSG_124 11949
#define ER_IB_MSG_125 11950
#define ER_IB_MSG_126 11951
#define ER_IB_MSG_127 11952
#define ER_IB_MSG_128 11953
#define ER_IB_MSG_129 11954
#define ER_IB_MSG_130 11955
#define ER_IB_MSG_131 11956
#define ER_IB_MSG_132 11957
#define ER_IB_MSG_133 11958
#define ER_IB_MSG_134 11959
#define ER_IB_MSG_135 11960
#define ER_IB_MSG_136 11961
#define ER_IB_MSG_137 11962
#define ER_IB_MSG_138 11963
#define ER_IB_MSG_139 11964
#define ER_IB_MSG_140 11965
#define ER_IB_MSG_141 11966
#define ER_IB_MSG_142 11967
#define ER_IB_MSG_143 11968
#define ER_IB_MSG_144 11969
#define ER_IB_MSG_145 11970
#define ER_IB_MSG_146 11971
#define ER_IB_MSG_147 11972
#define ER_IB_MSG_148 11973
#define ER_IB_CLONE_INTERNAL 11974
#define ER_IB_CLONE_TIMEOUT 11975
#define ER_IB_CLONE_STATUS_FILE 11976
#define ER_IB_CLONE_SQL 11977
#define ER_IB_CLONE_VALIDATE 11978
#define ER_IB_CLONE_PUNCH_HOLE 11979
#define ER_IB_CLONE_GTID_PERSIST 11980
#define ER_IB_MSG_156 11981
#define ER_IB_MSG_157 11982
#define ER_IB_MSG_158 11983
#define ER_IB_MSG_159 11984
#define ER_IB_MSG_160 11985
#define ER_IB_MSG_161 11986
#define ER_IB_MSG_162 11987
#define ER_IB_MSG_163 11988
#define ER_IB_MSG_164 11989
#define ER_IB_MSG_165 11990
#define ER_IB_MSG_166 11991
#define ER_IB_MSG_167 11992
#define ER_IB_MSG_168 11993
#define ER_IB_MSG_169 11994
#define ER_IB_MSG_170 11995
#define ER_IB_MSG_171 11996
#define ER_IB_MSG_172 11997
#define ER_IB_MSG_173 11998
#define ER_IB_MSG_174 11999
#define ER_IB_MSG_175 12000
#define ER_IB_MSG_176 12001
#define ER_IB_MSG_177 12002
#define ER_IB_MSG_178 12003
#define ER_IB_MSG_179 12004
#define ER_IB_MSG_180 12005
#define ER_IB_LONG_AHI_DISABLE_WAIT 12006
#define ER_IB_MSG_182 12007
#define ER_IB_MSG_183 12008
#define ER_IB_MSG_184 12009
//#define OBSOLETE_ER_IB_MSG_185 12010
//#define OBSOLETE_ER_IB_MSG_186 12011
#define ER_IB_MSG_187 12012
#define ER_IB_MSG_188 12013
#define ER_IB_MSG_189 12014
#define ER_IB_MSG_190 12015
#define ER_IB_MSG_191 12016
#define ER_IB_MSG_192 12017
#define ER_IB_MSG_193 12018
#define ER_IB_MSG_194 12019
#define ER_IB_MSG_195 12020
#define ER_IB_MSG_196 12021
#define ER_IB_MSG_197 12022
#define ER_IB_MSG_198 12023
#define ER_IB_MSG_199 12024
#define ER_IB_MSG_200 12025
#define ER_IB_MSG_201 12026
#define ER_IB_MSG_202 12027
#define ER_IB_MSG_203 12028
#define ER_IB_MSG_204 12029
#define ER_IB_MSG_205 12030
#define ER_IB_MSG_206 12031
#define ER_IB_MSG_207 12032
#define ER_IB_MSG_208 12033
#define ER_IB_MSG_209 12034
#define ER_IB_MSG_210 12035
#define ER_IB_MSG_211 12036
#define ER_IB_MSG_212 12037
#define ER_IB_MSG_213 12038
#define ER_IB_MSG_214 12039
#define ER_IB_MSG_215 12040
#define ER_IB_MSG_216 12041
#define ER_IB_MSG_217 12042
#define ER_IB_MSG_218 12043
#define ER_IB_MSG_219 12044
#define ER_IB_MSG_220 12045
#define ER_IB_MSG_221 12046
#define ER_IB_MSG_222 12047
#define ER_IB_MSG_223 12048
#define ER_IB_MSG_224 12049
#define ER_IB_MSG_225 12050
#define ER_IB_MSG_226 12051
//#define OBSOLETE_ER_IB_MSG_227 12052
//#define OBSOLETE_ER_IB_MSG_228 12053
#define ER_IB_MSG_229 12054
#define ER_IB_MSG_230 12055
#define ER_IB_MSG_231 12056
#define ER_IB_MSG_232 12057
#define ER_IB_MSG_233 12058
#define ER_IB_MSG_234 12059
#define ER_IB_MSG_235 12060
#define ER_IB_MSG_236 12061
#define ER_IB_MSG_237 12062
#define ER_IB_MSG_238 12063
#define ER_IB_MSG_239 12064
#define ER_IB_MSG_240 12065
#define ER_IB_MSG_241 12066
#define ER_IB_MSG_242 12067
#define ER_IB_MSG_243 12068
#define ER_IB_MSG_244 12069
#define ER_IB_MSG_245 12070
#define ER_IB_MSG_246 12071
#define ER_IB_MSG_247 12072
#define ER_IB_MSG_248 12073
#define ER_IB_MSG_249 12074
#define ER_IB_MSG_250 12075
#define ER_IB_MSG_251 12076
#define ER_IB_MSG_252 12077
#define ER_IB_MSG_253 12078
#define ER_IB_MSG_254 12079
#define ER_IB_MSG_255 12080
#define ER_IB_MSG_256 12081
#define ER_IB_MSG_257 12082
#define ER_IB_MSG_258 12083
#define ER_IB_MSG_259 12084
#define ER_IB_MSG_260 12085
#define ER_IB_MSG_261 12086
#define ER_IB_MSG_262 12087
#define ER_IB_MSG_263 12088
#define ER_IB_MSG_264 12089
#define ER_IB_MSG_265 12090
#define ER_IB_MSG_266 12091
#define ER_IB_MSG_267 12092
#define ER_IB_MSG_268 12093
#define ER_IB_MSG_269 12094
#define ER_IB_MSG_270 12095
#define ER_IB_MSG_271 12096
#define ER_IB_MSG_272 12097
#define ER_IB_MSG_273 12098
//#define OBSOLETE_ER_IB_MSG_274 12099
//#define OBSOLETE_ER_IB_MSG_275 12100
//#define OBSOLETE_ER_IB_MSG_276 12101
//#define OBSOLETE_ER_IB_MSG_277 12102
#define ER_IB_MSG_278 12103
//#define OBSOLETE_ER_IB_MSG_279 12104
#define ER_IB_MSG_280 12105
#define ER_IB_MSG_281 12106
#define ER_IB_MSG_282 12107
#define ER_IB_MSG_283 12108
#define ER_IB_MSG_284 12109
#define ER_IB_MSG_285 12110
#define ER_IB_WARN_ACCESSING_NONEXISTINC_SPACE 12111
#define ER_IB_MSG_287 12112
#define ER_IB_MSG_288 12113
#define ER_IB_MSG_289 12114
//#define OBSOLETE_ER_IB_MSG_290 12115
#define ER_IB_MSG_291 12116
#define ER_IB_MSG_292 12117
#define ER_IB_MSG_293 12118
#define ER_IB_MSG_294 12119
#define ER_IB_MSG_295 12120
#define ER_IB_MSG_296 12121
#define ER_IB_MSG_297 12122
#define ER_IB_MSG_298 12123
#define ER_IB_MSG_299 12124
#define ER_IB_MSG_300 12125
#define ER_IB_MSG_301 12126
#define ER_IB_MSG_UNEXPECTED_FILE_EXISTS 12127
#define ER_IB_MSG_303 12128
#define ER_IB_MSG_304 12129
#define ER_IB_MSG_305 12130
#define ER_IB_MSG_306 12131
#define ER_IB_MSG_307 12132
#define ER_IB_MSG_308 12133
#define ER_IB_MSG_309 12134
#define ER_IB_MSG_310 12135
#define ER_IB_MSG_311 12136
#define ER_IB_MSG_312 12137
#define ER_IB_MSG_313 12138
#define ER_IB_MSG_314 12139
#define ER_IB_MSG_315 12140
#define ER_IB_MSG_316 12141
#define ER_IB_MSG_317 12142
#define ER_IB_MSG_318 12143
#define ER_IB_MSG_319 12144
#define ER_IB_MSG_320 12145
#define ER_IB_MSG_321 12146
#define ER_IB_MSG_322 12147
#define ER_IB_MSG_323 12148
#define ER_IB_MSG_324 12149
#define ER_IB_MSG_325 12150
#define ER_IB_MSG_326 12151
//#define OBSOLETE_ER_IB_MSG_327 12152
#define ER_IB_MSG_328 12153
#define ER_IB_MSG_329 12154
#define ER_IB_MSG_330 12155
#define ER_IB_MSG_331 12156
#define ER_IB_MSG_332 12157
#define ER_IB_MSG_333 12158
#define ER_IB_MSG_334 12159
#define ER_IB_MSG_335 12160
#define ER_IB_MSG_336 12161
#define ER_IB_MSG_337 12162
#define ER_IB_MSG_338 12163
#define ER_IB_MSG_339 12164
#define ER_IB_MSG_340 12165
#define ER_IB_MSG_341 12166
#define ER_IB_MSG_342 12167
#define ER_IB_MSG_343 12168
#define ER_IB_MSG_344 12169
#define ER_IB_MSG_345 12170
#define ER_IB_MSG_346 12171
#define ER_IB_MSG_347 12172
#define ER_IB_MSG_348 12173
#define ER_IB_MSG_349 12174
#define ER_IB_MSG_350 12175
//#define OBSOLETE_ER_IB_MSG_351 12176
#define ER_IB_MSG_UNPROTECTED_LOCATION_ALLOWED 12177
//#define OBSOLETE_ER_IB_MSG_353 12178
#define ER_IB_MSG_354 12179
#define ER_IB_MSG_355 12180
#define ER_IB_MSG_356 12181
#define ER_IB_MSG_357 12182
#define ER_IB_MSG_358 12183
#define ER_IB_MSG_359 12184
#define ER_IB_MSG_360 12185
#define ER_IB_MSG_361 12186
#define ER_IB_MSG_362 12187
//#define OBSOLETE_ER_IB_MSG_363 12188
#define ER_IB_MSG_364 12189
#define ER_IB_MSG_365 12190
#define ER_IB_MSG_IGNORE_SCAN_PATH 12191
#define ER_IB_MSG_367 12192
#define ER_IB_MSG_368 12193
#define ER_IB_MSG_369 12194
#define ER_IB_MSG_370 12195
#define ER_IB_MSG_371 12196
#define ER_IB_MSG_372 12197
#define ER_IB_MSG_373 12198
#define ER_IB_MSG_374 12199
#define ER_IB_MSG_375 12200
#define ER_IB_MSG_376 12201
#define ER_IB_MSG_377 12202
#define ER_IB_MSG_378 12203
#define ER_IB_MSG_379 12204
#define ER_IB_MSG_380 12205
#define ER_IB_MSG_381 12206
#define ER_IB_MSG_382 12207
#define ER_IB_MSG_383 12208
#define ER_IB_MSG_384 12209
#define ER_IB_MSG_385 12210
#define ER_IB_MSG_386 12211
#define ER_IB_MSG_387 12212
#define ER_IB_MSG_GENERAL_TABLESPACE_UNDER_DATADIR 12213
#define ER_IB_MSG_IMPLICIT_TABLESPACE_IN_DATADIR 12214
#define ER_IB_MSG_390 12215
#define ER_IB_MSG_391 12216
#define ER_IB_MSG_392 12217
#define ER_IB_MSG_393 12218
#define ER_IB_MSG_394 12219
#define ER_IB_MSG_395 12220
#define ER_IB_MSG_396 12221
#define ER_IB_MSG_397 12222
#define ER_IB_MSG_398 12223
#define ER_IB_MSG_399 12224
//#define OBSOLETE_ER_IB_MSG_400 12225
#define ER_IB_MSG_401 12226
#define ER_IB_MSG_402 12227
#define ER_IB_MSG_403 12228
#define ER_IB_MSG_404 12229
#define ER_IB_MSG_405 12230
#define ER_IB_MSG_406 12231
#define ER_IB_MSG_407 12232
#define ER_IB_MSG_408 12233
#define ER_IB_MSG_409 12234
#define ER_IB_MSG_410 12235
#define ER_IB_MSG_411 12236
#define ER_IB_MSG_412 12237
#define ER_IB_MSG_413 12238
#define ER_IB_MSG_414 12239
#define ER_IB_MSG_415 12240
#define ER_IB_MSG_416 12241
#define ER_IB_MSG_417 12242
#define ER_IB_MSG_418 12243
#define ER_IB_MSG_419 12244
#define ER_IB_MSG_420 12245
#define ER_IB_MSG_421 12246
#define ER_IB_MSG_422 12247
#define ER_IB_MSG_423 12248
#define ER_IB_MSG_424 12249
#define ER_IB_MSG_425 12250
#define ER_IB_MSG_426 12251
#define ER_IB_MSG_427 12252
#define ER_IB_MSG_428 12253
#define ER_IB_MSG_429 12254
#define ER_IB_MSG_430 12255
#define ER_IB_MSG_431 12256
#define ER_IB_MSG_432 12257
#define ER_IB_MSG_433 12258
#define ER_IB_MSG_434 12259
#define ER_IB_MSG_435 12260
#define ER_IB_MSG_436 12261
#define ER_IB_MSG_437 12262
#define ER_IB_MSG_438 12263
#define ER_IB_MSG_439 12264
#define ER_IB_MSG_440 12265
#define ER_IB_MSG_441 12266
#define ER_IB_MSG_442 12267
#define ER_IB_MSG_443 12268
//#define OBSOLETE_ER_IB_MSG_444 12269
#define ER_IB_MSG_445 12270
#define ER_IB_MSG_446 12271
#define ER_IB_MSG_447 12272
#define ER_IB_MSG_448 12273
#define ER_IB_MSG_449 12274
#define ER_IB_MSG_450 12275
#define ER_IB_MSG_451 12276
#define ER_IB_MSG_452 12277
#define ER_IB_MSG_453 12278
#define ER_IB_MSG_454 12279
#define ER_IB_MSG_455 12280
#define ER_IB_MSG_456 12281
#define ER_IB_MSG_457 12282
#define ER_IB_MSG_458 12283
#define ER_IB_MSG_459 12284
#define ER_IB_MSG_460 12285
#define ER_IB_MSG_461 12286
#define ER_IB_MSG_462 12287
#define ER_IB_MSG_463 12288
#define ER_IB_MSG_464 12289
#define ER_IB_MSG_465 12290
#define ER_IB_MSG_466 12291
#define ER_IB_MSG_467 12292
#define ER_IB_MSG_468 12293
#define ER_IB_MSG_469 12294
#define ER_IB_MSG_470 12295
#define ER_IB_MSG_471 12296
#define ER_IB_MSG_472 12297
#define ER_IB_MSG_473 12298
#define ER_IB_MSG_474 12299
#define ER_IB_MSG_475 12300
#define ER_IB_MSG_476 12301
#define ER_IB_MSG_477 12302
#define ER_IB_MSG_478 12303
#define ER_IB_MSG_479 12304
#define ER_IB_MSG_480 12305
#define ER_IB_MSG_481 12306
#define ER_IB_MSG_482 12307
#define ER_IB_MSG_483 12308
#define ER_IB_MSG_484 12309
#define ER_IB_MSG_485 12310
#define ER_IB_MSG_486 12311
#define ER_IB_MSG_487 12312
#define ER_IB_MSG_488 12313
#define ER_IB_MSG_489 12314
#define ER_IB_MSG_490 12315
#define ER_IB_MSG_491 12316
#define ER_IB_MSG_492 12317
#define ER_IB_MSG_493 12318
#define ER_IB_MSG_494 12319
#define ER_IB_MSG_495 12320
#define ER_IB_MSG_496 12321
#define ER_IB_MSG_497 12322
#define ER_IB_MSG_498 12323
#define ER_IB_MSG_499 12324
#define ER_IB_MSG_500 12325
#define ER_IB_MSG_501 12326
#define ER_IB_MSG_502 12327
#define ER_IB_MSG_503 12328
#define ER_IB_MSG_504 12329
#define ER_IB_MSG_505 12330
#define ER_IB_MSG_506 12331
#define ER_IB_MSG_507 12332
#define ER_IB_MSG_508 12333
#define ER_IB_MSG_509 12334
#define ER_IB_MSG_510 12335
#define ER_IB_MSG_511 12336
#define ER_IB_MSG_512 12337
#define ER_IB_MSG_513 12338
#define ER_IB_MSG_514 12339
#define ER_IB_MSG_515 12340
#define ER_IB_MSG_516 12341
#define ER_IB_MSG_517 12342
#define ER_IB_MSG_518 12343
#define ER_IB_MSG_519 12344
#define ER_IB_MSG_520 12345
#define ER_IB_MSG_521 12346
#define ER_IB_MSG_522 12347
#define ER_IB_MSG_523 12348
#define ER_IB_MSG_524 12349
#define ER_IB_MSG_525 12350
#define ER_IB_MSG_526 12351
#define ER_IB_MSG_527 12352
//#define OBSOLETE_ER_IB_MSG_528 12353
//#define OBSOLETE_ER_IB_MSG_529 12354
#define ER_IB_MSG_530 12355
#define ER_IB_MSG_531 12356
#define ER_IB_MSG_532 12357
#define ER_IB_MSG_533 12358
#define ER_IB_MSG_534 12359
//#define OBSOLETE_ER_IB_MSG_535 12360
//#define OBSOLETE_ER_IB_MSG_536 12361
#define ER_IB_MSG_537 12362
#define ER_IB_MSG_538 12363
#define ER_IB_MSG_539 12364
#define ER_IB_MSG_540 12365
#define ER_IB_MSG_541 12366
//#define OBSOLETE_ER_IB_MSG_542 12367
#define ER_IB_MSG_543 12368
#define ER_IB_MSG_544 12369
#define ER_IB_MSG_545 12370
#define ER_IB_MSG_546 12371
#define ER_IB_MSG_547 12372
#define ER_IB_MSG_548 12373
#define ER_IB_MSG_549 12374
#define ER_IB_MSG_550 12375
#define ER_IB_MSG_551 12376
#define ER_IB_MSG_552 12377
#define ER_IB_MSG_553 12378
#define ER_IB_MSG_554 12379
#define ER_IB_MSG_555 12380
#define ER_IB_MSG_556 12381
#define ER_IB_MSG_557 12382
#define ER_IB_MSG_558 12383
#define ER_IB_MSG_559 12384
#define ER_IB_MSG_560 12385
#define ER_IB_MSG_561 12386
#define ER_IB_MSG_562 12387
#define ER_IB_MSG_563 12388
#define ER_IB_MSG_564 12389
#define ER_IB_MSG_INVALID_LOCATION_FOR_TABLE 12390
#define ER_IB_MSG_566 12391
#define ER_IB_MSG_567 12392
#define ER_IB_MSG_568 12393
#define ER_IB_MSG_569 12394
#define ER_IB_MSG_570 12395
#define ER_IB_MSG_571 12396
//#define OBSOLETE_ER_IB_MSG_572 12397
#define ER_IB_MSG_573 12398
#define ER_IB_MSG_574 12399
//#define OBSOLETE_ER_IB_MSG_575 12400
//#define OBSOLETE_ER_IB_MSG_576 12401
//#define OBSOLETE_ER_IB_MSG_577 12402
#define ER_IB_MSG_578 12403
#define ER_IB_MSG_579 12404
#define ER_IB_MSG_580 12405
#define ER_IB_MSG_581 12406
#define ER_IB_MSG_582 12407
#define ER_IB_MSG_583 12408
#define ER_IB_MSG_584 12409
#define ER_IB_MSG_585 12410
#define ER_IB_MSG_586 12411
#define ER_IB_MSG_587 12412
#define ER_IB_MSG_588 12413
#define ER_IB_MSG_589 12414
#define ER_IB_MSG_590 12415
#define ER_IB_MSG_591 12416
#define ER_IB_MSG_592 12417
#define ER_IB_MSG_593 12418
#define ER_IB_MSG_594 12419
#define ER_IB_MSG_595 12420
#define ER_IB_MSG_596 12421
#define ER_IB_MSG_597 12422
#define ER_IB_MSG_598 12423
#define ER_IB_MSG_599 12424
#define ER_IB_MSG_600 12425
#define ER_IB_MSG_601 12426
#define ER_IB_MSG_602 12427
#define ER_IB_MSG_603 12428
#define ER_IB_MSG_604 12429
#define ER_IB_MSG_605 12430
#define ER_IB_MSG_606 12431
#define ER_IB_MSG_607 12432
#define ER_IB_MSG_608 12433
#define ER_IB_MSG_609 12434
//#define OBSOLETE_ER_IB_MSG_610 12435
#define ER_IB_MSG_611 12436
#define ER_IB_MSG_612 12437
#define ER_IB_MSG_613 12438
#define ER_IB_MSG_614 12439
#define ER_IB_MSG_615 12440
#define ER_IB_MSG_616 12441
#define ER_IB_MSG_617 12442
//#define OBSOLETE_ER_IB_MSG_618 12443
#define ER_IB_MSG_619 12444
#define ER_IB_MSG_IBUF_CURSOR_RESTORATION_FAILED 12445
//#define OBSOLETE_ER_IB_MSG_621 12446
#define ER_IB_MSG_IBUF_FAILED_TO_RESTORE_POSITION 12447
#define ER_IB_MSG_623 12448
#define ER_IB_MSG_624 12449
//#define OBSOLETE_ER_IB_MSG_625 12450
#define ER_IB_MSG_626 12451
#define ER_IB_MSG_627 12452
#define ER_IB_MSG_628 12453
#define ER_IB_MSG_629 12454
#define ER_IB_MSG_630 12455
#define ER_IB_MSG_631 12456
#define ER_IB_MSG_632 12457
#define ER_IB_MSG_633 12458
#define ER_IB_MSG_634 12459
#define ER_IB_MSG_635 12460
#define ER_IB_MSG_636 12461
#define ER_IB_MSG_637 12462
#define ER_IB_MSG_638 12463
#define ER_IB_MSG_639 12464
//#define OBSOLETE_ER_IB_MSG_640 12465
//#define OBSOLETE_ER_IB_MSG_641 12466
#define ER_IB_MSG_642 12467
#define ER_IB_MSG_643 12468
#define ER_IB_MSG_644 12469
#define ER_IB_MSG_645 12470
#define ER_IB_MSG_646 12471
#define ER_IB_MSG_647 12472
#define ER_IB_MSG_648 12473
#define ER_IB_MSG_649 12474
#define ER_IB_MSG_650 12475
#define ER_IB_MSG_651 12476
#define ER_IB_MSG_652 12477
#define ER_IB_MSG_DDL_LOG_DELETE_BY_ID_OK 12478
#define ER_IB_MSG_654 12479
#define ER_IB_MSG_655 12480
#define ER_IB_MSG_656 12481
#define ER_IB_MSG_657 12482
#define ER_IB_MSG_658 12483
#define ER_IB_MSG_659 12484
#define ER_IB_MSG_660 12485
#define ER_IB_MSG_661 12486
#define ER_IB_MSG_662 12487
#define ER_IB_MSG_663 12488
//#define OBSOLETE_ER_IB_MSG_664 12489
//#define OBSOLETE_ER_IB_MSG_665 12490
//#define OBSOLETE_ER_IB_MSG_666 12491
//#define OBSOLETE_ER_IB_MSG_667 12492
//#define OBSOLETE_ER_IB_MSG_668 12493
//#define OBSOLETE_ER_IB_MSG_669 12494
//#define OBSOLETE_ER_IB_MSG_670 12495
//#define OBSOLETE_ER_IB_MSG_671 12496
//#define OBSOLETE_ER_IB_MSG_672 12497
//#define OBSOLETE_ER_IB_MSG_673 12498
//#define OBSOLETE_ER_IB_MSG_674 12499
//#define OBSOLETE_ER_IB_MSG_675 12500
//#define OBSOLETE_ER_IB_MSG_676 12501
//#define OBSOLETE_ER_IB_MSG_677 12502
//#define OBSOLETE_ER_IB_MSG_678 12503
//#define OBSOLETE_ER_IB_MSG_679 12504
//#define OBSOLETE_ER_IB_MSG_680 12505
//#define OBSOLETE_ER_IB_MSG_681 12506
//#define OBSOLETE_ER_IB_MSG_682 12507
//#define OBSOLETE_ER_IB_MSG_683 12508
//#define OBSOLETE_ER_IB_MSG_684 12509
//#define OBSOLETE_ER_IB_MSG_685 12510
//#define OBSOLETE_ER_IB_MSG_686 12511
//#define OBSOLETE_ER_IB_MSG_687 12512
//#define OBSOLETE_ER_IB_MSG_688 12513
//#define OBSOLETE_ER_IB_MSG_689 12514
//#define OBSOLETE_ER_IB_MSG_690 12515
//#define OBSOLETE_ER_IB_MSG_691 12516
//#define OBSOLETE_ER_IB_MSG_692 12517
//#define OBSOLETE_ER_IB_MSG_693 12518
#define ER_IB_MSG_694 12519
#define ER_IB_MSG_695 12520
#define ER_IB_MSG_696 12521
#define ER_IB_MSG_697 12522
#define ER_IB_MSG_LOG_CORRUPT 12523
#define ER_IB_MSG_699 12524
#define ER_IB_MSG_LOG_FORMAT_OLD_AND_LOG_CORRUPTED 12525
#define ER_IB_MSG_LOG_FORMAT_OLD_AND_NO_CLEAN_SHUTDOWN 12526
//#define OBSOLETE_ER_IB_MSG_702 12527
//#define OBSOLETE_ER_IB_MSG_703 12528
#define ER_IB_MSG_LOG_FORMAT_BEFORE_8_0_30 12529
#define ER_IB_MSG_LOG_FILE_FORMAT_UNKNOWN 12530
#define ER_IB_MSG_RECOVERY_CHECKPOINT_NOT_FOUND 12531
#define ER_IB_MSG_707 12532
#define ER_IB_MSG_708 12533
#define ER_IB_MSG_709 12534
#define ER_IB_MSG_710 12535
#define ER_IB_MSG_711 12536
#define ER_IB_MSG_712 12537
#define ER_IB_MSG_713 12538
#define ER_IB_MSG_714 12539
#define ER_IB_MSG_715 12540
#define ER_IB_MSG_716 12541
//#define OBSOLETE_ER_IB_MSG_717 12542
#define ER_IB_MSG_718 12543
#define ER_IB_MSG_719 12544
#define ER_IB_MSG_720 12545
#define ER_IB_MSG_RECOVERY_SKIPPED_IN_READ_ONLY_MODE 12546
#define ER_IB_MSG_722 12547
#define ER_IB_MSG_723 12548
#define ER_IB_MSG_724 12549
#define ER_IB_MSG_725 12550
#define ER_IB_MSG_726 12551
#define ER_IB_MSG_727 12552
#define ER_IB_MSG_728 12553
#define ER_IB_MSG_LOG_FILES_CREATED_BY_MEB_AND_READ_ONLY_MODE 12554
#define ER_IB_MSG_LOG_FILES_CREATED_BY_MEB 12555
#define ER_IB_MSG_LOG_FILES_CREATED_BY_CLONE 12556
#define ER_IB_MSG_LOG_FORMAT_OLD 12557
//#define OBSOLETE_ER_IB_MSG_LOG_FORMAT_NOT_SUPPORTED 12558
#define ER_IB_MSG_RECOVERY_CHECKPOINT_FROM_BEFORE_CLEAN_SHUTDOWN 12559
#define ER_IB_MSG_RECOVERY_IS_NEEDED 12560
#define ER_IB_MSG_RECOVERY_IN_READ_ONLY 12561
#define ER_IB_MSG_737 12562
#define ER_IB_MSG_738 12563
#define ER_IB_MSG_739 12564
#define ER_IB_MSG_740 12565
#define ER_IB_MSG_741 12566
#define ER_IB_MSG_742 12567
#define ER_IB_MSG_743 12568
#define ER_IB_MSG_744 12569
#define ER_IB_MSG_745 12570
#define ER_IB_MSG_746 12571
#define ER_IB_MSG_747 12572
#define ER_IB_MSG_748 12573
#define ER_IB_MSG_749 12574
#define ER_IB_MSG_750 12575
#define ER_IB_MSG_751 12576
#define ER_IB_MSG_752 12577
#define ER_IB_MSG_753 12578
#define ER_IB_MSG_754 12579
#define ER_IB_MSG_755 12580
#define ER_IB_MSG_756 12581
#define ER_IB_MSG_757 12582
#define ER_IB_MSG_758 12583
#define ER_IB_MSG_759 12584
#define ER_IB_MSG_760 12585
#define ER_IB_MSG_761 12586
#define ER_IB_MSG_762 12587
#define ER_IB_MSG_763 12588
#define ER_IB_MSG_764 12589
#define ER_IB_MSG_765 12590
#define ER_IB_MSG_766 12591
#define ER_IB_MSG_767 12592
#define ER_IB_MSG_768 12593
#define ER_IB_MSG_769 12594
#define ER_IB_MSG_770 12595
#define ER_IB_MSG_771 12596
#define ER_IB_MSG_772 12597
#define ER_IB_MSG_773 12598
#define ER_IB_MSG_774 12599
#define ER_IB_MSG_775 12600
#define ER_IB_MSG_776 12601
#define ER_IB_MSG_777 12602
#define ER_IB_MSG_778 12603
#define ER_IB_MSG_779 12604
#define ER_IB_MSG_780 12605
#define ER_IB_MSG_781 12606
#define ER_IB_MSG_782 12607
#define ER_IB_MSG_783 12608
#define ER_IB_MSG_784 12609
#define ER_IB_MSG_785 12610
#define ER_IB_MSG_786 12611
#define ER_IB_MSG_787 12612
#define ER_IB_MSG_788 12613
#define ER_IB_MSG_789 12614
#define ER_IB_MSG_790 12615
#define ER_IB_MSG_791 12616
#define ER_IB_MSG_792 12617
#define ER_IB_MSG_793 12618
#define ER_IB_MSG_794 12619
#define ER_IB_MSG_795 12620
#define ER_IB_MSG_796 12621
#define ER_IB_MSG_797 12622
#define ER_IB_MSG_798 12623
#define ER_IB_MSG_799 12624
//#define OBSOLETE_ER_IB_MSG_800 12625
#define ER_IB_MSG_801 12626
#define ER_IB_MSG_802 12627
#define ER_IB_MSG_803 12628
#define ER_IB_MSG_804 12629
#define ER_IB_MSG_805 12630
#define ER_IB_MSG_806 12631
#define ER_IB_MSG_807 12632
#define ER_IB_MSG_808 12633
#define ER_IB_MSG_809 12634
#define ER_IB_MSG_810 12635
#define ER_IB_MSG_811 12636
#define ER_IB_MSG_812 12637
#define ER_IB_MSG_813 12638
#define ER_IB_MSG_814 12639
#define ER_IB_MSG_815 12640
#define ER_IB_MSG_816 12641
#define ER_IB_MSG_817 12642
#define ER_IB_MSG_818 12643
#define ER_IB_MSG_819 12644
#define ER_IB_MSG_820 12645
#define ER_IB_MSG_821 12646
#define ER_IB_MSG_822 12647
#define ER_IB_MSG_823 12648
#define ER_IB_MSG_824 12649
#define ER_IB_MSG_825 12650
#define ER_IB_MSG_826 12651
#define ER_IB_MSG_827 12652
#define ER_IB_MSG_828 12653
#define ER_IB_MSG_829 12654
#define ER_IB_MSG_830 12655
#define ER_IB_MSG_831 12656
#define ER_IB_MSG_832 12657
#define ER_IB_MSG_833 12658
#define ER_IB_MSG_834 12659
#define ER_IB_MSG_835 12660
#define ER_IB_MSG_836 12661
#define ER_IB_MSG_837 12662
#define ER_IB_MSG_838 12663
#define ER_IB_MSG_839 12664
#define ER_IB_MSG_840 12665
#define ER_IB_MSG_841 12666
#define ER_IB_MSG_842 12667
#define ER_IB_MSG_CANT_ENCRYPT_REDO_LOG_DATA 12668
#define ER_IB_MSG_844 12669
#define ER_IB_MSG_845 12670
#define ER_IB_MSG_CANT_DECRYPT_REDO_LOG 12671
#define ER_IB_MSG_847 12672
#define ER_IB_MSG_848 12673
#define ER_IB_MSG_849 12674
#define ER_IB_MSG_850 12675
#define ER_IB_MSG_851 12676
#define ER_IB_MSG_852 12677
#define ER_IB_MSG_853 12678
#define ER_IB_MSG_854 12679
#define ER_IB_MSG_855 12680
#define ER_IB_MSG_856 12681
#define ER_IB_MSG_857 12682
#define ER_IB_MSG_858 12683
#define ER_IB_MSG_859 12684
#define ER_IB_MSG_860 12685
#define ER_IB_MSG_861 12686
#define ER_IB_MSG_862 12687
#define ER_IB_MSG_863 12688
#define ER_IB_MSG_864 12689
#define ER_IB_MSG_865 12690
#define ER_IB_MSG_866 12691
#define ER_IB_MSG_867 12692
#define ER_IB_MSG_868 12693
#define ER_IB_MSG_869 12694
#define ER_IB_MSG_870 12695
#define ER_IB_MSG_871 12696
#define ER_IB_MSG_872 12697
#define ER_IB_MSG_873 12698
#define ER_IB_MSG_874 12699
#define ER_IB_MSG_875 12700
#define ER_IB_MSG_876 12701
#define ER_IB_MSG_877 12702
#define ER_IB_MSG_878 12703
#define ER_IB_MSG_879 12704
#define ER_IB_MSG_880 12705
#define ER_IB_MSG_881 12706
#define ER_IB_MSG_882 12707
#define ER_IB_MSG_883 12708
#define ER_IB_MSG_884 12709
#define ER_IB_MSG_885 12710
#define ER_IB_MSG_886 12711
#define ER_IB_MSG_887 12712
#define ER_IB_MSG_888 12713
#define ER_IB_MSG_889 12714
#define ER_IB_MSG_890 12715
#define ER_IB_MSG_891 12716
#define ER_IB_MSG_892 12717
#define ER_IB_MSG_893 12718
#define ER_IB_MSG_894 12719
#define ER_IB_MSG_895 12720
#define ER_IB_MSG_896 12721
#define ER_IB_MSG_897 12722
#define ER_IB_MSG_898 12723
#define ER_IB_MSG_899 12724
#define ER_IB_MSG_900 12725
#define ER_IB_MSG_901 12726
#define ER_IB_MSG_902 12727
#define ER_IB_MSG_903 12728
#define ER_IB_MSG_904 12729
#define ER_IB_MSG_905 12730
#define ER_IB_MSG_906 12731
#define ER_IB_MSG_907 12732
#define ER_IB_MSG_908 12733
#define ER_IB_MSG_909 12734
#define ER_IB_MSG_910 12735
#define ER_IB_MSG_911 12736
#define ER_IB_MSG_912 12737
#define ER_IB_MSG_913 12738
#define ER_IB_MSG_914 12739
#define ER_IB_MSG_915 12740
#define ER_IB_MSG_916 12741
#define ER_IB_MSG_917 12742
#define ER_IB_MSG_918 12743
#define ER_IB_MSG_919 12744
#define ER_IB_MSG_920 12745
#define ER_IB_MSG_921 12746
#define ER_IB_MSG_922 12747
#define ER_IB_MSG_923 12748
#define ER_IB_MSG_924 12749
#define ER_IB_MSG_925 12750
#define ER_IB_MSG_926 12751
#define ER_IB_MSG_927 12752
#define ER_IB_MSG_928 12753
#define ER_IB_MSG_929 12754
#define ER_IB_MSG_930 12755
#define ER_IB_MSG_931 12756
#define ER_IB_MSG_932 12757
#define ER_IB_MSG_933 12758
#define ER_IB_MSG_934 12759
#define ER_IB_MSG_935 12760
#define ER_IB_MSG_936 12761
#define ER_IB_MSG_937 12762
#define ER_IB_MSG_938 12763
#define ER_IB_MSG_939 12764
#define ER_IB_MSG_940 12765
#define ER_IB_MSG_941 12766
#define ER_IB_MSG_942 12767
#define ER_IB_MSG_943 12768
#define ER_IB_MSG_944 12769
#define ER_IB_MSG_945 12770
#define ER_IB_MSG_946 12771
#define ER_IB_IMPORT_INDEX_METADATA_READ_FAILED 12772
#define ER_IB_MSG_948 12773
#define ER_IB_MSG_949 12774
#define ER_IB_MSG_950 12775
#define ER_IB_MSG_951 12776
#define ER_IB_MSG_952 12777
#define ER_IB_MSG_953 12778
#define ER_IB_MSG_954 12779
#define ER_IB_MSG_955 12780
#define ER_IB_MSG_956 12781
#define ER_IB_MSG_957 12782
#define ER_IB_MSG_958 12783
#define ER_IB_MSG_959 12784
#define ER_IB_MSG_960 12785
#define ER_IB_MSG_961 12786
#define ER_IB_MSG_962 12787
#define ER_IB_MSG_963 12788
#define ER_IB_MSG_964 12789
#define ER_IB_MSG_965 12790
#define ER_IB_MSG_966 12791
#define ER_IB_MSG_967 12792
#define ER_IB_MSG_968 12793
#define ER_IB_MSG_969 12794
#define ER_IB_MSG_970 12795
#define ER_IB_MSG_971 12796
#define ER_IB_MSG_972 12797
#define ER_IB_MSG_973 12798
#define ER_IB_MSG_974 12799
#define ER_IB_MSG_975 12800
#define ER_IB_MSG_976 12801
#define ER_IB_MSG_977 12802
#define ER_IB_MSG_978 12803
#define ER_IB_MSG_979 12804
#define ER_IB_MSG_980 12805
#define ER_IB_MSG_981 12806
#define ER_IB_MSG_982 12807
#define ER_IB_MSG_983 12808
#define ER_IB_MSG_984 12809
#define ER_IB_MSG_985 12810
#define ER_IB_MSG_986 12811
#define ER_IB_MSG_987 12812
#define ER_IB_MSG_988 12813
#define ER_IB_MSG_989 12814
#define ER_IB_MSG_990 12815
#define ER_IB_MSG_991 12816
#define ER_IB_MSG_992 12817
#define ER_IB_MSG_993 12818
#define ER_IB_MSG_994 12819
#define ER_IB_MSG_995 12820
#define ER_IB_MSG_996 12821
#define ER_IB_MSG_997 12822
#define ER_IB_MSG_998 12823
#define ER_IB_MSG_999 12824
#define ER_IB_MSG_1000 12825
#define ER_IB_MSG_1001 12826
#define ER_IB_MSG_1002 12827
#define ER_IB_MSG_1003 12828
#define ER_IB_MSG_1004 12829
#define ER_IB_MSG_1005 12830
#define ER_IB_MSG_1006 12831
#define ER_IB_MSG_1007 12832
#define ER_IB_MSG_1008 12833
#define ER_IB_MSG_1009 12834
#define ER_IB_MSG_1010 12835
#define ER_IB_MSG_1011 12836
#define ER_IB_MSG_1012 12837
#define ER_IB_MSG_1013 12838
#define ER_IB_IMPORT_START_CFG_NAME 12839
#define ER_IB_MSG_1015 12840
#define ER_IB_MSG_1016 12841
#define ER_IB_MSG_1017 12842
#define ER_IB_MSG_1018 12843
#define ER_IB_MSG_1019 12844
#define ER_IB_MSG_1020 12845
#define ER_IB_MSG_1021 12846
#define ER_IB_MSG_1022 12847
#define ER_IB_MSG_1023 12848
#define ER_IB_MSG_1024 12849
#define ER_IB_MSG_1025 12850
#define ER_IB_MSG_1026 12851
#define ER_IB_MSG_1027 12852
#define ER_IB_MSG_1028 12853
#define ER_IB_MSG_1029 12854
#define ER_IB_MSG_1030 12855
#define ER_IB_MSG_1031 12856
#define ER_IB_MSG_1032 12857
#define ER_IB_MSG_1033 12858
#define ER_IB_MSG_1034 12859
#define ER_IB_MSG_1035 12860
#define ER_IB_MSG_1036 12861
#define ER_IB_MSG_1037 12862
#define ER_IB_MSG_1038 12863
#define ER_IB_MSG_1039 12864
#define ER_IB_MSG_1040 12865
#define ER_IB_MSG_1041 12866
#define ER_IB_MSG_1042 12867
#define ER_IB_MSG_1043 12868
#define ER_IB_MSG_1044 12869
#define ER_IB_MSG_1045 12870
#define ER_IB_MSG_1046 12871
#define ER_IB_MSG_1047 12872
#define ER_IB_MSG_1048 12873
#define ER_IB_MSG_1049 12874
//#define OBSOLETE_ER_IB_MSG_1050 12875
#define ER_IB_MSG_1051 12876
#define ER_IB_MSG_1052 12877
#define ER_IB_MSG_1053 12878
#define ER_IB_MSG_1054 12879
#define ER_IB_MSG_1055 12880
#define ER_IB_MSG_1056 12881
#define ER_IB_MSG_1057 12882
#define ER_IB_MSG_1058 12883
#define ER_IB_MSG_1059 12884
#define ER_IB_MSG_1060 12885
#define ER_IB_MSG_LOG_FILE_OS_CREATE_FAILED 12886
#define ER_IB_MSG_FILE_RESIZE 12887
#define ER_IB_MSG_LOG_FILE_RESIZE_FAILED 12888
#define ER_IB_MSG_LOG_FILES_CREATE_AND_READ_ONLY_MODE 12889
#define ER_IB_MSG_1065 12890
#define ER_IB_MSG_LOG_FILE_PREPARE_ON_CREATE_FAILED 12891
//#define OBSOLETE_ER_IB_MSG_1067 12892
#define ER_IB_MSG_LOG_FILES_INITIALIZED 12893
#define ER_IB_MSG_LOG_FILE_OPEN_FAILED 12894
#define ER_IB_MSG_1070 12895
#define ER_IB_MSG_1071 12896
#define ER_IB_MSG_1072 12897
#define ER_IB_MSG_1073 12898
#define ER_IB_MSG_1074 12899
#define ER_IB_MSG_1075 12900
#define ER_IB_MSG_1076 12901
#define ER_IB_MSG_1077 12902
#define ER_IB_MSG_1078 12903
#define ER_IB_MSG_1079 12904
#define ER_IB_MSG_1080 12905
#define ER_IB_MSG_1081 12906
#define ER_IB_MSG_1082 12907
#define ER_IB_MSG_1083 12908
#define ER_IB_MSG_CANNOT_OPEN_57_UNDO 12909
#define ER_IB_MSG_1085 12910
#define ER_IB_MSG_1086 12911
#define ER_IB_MSG_1087 12912
#define ER_IB_MSG_1088 12913
#define ER_IB_MSG_1089 12914
#define ER_IB_MSG_1090 12915
#define ER_IB_MSG_1091 12916
#define ER_IB_MSG_1092 12917
#define ER_IB_MSG_1093 12918
#define ER_IB_MSG_1094 12919
#define ER_IB_MSG_1095 12920
#define ER_IB_MSG_1096 12921
#define ER_IB_MSG_1097 12922
#define ER_IB_MSG_1098 12923
#define ER_IB_MSG_1099 12924
#define ER_IB_MSG_1100 12925
#define ER_IB_MSG_1101 12926
#define ER_IB_MSG_1102 12927
#define ER_IB_MSG_1103 12928
#define ER_IB_MSG_1104 12929
#define ER_IB_MSG_1105 12930
#define ER_IB_MSG_BUF_PENDING_IO 12931
#define ER_IB_MSG_1107 12932
#define ER_IB_MSG_1108 12933
#define ER_IB_MSG_1109 12934
#define ER_IB_MSG_1110 12935
#define ER_IB_MSG_1111 12936
#define ER_IB_MSG_1112 12937
#define ER_IB_MSG_1113 12938
#define ER_IB_MSG_1114 12939
#define ER_IB_MSG_1115 12940
#define ER_IB_MSG_1116 12941
#define ER_IB_MSG_1117 12942
//#define OBSOLETE_ER_IB_MSG_1118 12943
#define ER_IB_MSG_1119 12944
#define ER_IB_MSG_1120 12945
#define ER_IB_MSG_1121 12946
#define ER_IB_MSG_1122 12947
#define ER_IB_MSG_1123 12948
#define ER_IB_MSG_1124 12949
#define ER_IB_MSG_1125 12950
#define ER_IB_MSG_1126 12951
#define ER_IB_MSG_1127 12952
#define ER_IB_MSG_1128 12953
#define ER_IB_MSG_1129 12954
#define ER_IB_MSG_1130 12955
#define ER_IB_MSG_1131 12956
#define ER_IB_MSG_1132 12957
#define ER_IB_MSG_1133 12958
#define ER_IB_MSG_1134 12959
#define ER_IB_MSG_DATA_DIRECTORY_NOT_INITIALIZED_OR_CORRUPTED 12960
#define ER_IB_MSG_LOG_FILES_INVALID_SET 12961
#define ER_IB_MSG_LOG_FILE_SIZE_INVALID 12962
#define ER_IB_MSG_LOG_FILES_DIFFERENT_SIZES 12963
#define ER_IB_MSG_1139 12964
#define ER_IB_MSG_RECOVERY_CORRUPT 12965
//#define OBSOLETE_ER_IB_MSG_LOG_FILES_RESIZE_ON_START_IN_READ_ONLY_MODE 12966
#define ER_IB_MSG_1142 12967
#define ER_IB_MSG_LOG_FILES_REWRITING 12968
#define ER_IB_MSG_1144 12969
#define ER_IB_MSG_1145 12970
#define ER_IB_MSG_1146 12971
#define ER_IB_MSG_1147 12972
#define ER_IB_MSG_1148 12973
#define ER_IB_MSG_1149 12974
#define ER_IB_MSG_1150 12975
#define ER_IB_MSG_1151 12976
#define ER_IB_MSG_1152 12977
//#define OBSOLETE_ER_IB_MSG_1153 12978
#define ER_IB_MSG_1154 12979
#define ER_IB_MSG_1155 12980
#define ER_IB_MSG_1156 12981
#define ER_IB_MSG_1157 12982
#define ER_IB_MSG_1158 12983
#define ER_IB_MSG_1159 12984
#define ER_IB_MSG_1160 12985
#define ER_IB_MSG_1161 12986
#define ER_IB_MSG_1162 12987
#define ER_IB_MSG_1163 12988
#define ER_IB_MSG_1164 12989
#define ER_IB_MSG_1165 12990
#define ER_IB_MSG_UNDO_TRUNCATE_FAIL_TO_READ_LOG_FILE 12991
#define ER_IB_MSG_UNDO_MARKED_FOR_TRUNCATE 12992
//#define OBSOLETE_ER_IB_MSG_UNDO_INJECT_BEFORE_MDL 12993
#define ER_IB_MSG_UNDO_TRUNCATE_START 12994
//#define OBSOLETE_ER_IB_MSG_UNDO_INJECT_BEFORE_DDL_LOG_START 12995
#define ER_IB_MSG_UNDO_TRUNCATE_DELAY_BY_LOG_CREATE 12996
//#define OBSOLETE_ER_IB_MSG_UNDO_INJECT_BEFORE_TRUNCATE 12997
#define ER_IB_MSG_UNDO_TRUNCATE_DELAY_BY_FAILURE 12998
//#define OBSOLETE_ER_IB_MSG_UNDO_INJECT_BEFORE_STATE_UPDATE 12999
#define ER_IB_MSG_UNDO_TRUNCATE_COMPLETE 13000
//#define OBSOLETE_ER_IB_MSG_UNDO_INJECT_TRUNCATE_DONE 13001
#define ER_IB_MSG_1177 13002
#define ER_IB_MSG_1178 13003
#define ER_IB_MSG_1179 13004
#define ER_IB_MSG_1180 13005
#define ER_IB_MSG_1181 13006
#define ER_IB_MSG_1182 13007
#define ER_IB_MSG_1183 13008
#define ER_IB_ERR_ACCESSING_OUT_OF_BOUND_FIELD_IN_INDEX 13009
#define ER_IB_MSG_1185 13010
#define ER_IB_MSG_1186 13011
#define ER_IB_MSG_1187 13012
#define ER_IB_MSG_1188 13013
#define ER_IB_MSG_1189 13014
#define ER_IB_MSG_TRX_RECOVERY_ROLLBACK_COMPLETED 13015
#define ER_IB_MSG_1191 13016
#define ER_IB_MSG_1192 13017
#define ER_IB_MSG_1193 13018
#define ER_IB_MSG_1194 13019
#define ER_IB_MSG_1195 13020
#define ER_IB_MSG_1196 13021
#define ER_IB_MSG_1197 13022
#define ER_IB_MSG_1198 13023
#define ER_IB_MSG_1199 13024
#define ER_IB_MSG_1200 13025
#define ER_IB_MSG_1201 13026
#define ER_IB_MSG_1202 13027
#define ER_IB_MSG_1203 13028
#define ER_IB_MSG_1204 13029
#define ER_IB_MSG_1205 13030
#define ER_IB_MSG_1206 13031
#define ER_IB_MSG_1207 13032
#define ER_IB_MSG_1208 13033
#define ER_IB_MSG_1209 13034
#define ER_IB_MSG_1210 13035
#define ER_IB_MSG_1211 13036
#define ER_IB_MSG_1212 13037
#define ER_IB_MSG_1213 13038
#define ER_IB_MSG_1214 13039
#define ER_IB_MSG_1215 13040
#define ER_IB_MSG_LOG_FILES_RESIZE_ON_START 13041
#define ER_IB_MSG_1217 13042
#define ER_IB_MSG_1218 13043
#define ER_IB_MSG_1219 13044
#define ER_IB_MSG_1220 13045
#define ER_IB_MSG_1221 13046
#define ER_IB_MSG_1222 13047
#define ER_IB_MSG_1223 13048
#define ER_IB_MSG_1224 13049
#define ER_IB_MSG_1225 13050
#define ER_IB_MSG_1226 13051
#define ER_IB_MSG_1227 13052
#define ER_IB_MSG_1228 13053
#define ER_IB_MSG_1229 13054
//#define OBSOLETE_ER_IB_MSG_1230 13055
#define ER_IB_MSG_1231 13056
//#define OBSOLETE_ER_IB_MSG_1232 13057
#define ER_IB_MSG_1233 13058
#define ER_IB_MSG_LOG_WRITER_OUT_OF_SPACE 13059
#define ER_IB_MSG_1235 13060
#define ER_IB_MSG_LOG_WRITER_ABORTS_LOG_ARCHIVER 13061
#define ER_IB_MSG_LOG_WRITER_WAITING_FOR_ARCHIVER 13062
#define ER_IB_MSG_1238 13063
#define ER_IB_MSG_1239 13064
//#define OBSOLETE_ER_IB_MSG_1240 13065
#define ER_IB_MSG_1241 13066
#define ER_IB_MSG_LOG_FILES_CANNOT_ENCRYPT_IN_READ_ONLY 13067
#define ER_IB_MSG_LOG_FILES_ENCRYPTION_INIT_FAILED 13068
//#define OBSOLETE_ER_IB_MSG_1244 13069
#define ER_IB_MSG_1245 13070
#define ER_IB_MSG_1246 13071
#define ER_IB_MSG_1247 13072
#define ER_IB_MSG_1248 13073
#define ER_IB_MSG_1249 13074
#define ER_IB_MSG_1250 13075
#define ER_IB_MSG_1251 13076
#define ER_IB_MSG_BUF_PENDING_IO_ON_SHUTDOWN 13077
#define ER_IB_MSG_1253 13078
//#define OBSOLETE_ER_IB_MSG_1254 13079
#define ER_IB_MSG_1255 13080
#define ER_IB_MSG_1256 13081
#define ER_IB_MSG_1257 13082
#define ER_IB_MSG_1258 13083
#define ER_IB_MSG_1259 13084
#define ER_IB_MSG_1260 13085
#define ER_IB_MSG_1261 13086
#define ER_IB_MSG_1262 13087
#define ER_IB_MSG_1263 13088
#define ER_IB_MSG_LOG_FILE_HEADER_INVALID_CHECKSUM 13089
#define ER_IB_MSG_LOG_FORMAT_BEFORE_5_7_9 13090
#define ER_IB_MSG_1266 13091
#define ER_IB_MSG_LOG_PARAMS_CONCURRENCY_MARGIN_UNSAFE 13092
#define ER_IB_MSG_1268 13093
#define ER_IB_MSG_1269 13094
#define ER_IB_MSG_THREAD_CONCURRENCY_CHANGED 13095
#define ER_RPL_REPLICA_SQL_THREAD_STOP_CMD_EXEC_TIMEOUT 13096
#define ER_RPL_REPLICA_IO_THREAD_STOP_CMD_EXEC_TIMEOUT 13097
#define ER_RPL_GTID_UNSAFE_STMT_ON_NON_TRANS_TABLE 13098
#define ER_RPL_GTID_UNSAFE_STMT_CREATE_SELECT 13099
//#define OBSOLETE_ER_RPL_GTID_UNSAFE_STMT_ON_TEMPORARY_TABLE 13100
#define ER_BINLOG_ROW_VALUE_OPTION_IGNORED 13101
//#define OBSOLETE_ER_BINLOG_USE_V1_ROW_EVENTS_IGNORED 13102
#define ER_BINLOG_ROW_VALUE_OPTION_USED_ONLY_FOR_AFTER_IMAGES 13103
#define ER_CONNECTION_ABORTED 13104
#define ER_NORMAL_SERVER_SHUTDOWN 13105
#define ER_KEYRING_MIGRATE_FAILED 13106
#define ER_GRP_RPL_LOWER_CASE_TABLE_NAMES_DIFF_FROM_GRP 13107
#define ER_OOM_SAVE_GTIDS 13108
#define ER_LCTN_NOT_FOUND 13109
//#define OBSOLETE_ER_REGEXP_INVALID_CAPTURE_GROUP_NAME 13110
#define ER_COMPONENT_FILTER_WRONG_VALUE 13111
#define ER_XPLUGIN_FAILED_TO_STOP_SERVICES 13112
#define ER_INCONSISTENT_ERROR 13113
#define ER_SERVER_SOURCE_FATAL_ERROR_READING_BINLOG 13114
#define ER_NETWORK_READ_EVENT_CHECKSUM_FAILURE 13115
#define ER_REPLICA_CREATE_EVENT_FAILURE 13116
#define ER_REPLICA_FATAL_ERROR 13117
#define ER_REPLICA_HEARTBEAT_FAILURE 13118
#define ER_REPLICA_INCIDENT 13119
#define ER_REPLICA_SOURCE_COM_FAILURE 13120
#define ER_REPLICA_RELAY_LOG_READ_FAILURE 13121
#define ER_REPLICA_RELAY_LOG_WRITE_FAILURE 13122
#define ER_SERVER_REPLICA_CM_INIT_REPOSITORY 13123
#define ER_SERVER_REPLICA_AM_INIT_REPOSITORY 13124
#define ER_SERVER_NET_PACKET_TOO_LARGE 13125
#define ER_SERVER_NO_SYSTEM_TABLE_ACCESS 13126
//#define OBSOLETE_ER_SERVER_UNKNOWN_ERROR 13127
#define ER_SERVER_UNKNOWN_SYSTEM_VARIABLE 13128
#define ER_SERVER_NO_SESSION_TO_SEND_TO 13129
#define ER_SERVER_NEW_ABORTING_CONNECTION 13130
#define ER_SERVER_OUT_OF_SORTMEMORY 13131
#define ER_SERVER_RECORD_FILE_FULL 13132
#define ER_SERVER_DISK_FULL_NOWAIT 13133
#define ER_SERVER_HANDLER_ERROR 13134
#define ER_SERVER_NOT_FORM_FILE 13135
#define ER_SERVER_CANT_OPEN_FILE 13136
#define ER_SERVER_FILE_NOT_FOUND 13137
#define ER_SERVER_FILE_USED 13138
#define ER_SERVER_CANNOT_LOAD_FROM_TABLE_V2 13139
#define ER_ERROR_INFO_FROM_DA 13140
#define ER_SERVER_TABLE_CHECK_FAILED 13141
#define ER_SERVER_COL_COUNT_DOESNT_MATCH_PLEASE_UPDATE_V2 13142
#define ER_SERVER_COL_COUNT_DOESNT_MATCH_CORRUPTED_V2 13143
#define ER_SERVER_ACL_TABLE_ERROR 13144
#define ER_SERVER_REPLICA_INIT_QUERY_FAILED 13145
#define ER_SERVER_REPLICA_CONVERSION_FAILED 13146
#define ER_SERVER_REPLICA_IGNORED_TABLE 13147
#define ER_CANT_REPLICATE_ANONYMOUS_WITH_AUTO_POSITION 13148
#define ER_CANT_REPLICATE_ANONYMOUS_WITH_GTID_MODE_ON 13149
#define ER_CANT_REPLICATE_GTID_WITH_GTID_MODE_OFF 13150
#define ER_SERVER_TEST_MESSAGE 13151
#define ER_AUDIT_LOG_JSON_FILTER_PARSING_ERROR 13152
#define ER_AUDIT_LOG_JSON_FILTERING_NOT_ENABLED 13153
#define ER_PLUGIN_FAILED_TO_OPEN_TABLES 13154
#define ER_PLUGIN_FAILED_TO_OPEN_TABLE 13155
#define ER_AUDIT_LOG_JSON_FILTER_NAME_CANNOT_BE_EMPTY 13156
#define ER_AUDIT_LOG_USER_NAME_INVALID_CHARACTER 13157
#define ER_AUDIT_LOG_UDF_INSUFFICIENT_PRIVILEGE 13158
#define ER_AUDIT_LOG_NO_KEYRING_PLUGIN_INSTALLED 13159
#define ER_AUDIT_LOG_HOST_NAME_INVALID_CHARACTER 13160
#define ER_AUDIT_LOG_ENCRYPTION_PASSWORD_HAS_NOT_BEEN_SET 13161
#define ER_AUDIT_LOG_COULD_NOT_CREATE_AES_KEY 13162
#define ER_AUDIT_LOG_ENCRYPTION_PASSWORD_CANNOT_BE_FETCHED 13163
#define ER_COULD_NOT_REINITIALIZE_AUDIT_LOG_FILTERS 13164
#define ER_AUDIT_LOG_JSON_USER_NAME_CANNOT_BE_EMPTY 13165
#define ER_AUDIT_LOG_USER_FIRST_CHARACTER_MUST_BE_ALPHANUMERIC 13166
#define ER_AUDIT_LOG_JSON_FILTER_DOES_NOT_EXIST 13167
//#define OBSOLETE_ER_IB_MSG_1271 13168
#define ER_STARTING_INIT 13169
#define ER_ENDING_INIT 13170
#define ER_IB_MSG_1272 13171
#define ER_SERVER_SHUTDOWN_INFO 13172
#define ER_GRP_RPL_PLUGIN_ABORT 13173
//#define OBSOLETE_ER_REGEXP_INVALID_FLAG 13174
//#define OBSOLETE_ER_XA_REPLICATION_FILTERS 13175
//#define OBSOLETE_ER_UPDATE_GTID_PURGED_WITH_GR 13176
#define ER_AUDIT_LOG_TABLE_DEFINITION_NOT_UPDATED 13177
#define ER_DD_INITIALIZE_SQL_ERROR 13178
#define ER_NO_PATH_FOR_SHARED_LIBRARY 13179
#define ER_UDF_ALREADY_EXISTS 13180
#define ER_SET_EVENT_FAILED 13181
#define ER_FAILED_TO_ALLOCATE_SSL_BIO 13182
#define ER_IB_MSG_1273 13183
#define ER_PID_FILEPATH_LOCATIONS_INACCESSIBLE 13184
#define ER_UNKNOWN_VARIABLE_IN_PERSISTED_CONFIG_FILE 13185
#define ER_FAILED_TO_HANDLE_DEFAULTS_FILE 13186
#define ER_DUPLICATE_SYS_VAR 13187
#define ER_FAILED_TO_INIT_SYS_VAR 13188
#define ER_SYS_VAR_NOT_FOUND 13189
#define ER_IB_MSG_1274 13190
#define ER_IB_MSG_1275 13191
//#define OBSOLETE_ER_TARGET_TS_UNENCRYPTED 13192
#define ER_IB_MSG_WAIT_FOR_ENCRYPT_THREAD 13193
#define ER_IB_MSG_1277 13194
#define ER_IB_MSG_NO_ENCRYPT_PROGRESS_FOUND 13195
#define ER_IB_MSG_RESUME_OP_FOR_SPACE 13196
#define ER_IB_MSG_1280 13197
#define ER_IB_MSG_1281 13198
#define ER_IB_MSG_1282 13199
#define ER_IB_MSG_1283 13200
#define ER_IB_MSG_1284 13201
#define ER_CANT_SET_ERROR_SUPPRESSION_LIST_FROM_COMMAND_LINE 13202
#define ER_INVALID_VALUE_OF_BIND_ADDRESSES 13203
#define ER_RELAY_LOG_SPACE_LIMIT_DISABLED 13204
#define ER_GRP_RPL_ERROR_GTID_SET_EXTRACTION 13205
#define ER_GRP_RPL_MISSING_GRP_RPL_ACTION_COORDINATOR 13206
#define ER_GRP_RPL_JOIN_WHEN_GROUP_ACTION_RUNNING 13207
#define ER_GRP_RPL_JOINER_EXIT_WHEN_GROUP_ACTION_RUNNING 13208
#define ER_GRP_RPL_CHANNEL_THREAD_WHEN_GROUP_ACTION_RUNNING 13209
#define ER_GRP_RPL_APPOINTED_PRIMARY_NOT_PRESENT 13210
#define ER_GRP_RPL_ERROR_ON_MESSAGE_SENDING 13211
#define ER_GRP_RPL_CONFIGURATION_ACTION_ERROR 13212
#define ER_GRP_RPL_CONFIGURATION_ACTION_LOCAL_TERMINATION 13213
#define ER_GRP_RPL_CONFIGURATION_ACTION_START 13214
#define ER_GRP_RPL_CONFIGURATION_ACTION_END 13215
#define ER_GRP_RPL_CONFIGURATION_ACTION_KILLED_ERROR 13216
#define ER_GRP_RPL_PRIMARY_ELECTION_PROCESS_ERROR 13217
#define ER_GRP_RPL_PRIMARY_ELECTION_STOP_ERROR 13218
#define ER_GRP_RPL_NO_STAGE_SERVICE 13219
#define ER_GRP_RPL_UDF_REGISTER_ERROR 13220
#define ER_GRP_RPL_UDF_UNREGISTER_ERROR 13221
#define ER_GRP_RPL_UDF_REGISTER_SERVICE_ERROR 13222
#define ER_GRP_RPL_SERVER_UDF_ERROR 13223
//#define OBSOLETE_ER_CURRENT_PASSWORD_NOT_REQUIRED 13224
//#define OBSOLETE_ER_INCORRECT_CURRENT_PASSWORD 13225
//#define OBSOLETE_ER_MISSING_CURRENT_PASSWORD 13226
#define ER_SERVER_WRONG_VALUE_FOR_VAR 13227
#define ER_COULD_NOT_CREATE_WINDOWS_REGISTRY_KEY 13228
#define ER_SERVER_GTID_UNSAFE_CREATE_DROP_TEMP_TABLE_IN_TRX_IN_SBR 13229
//#define OBSOLETE_ER_SECONDARY_ENGINE 13230
//#define OBSOLETE_ER_SECONDARY_ENGINE_DDL 13231
//#define OBSOLETE_ER_NO_SESSION_TEMP 13232
#define ER_XPLUGIN_FAILED_TO_SWITCH_SECURITY_CTX 13233
#define ER_RPL_GTID_UNSAFE_ALTER_ADD_COL_WITH_DEFAULT_EXPRESSION 13234
#define ER_UPGRADE_PARSE_ERROR 13235
#define ER_DATA_DIRECTORY_UNUSABLE 13236
#define ER_LDAP_AUTH_USER_GROUP_SEARCH_ROOT_BIND 13237
#define ER_PLUGIN_INSTALL_ERROR 13238
#define ER_PLUGIN_UNINSTALL_ERROR 13239
#define ER_SHARED_TABLESPACE_USED_BY_PARTITIONED_TABLE 13240
#define ER_UNKNOWN_TABLESPACE_TYPE 13241
#define ER_WARN_DEPRECATED_UTF8_ALIAS_OPTION 13242
#define ER_WARN_DEPRECATED_UTF8MB3_CHARSET_OPTION 13243
#define ER_WARN_DEPRECATED_UTF8MB3_COLLATION_OPTION 13244
#define ER_SSL_MEMORY_INSTRUMENTATION_INIT_FAILED 13245
#define ER_IB_MSG_MADV_DONTDUMP_UNSUPPORTED 13246
#define ER_IB_MSG_MADVISE_FAILED 13247
//#define OBSOLETE_ER_COLUMN_CHANGE_SIZE 13248
#define ER_WARN_REMOVED_SQL_MODE 13249
#define ER_IB_MSG_FAILED_TO_ALLOCATE_WAIT 13250
//#define OBSOLETE_ER_IB_MSG_NUM_POOLS 13251
#define ER_IB_MSG_USING_UNDO_SPACE 13252
#define ER_IB_MSG_FAIL_TO_SAVE_SPACE_STATE 13253
#define ER_IB_MSG_MAX_UNDO_SPACES_REACHED 13254
#define ER_IB_MSG_ERROR_OPENING_NEW_UNDO_SPACE 13255
#define ER_IB_MSG_FAILED_SDI_Z_BUF_ERROR 13256
#define ER_IB_MSG_FAILED_SDI_Z_MEM_ERROR 13257
#define ER_IB_MSG_SDI_Z_STREAM_ERROR 13258
#define ER_IB_MSG_SDI_Z_UNKNOWN_ERROR 13259
#define ER_IB_MSG_FOUND_WRONG_UNDO_SPACE 13260
#define ER_IB_MSG_NOT_END_WITH_IBU 13261
//#define OBSOLETE_ER_IB_MSG_UNDO_TRUNCATE_EMPTY_FILE 13262
//#define OBSOLETE_ER_IB_MSG_UNDO_INJECT_BEFORE_DD_UPDATE 13263
//#define OBSOLETE_ER_IB_MSG_UNDO_INJECT_BEFORE_UNDO_LOGGING 13264
//#define OBSOLETE_ER_IB_MSG_UNDO_INJECT_BEFORE_RSEG 13265
#define ER_IB_MSG_FAILED_TO_FINISH_TRUNCATE 13266
#define ER_IB_MSG_DEPRECATED_INNODB_UNDO_TABLESPACES 13267
#define ER_IB_MSG_WRONG_TABLESPACE_DIR 13268
#define ER_IB_MSG_LOCK_FREE_HASH_USAGE_STATS 13269
#define ER_CLONE_DONOR_TRACE 13270
#define ER_CLONE_PROTOCOL_TRACE 13271
#define ER_CLONE_CLIENT_TRACE 13272
#define ER_CLONE_SERVER_TRACE 13273
#define ER_THREAD_POOL_PFS_TABLES_INIT_FAILED 13274
#define ER_THREAD_POOL_PFS_TABLES_ADD_FAILED 13275
#define ER_CANT_SET_DATA_DIR 13276
#define ER_INNODB_INVALID_INNODB_UNDO_DIRECTORY_LOCATION 13277
#define ER_SERVER_RPL_ENCRYPTION_FAILED_TO_FETCH_KEY 13278
#define ER_SERVER_RPL_ENCRYPTION_KEY_NOT_FOUND 13279
#define ER_SERVER_RPL_ENCRYPTION_KEYRING_INVALID_KEY 13280
#define ER_SERVER_RPL_ENCRYPTION_HEADER_ERROR 13281
#define ER_SERVER_RPL_ENCRYPTION_FAILED_TO_ROTATE_LOGS 13282
#define ER_SERVER_RPL_ENCRYPTION_KEY_EXISTS_UNEXPECTED 13283
#define ER_SERVER_RPL_ENCRYPTION_FAILED_TO_GENERATE_KEY 13284
#define ER_SERVER_RPL_ENCRYPTION_FAILED_TO_STORE_KEY 13285
#define ER_SERVER_RPL_ENCRYPTION_FAILED_TO_REMOVE_KEY 13286
#define ER_SERVER_RPL_ENCRYPTION_MASTER_KEY_RECOVERY_FAILED 13287
#define ER_SERVER_RPL_ENCRYPTION_UNABLE_TO_INITIALIZE 13288
#define ER_SERVER_RPL_ENCRYPTION_UNABLE_TO_ROTATE_MASTER_KEY_AT_STARTUP 13289
#define ER_SERVER_RPL_ENCRYPTION_IGNORE_ROTATE_MASTER_KEY_AT_STARTUP 13290
#define ER_INVALID_ADMIN_ADDRESS 13291
#define ER_SERVER_STARTUP_ADMIN_INTERFACE 13292
#define ER_CANT_CREATE_ADMIN_THREAD 13293
#define ER_WARNING_RETAIN_CURRENT_PASSWORD_CLAUSE_VOID 13294
#define ER_WARNING_DISCARD_OLD_PASSWORD_CLAUSE_VOID 13295
//#define OBSOLETE_ER_SECOND_PASSWORD_CANNOT_BE_EMPTY 13296
//#define OBSOLETE_ER_PASSWORD_CANNOT_BE_RETAINED_ON_PLUGIN_CHANGE 13297
//#define OBSOLETE_ER_CURRENT_PASSWORD_CANNOT_BE_RETAINED 13298
#define ER_WARNING_AUTHCACHE_INVALID_USER_ATTRIBUTES 13299
#define ER_MYSQL_NATIVE_PASSWORD_SECOND_PASSWORD_USED_INFORMATION 13300
#define ER_SHA256_PASSWORD_SECOND_PASSWORD_USED_INFORMATION 13301
#define ER_CACHING_SHA2_PASSWORD_SECOND_PASSWORD_USED_INFORMATION 13302
#define ER_GRP_RPL_SEND_TRX_PREPARED_MESSAGE_FAILED 13303
#define ER_GRP_RPL_RELEASE_COMMIT_AFTER_GROUP_PREPARE_FAILED 13304
#define ER_GRP_RPL_TRX_ALREADY_EXISTS_ON_TCM_ON_AFTER_CERTIFICATION 13305
#define ER_GRP_RPL_FAILED_TO_INSERT_TRX_ON_TCM_ON_AFTER_CERTIFICATION 13306
#define ER_GRP_RPL_REGISTER_TRX_TO_WAIT_FOR_GROUP_PREPARE_FAILED 13307
#define ER_GRP_RPL_TRX_WAIT_FOR_GROUP_PREPARE_FAILED 13308
#define ER_GRP_RPL_TRX_DOES_NOT_EXIST_ON_TCM_ON_HANDLE_REMOTE_PREPARE 13309
#define ER_GRP_RPL_RELEASE_BEGIN_TRX_AFTER_DEPENDENCIES_COMMIT_FAILED 13310
#define ER_GRP_RPL_REGISTER_TRX_TO_WAIT_FOR_DEPENDENCIES_FAILED 13311
#define ER_GRP_RPL_WAIT_FOR_DEPENDENCIES_FAILED 13312
#define ER_GRP_RPL_REGISTER_TRX_TO_WAIT_FOR_SYNC_BEFORE_EXECUTION_FAILED 13313
#define ER_GRP_RPL_SEND_TRX_SYNC_BEFORE_EXECUTION_FAILED 13314
#define ER_GRP_RPL_TRX_WAIT_FOR_SYNC_BEFORE_EXECUTION_FAILED 13315
#define ER_GRP_RPL_RELEASE_BEGIN_TRX_AFTER_WAIT_FOR_SYNC_BEFORE_EXEC 13316
#define ER_GRP_RPL_TRX_WAIT_FOR_GROUP_GTID_EXECUTED 13317
//#define OBSOLETE_ER_UNIT_NOT_FOUND 13318
//#define OBSOLETE_ER_GEOMETRY_IN_UNKNOWN_LENGTH_UNIT 13319
#define ER_WARN_PROPERTY_STRING_PARSE_FAILED 13320
#define ER_INVALID_PROPERTY_KEY 13321
#define ER_GRP_RPL_GTID_SET_EXTRACT_ERROR_DURING_RECOVERY 13322
#define ER_SERVER_RPL_ENCRYPTION_FAILED_TO_ENCRYPT 13323
#define ER_CANNOT_GET_SERVER_VERSION_FROM_TABLESPACE_HEADER 13324
#define ER_CANNOT_SET_SERVER_VERSION_IN_TABLESPACE_HEADER 13325
#define ER_SERVER_UPGRADE_VERSION_NOT_SUPPORTED 13326
#define ER_SERVER_UPGRADE_FROM_VERSION 13327
#define ER_GRP_RPL_ERROR_ON_CERT_DB_INSTALL 13328
#define ER_GRP_RPL_FORCE_MEMBERS_WHEN_LEAVING 13329
#define ER_TRG_WRONG_ORDER 13330
//#define OBSOLETE_ER_SECONDARY_ENGINE_PLUGIN 13331
#define ER_LDAP_AUTH_GRP_SEARCH_NOT_SPECIAL_HDL 13332
#define ER_LDAP_AUTH_GRP_USER_OBJECT_HAS_GROUP_INFO 13333
#define ER_LDAP_AUTH_GRP_INFO_FOUND_IN_MANY_OBJECTS 13334
#define ER_LDAP_AUTH_GRP_INCORRECT_ATTRIBUTE 13335
#define ER_LDAP_AUTH_GRP_NULL_ATTRIBUTE_VALUE 13336
#define ER_LDAP_AUTH_GRP_DN_PARSING_FAILED 13337
#define ER_LDAP_AUTH_GRP_OBJECT_HAS_USER_INFO 13338
#define ER_LDAP_AUTH_LDAPS 13339
#define ER_LDAP_MAPPING_GET_USER_PROXY 13340
#define ER_LDAP_MAPPING_USER_DONT_BELONG_GROUP 13341
#define ER_LDAP_MAPPING_INFO 13342
#define ER_LDAP_MAPPING_EMPTY_MAPPING 13343
#define ER_LDAP_MAPPING_PROCESS_MAPPING 13344
#define ER_LDAP_MAPPING_CHECK_DELIMI_QUOTE 13345
#define ER_LDAP_MAPPING_PROCESS_DELIMITER 13346
#define ER_LDAP_MAPPING_PROCESS_DELIMITER_EQUAL_NOT_FOUND 13347
#define ER_LDAP_MAPPING_PROCESS_DELIMITER_TRY_COMMA 13348
#define ER_LDAP_MAPPING_PROCESS_DELIMITER_COMMA_NOT_FOUND 13349
#define ER_LDAP_MAPPING_NO_SEPEARATOR_END_OF_GROUP 13350
#define ER_LDAP_MAPPING_GETTING_NEXT_MAPPING 13351
#define ER_LDAP_MAPPING_PARSING_CURRENT_STATE 13352
#define ER_LDAP_MAPPING_PARSING_MAPPING_INFO 13353
#define ER_LDAP_MAPPING_PARSING_ERROR 13354
#define ER_LDAP_MAPPING_TRIMMING_SPACES 13355
#define ER_LDAP_MAPPING_IS_QUOTE 13356
#define ER_LDAP_MAPPING_NON_DESIRED_STATE 13357
#define ER_INVALID_NAMED_PIPE_FULL_ACCESS_GROUP 13358
#define ER_PREPARE_FOR_SECONDARY_ENGINE 13359
#define ER_SERVER_WARN_DEPRECATED 13360
#define ER_AUTH_ID_WITH_SYSTEM_USER_PRIV_IN_MANDATORY_ROLES 13361
#define ER_SERVER_BINLOG_MASTER_KEY_RECOVERY_OUT_OF_COMBINATION 13362
#define ER_SERVER_BINLOG_MASTER_KEY_ROTATION_FAIL_TO_CLEANUP_AUX_KEY 13363
//#define OBSOLETE_ER_CANNOT_GRANT_SYSTEM_PRIV_TO_MANDATORY_ROLE 13364
//#define OBSOLETE_ER_PARTIAL_REVOKE_AND_DB_GRANT_BOTH_EXISTS 13365
//#define OBSOLETE_ER_DB_ACCESS_DENIED 13366
//#define OBSOLETE_ER_PARTIAL_REVOKES_EXIST 13367
#define ER_TURNING_ON_PARTIAL_REVOKES 13368
#define ER_WARN_PARTIAL_REVOKE_AND_DB_GRANT 13369
#define ER_WARN_INCORRECT_PRIVILEGE_FOR_DB_RESTRICTIONS 13370
#define ER_WARN_INVALID_DB_RESTRICTIONS 13371
#define ER_GRP_RPL_INVALID_COMMUNICATION_PROTOCOL 13372
#define ER_GRP_RPL_STARTED_AUTO_REJOIN 13373
#define ER_GRP_RPL_TIMEOUT_RECEIVED_VC_ON_REJOIN 13374
#define ER_GRP_RPL_FINISHED_AUTO_REJOIN 13375
#define ER_GRP_RPL_DEFAULT_TABLE_ENCRYPTION_DIFF_FROM_GRP 13376
#define ER_SERVER_UPGRADE_OFF 13377
#define ER_SERVER_UPGRADE_SKIP 13378
#define ER_SERVER_UPGRADE_PENDING 13379
#define ER_SERVER_UPGRADE_FAILED 13380
#define ER_SERVER_UPGRADE_STATUS 13381
#define ER_SERVER_UPGRADE_REPAIR_REQUIRED 13382
#define ER_SERVER_UPGRADE_REPAIR_STATUS 13383
#define ER_SERVER_UPGRADE_INFO_FILE 13384
#define ER_SERVER_UPGRADE_SYS_SCHEMA 13385
#define ER_SERVER_UPGRADE_MYSQL_TABLES 13386
#define ER_SERVER_UPGRADE_SYSTEM_TABLES 13387
#define ER_SERVER_UPGRADE_EMPTY_SYS 13388
#define ER_SERVER_UPGRADE_NO_SYS_VERSION 13389
#define ER_SERVER_UPGRADE_SYS_VERSION_EMPTY 13390
#define ER_SERVER_UPGRADE_SYS_SCHEMA_OUTDATED 13391
#define ER_SERVER_UPGRADE_SYS_SCHEMA_UP_TO_DATE 13392
#define ER_SERVER_UPGRADE_SYS_SCHEMA_OBJECT_COUNT 13393
#define ER_SERVER_UPGRADE_CHECKING_DB 13394
#define ER_IB_MSG_DDL_LOG_DELETE_BY_ID_TMCT 13395
#define ER_IB_MSG_POST_RECOVER_DDL_LOG_RECOVER 13396
#define ER_IB_MSG_POST_RECOVER_POST_TS_ENCRYPT 13397
#define ER_IB_MSG_DDL_LOG_FAIL_POST_DDL 13398
#define ER_SERVER_BINLOG_UNSAFE_SYSTEM_FUNCTION 13399
#define ER_SERVER_UPGRADE_HELP_TABLE_STATUS 13400
//#define OBSOLETE_ER_GRP_RPL_SRV_GTID_WAIT_ERROR 13401
//#define OBSOLETE_ER_GRP_DELAYED_VCLE_LOGGING 13402
//#define OBSOLETE_ER_CANNOT_GRANT_ROLES_TO_ANONYMOUS_USER 13403
#define ER_BINLOG_UNABLE_TO_ROTATE_GTID_TABLE_READONLY 13404
#define ER_NETWORK_NAMESPACES_NOT_SUPPORTED 13405
#define ER_UNKNOWN_NETWORK_NAMESPACE 13406
#define ER_NETWORK_NAMESPACE_NOT_ALLOWED_FOR_WILDCARD_ADDRESS 13407
#define ER_SETNS_FAILED 13408
#define ER_WILDCARD_NOT_ALLOWED_FOR_MULTIADDRESS_BIND 13409
#define ER_NETWORK_NAMESPACE_FILE_PATH_TOO_LONG 13410
#define ER_IB_MSG_TOO_LONG_PATH 13411
#define ER_IB_RECV_FIRST_REC_GROUP_INVALID 13412
#define ER_DD_UPGRADE_COMPLETED 13413
//#define OBSOLETE_ER_SSL_SERVER_CERT_VERIFY_FAILED 13414
#define ER_PERSIST_OPTION_USER_TRUNCATED 13415
#define ER_PERSIST_OPTION_HOST_TRUNCATED 13416
#define ER_NET_WAIT_ERROR 13417
#define ER_IB_MSG_1285 13418
#define ER_IB_MSG_CLOCK_MONOTONIC_UNSUPPORTED 13419
#define ER_IB_MSG_CLOCK_GETTIME_FAILED 13420
#define ER_PLUGIN_NOT_EARLY_DUP 13421
#define ER_PLUGIN_NO_INSTALL_DUP 13422
//#define OBSOLETE_ER_WARN_DEPRECATED_SQL_CALC_FOUND_ROWS 13423
//#define OBSOLETE_ER_WARN_DEPRECATED_FOUND_ROWS 13424
#define ER_BINLOG_UNSAFE_DEFAULT_EXPRESSION_IN_SUBSTATEMENT 13425
#define ER_GRP_RPL_MEMBER_VER_READ_COMPATIBLE 13426
#define ER_LOCK_ORDER_INIT_FAILED 13427
#define ER_AUDIT_LOG_KEYRING_ID_TIMESTAMP_VALUE_IS_INVALID 13428
#define ER_AUDIT_LOG_FILE_NAME_TIMESTAMP_VALUE_IS_MISSING_OR_INVALID 13429
#define ER_AUDIT_LOG_FILE_NAME_DOES_NOT_HAVE_REQUIRED_FORMAT 13430
#define ER_AUDIT_LOG_FILE_NAME_KEYRING_ID_VALUE_IS_MISSING 13431
#define ER_AUDIT_LOG_FILE_HAS_BEEN_SUCCESSFULLY_PROCESSED 13432
#define ER_AUDIT_LOG_COULD_NOT_OPEN_FILE_FOR_READING 13433
#define ER_AUDIT_LOG_INVALID_FILE_CONTENT 13434
#define ER_AUDIT_LOG_CANNOT_READ_PASSWORD 13435
#define ER_AUDIT_LOG_CANNOT_STORE_PASSWORD 13436
#define ER_AUDIT_LOG_CANNOT_REMOVE_PASSWORD 13437
#define ER_AUDIT_LOG_PASSWORD_HAS_BEEN_COPIED 13438
//#define OBSOLETE_ER_AUDIT_LOG_INSUFFICIENT_PRIVILEGE 13439
//#define OBSOLETE_ER_WRONG_MVI_VALUE 13440
//#define OBSOLETE_ER_WARN_FUNC_INDEX_NOT_APPLICABLE 13441
//#define OBSOLETE_ER_EXCEEDED_MV_KEYS_NUM 13442
//#define OBSOLETE_ER_EXCEEDED_MV_KEYS_SPACE 13443
//#define OBSOLETE_ER_FUNCTIONAL_INDEX_DATA_IS_TOO_LONG 13444
//#define OBSOLETE_ER_INVALID_JSON_VALUE_FOR_FUNC_INDEX 13445
//#define OBSOLETE_ER_JSON_VALUE_OUT_OF_RANGE_FOR_FUNC_INDEX 13446
#define ER_LDAP_EMPTY_USERDN_PASSWORD 13447
//#define OBSOLETE_ER_GROUPING_ON_TIMESTAMP_IN_DST 13448
#define ER_ACL_WRONG_OR_MISSING_ACL_TABLES_LOG 13449
#define ER_LOCK_ORDER_FAILED_WRITE_FILE 13450
#define ER_LOCK_ORDER_FAILED_READ_FILE 13451
#define ER_LOCK_ORDER_MESSAGE 13452
#define ER_LOCK_ORDER_DEPENDENCIES_SYNTAX 13453
#define ER_LOCK_ORDER_SCANNER_SYNTAX 13454
#define ER_DATA_DIRECTORY_UNUSABLE_DELETABLE 13455
#define ER_IB_MSG_BTREE_LEVEL_LIMIT_EXCEEDED 13456
#define ER_IB_CLONE_START_STOP 13457
#define ER_IB_CLONE_OPERATION 13458
#define ER_IB_CLONE_RESTART 13459
#define ER_IB_CLONE_USER_DATA 13460
#define ER_IB_CLONE_NON_INNODB_TABLE 13461
#define ER_CLONE_SHUTDOWN_TRACE 13462
#define ER_GRP_RPL_GTID_PURGED_EXTRACT_ERROR 13463
#define ER_GRP_RPL_CLONE_PROCESS_PREPARE_ERROR 13464
#define ER_GRP_RPL_CLONE_PROCESS_EXEC_ERROR 13465
#define ER_GRP_RPL_RECOVERY_EVAL_ERROR 13466
#define ER_GRP_RPL_NO_POSSIBLE_RECOVERY 13467
#define ER_GRP_RPL_CANT_KILL_THREAD 13468
#define ER_GRP_RPL_RECOVERY_STRAT_CLONE_THRESHOLD 13469
#define ER_GRP_RPL_RECOVERY_STRAT_CLONE_PURGED 13470
#define ER_GRP_RPL_RECOVERY_STRAT_CHOICE 13471
#define ER_GRP_RPL_RECOVERY_STRAT_FALLBACK 13472
#define ER_GRP_RPL_RECOVERY_STRAT_NO_FALLBACK 13473
#define ER_GRP_RPL_REPLICA_THREAD_ERROR_ON_CLONE 13474
#define ER_UNKNOWN_TABLE_IN_UPGRADE 13475
#define ER_IDENT_CAUSES_TOO_LONG_PATH_IN_UPGRADE 13476
#define ER_XA_CANT_CREATE_MDL_BACKUP 13477
#define ER_AUDIT_LOG_SUPER_PRIVILEGE_REQUIRED 13478
#define ER_AUDIT_LOG_UDF_INVALID_ARGUMENT_TYPE 13479
#define ER_AUDIT_LOG_UDF_INVALID_ARGUMENT_COUNT 13480
#define ER_AUDIT_LOG_HAS_NOT_BEEN_INSTALLED 13481
#define ER_AUDIT_LOG_UDF_READ_INVALID_MAX_ARRAY_LENGTH_ARG_TYPE 13482
#define ER_LOG_CANNOT_WRITE_EXTENDED 13483
//#define OBSOLETE_ER_UPGRADE_WITH_PARTITIONED_TABLES_REJECTED 13484
#define ER_KEYRING_AWS_INCORRECT_PROXY 13485
#define ER_GRP_RPL_SERVER_SET_TO_OFFLINE_MODE_DUE_TO_ERRORS 13486
#define ER_GRP_RPL_MESSAGE_SERVICE_FATAL_ERROR 13487
#define ER_WARN_WRONG_COMPRESSION_ALGORITHM_LOG 13488
#define ER_WARN_WRONG_COMPRESSION_LEVEL_LOG 13489
#define ER_PROTOCOL_COMPRESSION_RESET_LOG 13490
#define ER_XPLUGIN_COMPRESSION_ERROR 13491
#define ER_MYSQLBACKUP_MSG 13492
#define ER_WARN_UNKNOWN_KEYRING_AWS_REGION 13493
#define ER_WARN_LOG_PRIVILEGE_CHECKS_USER_DOES_NOT_EXIST 13494
#define ER_WARN_LOG_PRIVILEGE_CHECKS_USER_CORRUPT 13495
#define ER_WARN_LOG_PRIVILEGE_CHECKS_USER_NEEDS_RPL_APPLIER_PRIV 13496
#define ER_OBSOLETE_FILE_PRIVILEGE_FOR_REPLICATION_CHECKS 13497
#define ER_RPL_REPLICA_SQL_THREAD_STARTING_WITH_PRIVILEGE_CHECKS 13498
#define ER_AUDIT_LOG_CANNOT_GENERATE_PASSWORD 13499
#define ER_INIT_FAILED_TO_GENERATE_ROOT_PASSWORD 13500
#define ER_PLUGIN_LOAD_OPTIONS_IGNORED 13501
#define ER_WARN_AUTH_ID_WITH_SYSTEM_USER_PRIV_IN_MANDATORY_ROLES 13502
#define ER_IB_MSG_SKIP_HIDDEN_DIR 13503
#define ER_WARN_RPL_RECOVERY_NO_ROTATE_EVENT_FROM_SOURCE_EOF 13504
#define ER_IB_LOB_ROLLBACK_INDEX_LEN 13505
#define ER_CANT_PROCESS_EXPRESSION_FOR_GENERATED_COLUMN_TO_DD 13506
#define ER_RPL_REPLICA_QUEUE_EVENT_FAILED_INVALID_NON_ROW_FORMAT 13507
#define ER_OBSOLETE_REQUIRE_ROW_FORMAT_VIOLATION 13508
#define ER_LOG_PRIV_CHECKS_REQUIRE_ROW_FORMAT_NOT_SET 13509
#define ER_RPL_REPLICA_SQL_THREAD_DETECTED_UNEXPECTED_EVENT_SEQUENCE 13510
#define ER_IB_MSG_UPGRADE_PARTITION_FILE 13511
#define ER_IB_MSG_DOWNGRADE_PARTITION_FILE 13512
#define ER_IB_MSG_UPGRADE_PARTITION_FILE_IMPORT 13513
#define ER_IB_WARN_OPEN_PARTITION_FILE 13514
#define ER_IB_MSG_FIL_STATE_MOVED_CORRECTED 13515
#define ER_IB_MSG_FIL_STATE_MOVED_CHANGED_PATH 13516
#define ER_IB_MSG_FIL_STATE_MOVED_CHANGED_NAME 13517
#define ER_IB_MSG_FIL_STATE_MOVED_TOO_MANY 13518
#define ER_GR_ELECTED_PRIMARY_GTID_INFORMATION 13519
#define ER_SCHEMA_NAME_IN_UPPER_CASE_NOT_ALLOWED 13520
#define ER_TABLE_NAME_IN_UPPER_CASE_NOT_ALLOWED 13521
#define ER_SCHEMA_NAME_IN_UPPER_CASE_NOT_ALLOWED_FOR_FK 13522
#define ER_TABLE_NAME_IN_UPPER_CASE_NOT_ALLOWED_FOR_FK 13523
#define ER_IB_MSG_DICT_PARTITION_NOT_FOUND 13524
#define ER_ACCESS_DENIED_FOR_USER_ACCOUNT_BLOCKED_BY_PASSWORD_LOCK 13525
#define ER_INNODB_OUT_OF_RESOURCES 13526
//#define OBSOLETE_ER_DD_UPGRADE_FOUND_PREPARED_XA_TRANSACTION 13527
#define ER_MIGRATE_TABLE_TO_DD_OOM 13528
#define ER_RPL_RELAY_LOG_RECOVERY_INFO_AFTER_CLONE 13529
#define ER_IB_MSG_57_UNDO_SPACE_DELETE_FAIL 13530
#define ER_IB_MSG_DBLWR_1285 13531
#define ER_IB_MSG_DBLWR_1286 13532
#define ER_IB_MSG_DBLWR_1287 13533
#define ER_IB_MSG_DBLWR_1288 13534
#define ER_IB_MSG_DBLWR_1290 13535
#define ER_IB_MSG_BAD_DBLWR_FILE_NAME 13536
//#define OBSOLETE_ER_IB_MSG_DBLWR_1292 13537
#define ER_IB_MSG_DBLWR_1293 13538
#define ER_IB_MSG_DBLWR_1294 13539
#define ER_IB_MSG_DBLWR_1295 13540
#define ER_IB_MSG_DBLWR_1296 13541
#define ER_IB_MSG_DBLWR_1297 13542
#define ER_IB_MSG_DBLWR_1298 13543
#define ER_IB_MSG_DBLWR_1300 13544
#define ER_IB_MSG_DBLWR_1301 13545
#define ER_IB_MSG_DBLWR_1304 13546
#define ER_IB_MSG_DBLWR_1305 13547
#define ER_IB_MSG_DBLWR_1306 13548
#define ER_IB_MSG_DBLWR_1307 13549
#define ER_IB_MSG_DBLWR_1308 13550
#define ER_IB_MSG_DBLWR_1309 13551
#define ER_IB_MSG_DBLWR_1310 13552
#define ER_IB_MSG_DBLWR_1311 13553
#define ER_IB_MSG_DBLWR_1312 13554
#define ER_IB_MSG_DBLWR_1313 13555
#define ER_IB_MSG_DBLWR_1314 13556
#define ER_IB_MSG_DBLWR_1315 13557
#define ER_IB_MSG_DBLWR_1316 13558
#define ER_IB_MSG_DBLWR_1317 13559
#define ER_IB_MSG_DBLWR_1318 13560
#define ER_IB_MSG_DBLWR_LOAD_WRONG_SIZE 13561
#define ER_IB_MSG_DBLWR_1320 13562
#define ER_IB_MSG_DBLWR_1321 13563
#define ER_IB_MSG_DBLWR_OPEN_OR_CREATE_WRONG_SIZE 13564
#define ER_IB_MSG_DBLWR_1323 13565
#define ER_IB_MSG_DBLWR_1324 13566
#define ER_IB_MSG_DBLWR_1325 13567
#define ER_IB_MSG_DBLWR_1326 13568
#define ER_IB_MSG_DBLWR_1327 13569
#define ER_IB_MSG_GTID_FLUSH_AT_SHUTDOWN 13570
#define ER_IB_MSG_57_STAT_SPACE_DELETE_FAIL 13571
#define ER_NDBINFO_UPGRADING_SCHEMA 13572
#define ER_NDBINFO_NOT_UPGRADING_SCHEMA 13573
#define ER_NDBINFO_UPGRADING_SCHEMA_FAIL 13574
//#define OBSOLETE_ER_IB_MSG_CREATE_LOG_FILE 13575
#define ER_IB_MSG_INNODB_START_INITIALIZE 13576
#define ER_IB_MSG_INNODB_END_INITIALIZE 13577
#define ER_IB_MSG_PAGE_ARCH_NO_RESET_POINTS 13578
#define ER_IB_WRN_PAGE_ARCH_FLUSH_DATA 13579
#define ER_IB_ERR_PAGE_ARCH_INVALID_DOUBLE_WRITE_BUF 13580
#define ER_IB_ERR_PAGE_ARCH_RECOVERY_FAILED 13581
#define ER_IB_ERR_PAGE_ARCH_INVALID_FORMAT 13582
#define ER_INVALID_XPLUGIN_SOCKET_SAME_AS_SERVER 13583
#define ER_INNODB_UNABLE_TO_ACQUIRE_DD_OBJECT 13584
//#define OBSOLETE_ER_WARN_LOG_DEPRECATED_PARTITION_PREFIX_KEY 13585
#define ER_IB_MSG_UNDO_TRUNCATE_TOO_OFTEN 13586
#define ER_GRP_RPL_IS_STARTING 13587
#define ER_IB_MSG_INVALID_LOCATION_FOR_TABLESPACE 13588
#define ER_IB_MSG_INVALID_LOCATION_WRONG_DB 13589
#define ER_IB_MSG_CANNOT_FIND_DD_UNDO_SPACE 13590
#define ER_GRP_RPL_RECOVERY_ENDPOINT_FORMAT 13591
#define ER_GRP_RPL_RECOVERY_ENDPOINT_INVALID 13592
#define ER_GRP_RPL_RECOVERY_ENDPOINT_INVALID_DONOR_ENDPOINT 13593
#define ER_GRP_RPL_RECOVERY_ENDPOINT_INTERFACES_IPS 13594
#define ER_WARN_TLS_CHANNEL_INITIALIZATION_ERROR 13595
#define ER_XPLUGIN_FAILED_TO_VALIDATE_ADDRESS 13596
#define ER_XPLUGIN_FAILED_TO_BIND_INTERFACE_ADDRESS 13597
#define ER_IB_ERR_RECOVERY_REDO_DISABLED 13598
#define ER_IB_WRN_FAST_SHUTDOWN_REDO_DISABLED 13599
#define ER_IB_WRN_REDO_DISABLED 13600
#define ER_IB_WRN_REDO_ENABLED 13601
#define ER_TLS_CONFIGURED_FOR_CHANNEL 13602
#define ER_TLS_CONFIGURATION_REUSED 13603
#define ER_IB_TABLESPACE_PATH_VALIDATION_SKIPPED 13604
#define ER_IB_CANNOT_UPGRADE_WITH_DISCARDED_TABLESPACES 13605
#define ER_USERNAME_TRUNKATED 13606
#define ER_HOSTNAME_TRUNKATED 13607
#define ER_IB_MSG_TRX_RECOVERY_ROLLBACK_NOT_COMPLETED 13608
#define ER_AUTHCACHE_ROLE_EDGES_IGNORED_EMPTY_NAME 13609
#define ER_AUTHCACHE_ROLE_EDGES_UNKNOWN_AUTHORIZATION_ID 13610
#define ER_AUTHCACHE_DEFAULT_ROLES_IGNORED_EMPTY_NAME 13611
#define ER_AUTHCACHE_DEFAULT_ROLES_UNKNOWN_AUTHORIZATION_ID 13612
#define ER_IB_ERR_DDL_LOG_INSERT_FAILURE 13613
#define ER_IB_LOCK_VALIDATE_LATCH_ORDER_VIOLATION 13614
#define ER_IB_RELOCK_LATCH_ORDER_VIOLATION 13615
//#define OBSOLETE_ER_IB_MSG_1352 13616
//#define OBSOLETE_ER_IB_MSG_1353 13617
//#define OBSOLETE_ER_IB_MSG_1354 13618
//#define OBSOLETE_ER_IB_MSG_1355 13619
//#define OBSOLETE_ER_IB_MSG_1356 13620
#define ER_IB_MSG_1357 13621
#define ER_IB_MSG_1358 13622
#define ER_IB_MSG_1359 13623
#define ER_IB_FAILED_TO_DELETE_TABLESPACE_FILE 13624
#define ER_IB_UNABLE_TO_EXPAND_TEMPORARY_TABLESPACE_POOL 13625
#define ER_IB_TMP_TABLESPACE_CANNOT_CREATE_DIRECTORY 13626
#define ER_IB_MSG_SCANNING_TEMP_TABLESPACE_DIR 13627
#define ER_IB_ERR_TEMP_TABLESPACE_DIR_DOESNT_EXIST 13628
#define ER_IB_ERR_TEMP_TABLESPACE_DIR_EMPTY 13629
#define ER_IB_ERR_TEMP_TABLESPACE_DIR_CONTAINS_SEMICOLON 13630
#define ER_IB_ERR_TEMP_TABLESPACE_DIR_SUBDIR_OF_DATADIR 13631
#define ER_IB_ERR_SCHED_SETAFFNINITY_FAILED 13632
#define ER_IB_ERR_UNKNOWN_PAGE_FETCH_MODE 13633
#define ER_IB_ERR_LOG_PARSING_BUFFER_OVERFLOW 13634
#define ER_IB_ERR_NOT_ENOUGH_MEMORY_FOR_PARSE_BUFFER 13635
#define ER_IB_MSG_1372 13636
#define ER_IB_MSG_1373 13637
#define ER_IB_MSG_1374 13638
#define ER_IB_MSG_1375 13639
#define ER_IB_ERR_ZLIB_UNCOMPRESS_FAILED 13640
#define ER_IB_ERR_ZLIB_BUF_ERROR 13641
#define ER_IB_ERR_ZLIB_MEM_ERROR 13642
#define ER_IB_ERR_ZLIB_DATA_ERROR 13643
#define ER_IB_ERR_ZLIB_UNKNOWN_ERROR 13644
#define ER_IB_MSG_1381 13645
#define ER_IB_ERR_INDEX_RECORDS_WRONG_ORDER 13646
#define ER_IB_ERR_INDEX_DUPLICATE_KEY 13647
#define ER_IB_ERR_FOUND_N_DUPLICATE_KEYS 13648
#define ER_IB_ERR_FOUND_N_RECORDS_WRONG_ORDER 13649
#define ER_IB_ERR_PARALLEL_READ_OOM 13650
#define ER_IB_MSG_UNDO_MARKED_ACTIVE 13651
#define ER_IB_MSG_UNDO_ALTERED_ACTIVE 13652
#define ER_IB_MSG_UNDO_ALTERED_INACTIVE 13653
#define ER_IB_MSG_UNDO_MARKED_EMPTY 13654
#define ER_IB_MSG_UNDO_TRUNCATE_DELAY_BY_CLONE 13655
#define ER_IB_MSG_UNDO_TRUNCATE_DELAY_BY_MDL 13656
#define ER_IB_MSG_INJECT_CRASH 13657
#define ER_IB_MSG_INJECT_FAILURE 13658
#define ER_GRP_RPL_TIMEOUT_RECEIVED_VC_LEAVE_ON_REJOIN 13659
#define ER_RPL_ASYNC_RECONNECT_FAIL_NO_SOURCE 13660
#define ER_UDF_REGISTER_SERVICE_ERROR 13661
#define ER_UDF_REGISTER_ERROR 13662
#define ER_UDF_UNREGISTER_ERROR 13663
#define ER_EMPTY_PRIVILEGE_NAME_IGNORED 13664
#define ER_IB_MSG_INCORRECT_SIZE 13665
#define ER_TMPDIR_PATH_TOO_LONG 13666
#define ER_ERROR_LOG_DESTINATION_NOT_A_FILE 13667
#define ER_NO_ERROR_LOG_PARSER_CONFIGURED 13668
#define ER_UPGRADE_NONEXISTENT_SCHEMA 13669
#define ER_IB_MSG_CREATED_UNDO_SPACE 13670
#define ER_IB_MSG_DROPPED_UNDO_SPACE 13671
#define ER_IB_MSG_MASTER_KEY_ROTATED 13672
#define ER_IB_DBLWR_DECOMPRESS_FAILED 13673
#define ER_IB_DBLWR_DECRYPT_FAILED 13674
#define ER_IB_DBLWR_KEY_MISSING 13675
#define ER_INNODB_IO_WRITE_ERROR_RETRYING 13676
#define ER_INNODB_IO_WRITE_FAILED 13677
#define ER_LOG_COMPONENT_CANNOT_INIT 13678
#define ER_RPL_ASYNC_CHANNEL_CANT_CONNECT 13679
#define ER_RPL_ASYNC_SENDER_ADDED 13680
#define ER_RPL_ASYNC_SENDER_REMOVED 13681
#define ER_RPL_ASYNC_CHANNEL_STOPPED_QUORUM_LOST 13682
#define ER_RPL_ASYNC_CHANNEL_CANT_CONNECT_NO_QUORUM 13683
//#define OBSOLETE_ER_RPL_ASYNC_EXECUTING_QUERY 13684
#define ER_RPL_REPLICA_MONITOR_IO_THREAD_EXITING 13685
#define ER_RPL_ASYNC_MANAGED_NAME_REMOVED 13686
#define ER_RPL_ASYNC_MANAGED_NAME_ADDED 13687
#define ER_RPL_ASYNC_READ_FAILOVER_TABLE 13688
#define ER_RPL_REPLICA_MONITOR_IO_THREAD_RECONNECT_CHANNEL 13689
#define ER_REPLICA_ANON_TO_GTID_IS_LOCAL_OR_UUID_AND_GTID_MODE_NOT_ON 13690
#define ER_REPLICA_ANONYMOUS_TO_GTID_UUID_SAME_AS_GROUP_NAME 13691
#define ER_GRP_RPL_GRP_NAME_IS_SAME_AS_ANONYMOUS_TO_GTID_UUID 13692
#define ER_WARN_GTID_THRESHOLD_BREACH 13693
#define ER_HEALTH_INFO 13694
#define ER_HEALTH_WARNING 13695
#define ER_HEALTH_ERROR 13696
#define ER_HEALTH_WARNING_DISK_USAGE_LEVEL_1 13697
#define ER_HEALTH_WARNING_DISK_USAGE_LEVEL_2 13698
#define ER_HEALTH_WARNING_DISK_USAGE_LEVEL_3 13699
#define ER_IB_INNODB_TBSP_OUT_OF_SPACE 13700
#define ER_GRP_RPL_APPLIER_CHANNEL_STILL_RUNNING 13701
#define ER_RPL_ASYNC_RECONNECT_GTID_MODE_OFF_CHANNEL 13702
#define ER_FIREWALL_SERVICES_NOT_ACQUIRED 13703
#define ER_FIREWALL_UDF_REGISTER_FAILED 13704
#define ER_FIREWALL_PFS_TABLE_REGISTER_FAILED 13705
#define ER_IB_MSG_STATS_SAMPLING_TOO_LARGE 13706
#define ER_AUDIT_LOG_FILE_PRUNE_FAILED 13707
#define ER_AUDIT_LOG_FILE_AUTO_PRUNED 13708
#define ER_COMPONENTS_INFRASTRUCTURE_MANIFEST_INIT 13709
#define ER_COMPONENTS_INFRASTRUCTURE_MANIFEST_DEINIT 13710
#define ER_WARN_COMPONENTS_INFRASTRUCTURE_MANIFEST_NOT_RO 13711
#define ER_WARN_NO_KEYRING_COMPONENT_SERVICE_FOUND 13712
#define ER_NOTE_KEYRING_COMPONENT_INITIALIZED 13713
#define ER_KEYRING_COMPONENT_NOT_INITIALIZED 13714
#define ER_KEYRING_COMPONENT_EXCEPTION 13715
#define ER_KEYRING_COMPONENT_MEMORY_ALLOCATION_ERROR 13716
#define ER_NOTE_KEYRING_COMPONENT_AES_INVALID_MODE_BLOCK_SIZE 13717
#define ER_NOTE_KEYRING_COMPONENT_AES_DATA_IDENTIFIER_EMPTY 13718
#define ER_NOTE_KEYRING_COMPONENT_AES_INVALID_KEY 13719
#define ER_NOTE_KEYRING_COMPONENT_AES_OPERATION_ERROR 13720
#define ER_NOTE_KEYRING_COMPONENT_READ_DATA_NOT_FOUND 13721
#define ER_NOTE_KEYRING_COMPONENT_WRITE_MAXIMUM_DATA_LENGTH 13722
#define ER_NOTE_KEYRING_COMPONENT_STORE_FAILED 13723
#define ER_NOTE_KEYRING_COMPONENT_REMOVE_FAILED 13724
#define ER_NOTE_KEYRING_COMPONENT_GENERATE_FAILED 13725
#define ER_NOTE_KEYRING_COMPONENT_KEYS_METADATA_ITERATOR_FETCH_FAILED 13726
#define ER_NOTE_KEYRING_COMPONENT_METADATA_ITERATOR_INVALID_OUT_PARAM 13727
#define ER_IB_WRN_FAILED_TO_ACQUIRE_SERVICE 13728
#define ER_IB_WRN_OLD_GEOMETRY_TYPE 13729
#define ER_NET_WAIT_ERROR2 13730
#define ER_GRP_RPL_MEMBER_ACTION_TRIGGERED 13731
#define ER_GRP_RPL_MEMBER_ACTION_FAILURE_IGNORE 13732
#define ER_GRP_RPL_MEMBER_ACTION_FAILURE 13733
#define ER_GRP_RPL_MEMBER_ACTION_PARSE_ON_RECEIVE 13734
#define ER_GRP_RPL_MEMBER_ACTION_UPDATE_ACTIONS 13735
#define ER_GRP_RPL_MEMBER_ACTION_GET_EXCHANGEABLE_DATA 13736
#define ER_GRP_RPL_MEMBER_ACTION_DEFAULT_CONFIGURATION 13737
#define ER_GRP_RPL_MEMBER_ACTION_UNABLE_TO_SET_DEFAULT_CONFIGURATION 13738
#define ER_GRP_RPL_MEMBER_ACTION_PARSE_ON_MEMBER_JOIN 13739
#define ER_GRP_RPL_MEMBER_ACTION_UPDATE_ACTIONS_ON_MEMBER_JOIN 13740
#define ER_GRP_RPL_MEMBER_ACTION_INVALID_ACTIONS_ON_MEMBER_JOIN 13741
#define ER_GRP_RPL_MEMBER_ACTION_ENABLED 13742
#define ER_GRP_RPL_MEMBER_ACTION_DISABLED 13743
#define ER_GRP_RPL_MEMBER_ACTIONS_RESET 13744
//#define OBSOLETE_ER_DEPRECATED_TLS_VERSION_SESSION 13745
//#define OBSOLETE_ER_WARN_DEPRECATED_TLS_VERSION_FOR_CHANNEL 13746
#define ER_FIREWALL_DEPRECATED_USER_PROFILE 13747
#define ER_GRP_RPL_VIEW_CHANGE_UUID_INVALID 13748
#define ER_GRP_RPL_VIEW_CHANGE_UUID_SAME_AS_GROUP_NAME 13749
#define ER_GRP_RPL_GROUP_NAME_SAME_AS_VIEW_CHANGE_UUID 13750
#define ER_GRP_RPL_VIEW_CHANGE_UUID_IS_SAME_AS_ANONYMOUS_TO_GTID_UUID 13751
#define ER_GRP_RPL_GRP_VIEW_CHANGE_UUID_IS_INCOMPATIBLE_WITH_SERVER_UUID 13752
#define ER_GRP_RPL_VIEW_CHANGE_UUID_DIFF_FROM_GRP 13753
#define ER_WARN_REPLICA_ANONYMOUS_TO_GTID_UUID_SAME_AS_VIEW_CHANGE_UUID 13754
#define ER_GRP_RPL_FAILED_TO_PARSE_THE_VIEW_CHANGE_UUID 13755
#define ER_GRP_RPL_FAILED_TO_GENERATE_SIDNO_FOR_VIEW_CHANGE_UUID 13756
#define ER_GRP_RPL_VIEW_CHANGE_UUID_PARSE_ERROR 13757
#define ER_GRP_RPL_UPDATE_GRPGTID_VIEW_CHANGE_UUID_EXECUTED_ERROR 13758
#define ER_GRP_RPL_ADD_VIEW_CHANGE_UUID_TO_GRP_SID_MAP_ERROR 13759
#define ER_GRP_RPL_DONOR_VIEW_CHANGE_UUID_TRANS_INFO_ERROR 13760
#define ER_WARN_GRP_RPL_VIEW_CHANGE_UUID_FAIL_GET_VARIABLE 13761
#define ER_WARN_ADUIT_LOG_MAX_SIZE_AND_PRUNE_SECONDS_LOG 13762
#define ER_WARN_ADUIT_LOG_MAX_SIZE_CLOSE_TO_ROTATE_ON_SIZE_LOG 13763
#define ER_PLUGIN_INVALID_TABLE_DEFINITION 13764
#define ER_AUTH_KERBEROS_LOGGER_GENERIC_MSG 13765
#define ER_INSTALL_PLUGIN_CONFLICT_LOG 13766
#define ER_DEPRECATED_PERSISTED_VARIABLE_WITH_ALIAS 13767
#define ER_LOG_COMPONENT_FLUSH_FAILED 13768
#define ER_IB_MSG_REENCRYPTED_TABLESPACE_KEY 13769
#define ER_IB_MSG_REENCRYPTED_GENERAL_TABLESPACE_KEY 13770
#define ER_IB_ERR_PAGE_ARCH_DBLWR_INIT_FAILED 13771
#define ER_IB_MSG_RECOVERY_NO_SPACE_IN_REDO_LOG__SKIP_IBUF_MERGES 13772
#define ER_IB_MSG_RECOVERY_NO_SPACE_IN_REDO_LOG__UNEXPECTED 13773
#define ER_WARN_AUDIT_LOG_FORMAT_UNIX_TIMESTAMP_ONLY_WHEN_JSON_LOG 13774
#define ER_PREPARE_FOR_PRIMARY_ENGINE 13775
#define ER_IB_MSG_PAR_RSEG_INIT_COMPLETE_MSG 13776
#define ER_IB_MSG_PAR_RSEG_INIT_TIME_MSG 13777
#define ER_DDL_MSG_1 13778
#define ER_MTR_MSG_1 13779
#define ER_GRP_RPL_MYSQL_NETWORK_PROVIDER_CLIENT_ERROR_CONN_ERR 13780
#define ER_GRP_RPL_MYSQL_NETWORK_PROVIDER_CLIENT_ERROR_COMMAND_ERR 13781
#define ER_GRP_RPL_FAILOVER_CONF_GET_EXCHANGEABLE_DATA 13782
#define ER_GRP_RPL_FAILOVER_CONF_DEFAULT_CONFIGURATION 13783
#define ER_GRP_RPL_FAILOVER_CONF_UNABLE_TO_SET_DEFAULT_CONFIGURATION 13784
#define ER_GRP_RPL_FAILOVER_CONF_PARSE_ON_MEMBER_JOIN 13785
#define ER_GRP_RPL_FAILOVER_CONF_CHANNEL_DOES_NOT_EXIST 13786
#define ER_GRP_RPL_FAILOVER_REGISTER_MESSAGE_LISTENER_SERVICE 13787
#define ER_GRP_RPL_FAILOVER_PRIMARY_WITHOUT_MAJORITY 13788
#define ER_GRP_RPL_FAILOVER_PRIMARY_BACK_TO_MAJORITY 13789
#define ER_RPL_INCREMENTING_MEMBER_ACTION_VERSION 13790
#define ER_GRP_RPL_REPLICA_THREAD_ERROR_ON_SECONDARY_MEMBER 13791
#define ER_IB_MSG_CLONE_DDL_NTFN 13792
#define ER_IB_MSG_CLONE_DDL_APPLY 13793
#define ER_IB_MSG_CLONE_DDL_INVALIDATE 13794
#define ER_IB_MSG_UNDO_ENCRYPTION_INFO_LOADED 13795
#define ER_IB_WRN_ENCRYPTION_INFO_SIZE_MISMATCH 13796
#define ER_INVALID_AUTHENTICATION_POLICY 13797
#define ER_AUTHENTICATION_PLUGIN_REGISTRATION_FAILED 13798
#define ER_AUTHENTICATION_PLUGIN_REGISTRATION_INSUFFICIENT_BUFFER 13799
#define ER_AUTHENTICATION_PLUGIN_AUTH_DATA_CORRUPT 13800
#define ER_AUTHENTICATION_PLUGIN_SIGNATURE_CORRUPT 13801
#define ER_AUTHENTICATION_PLUGIN_VERIFY_SIGNATURE_FAILED 13802
#define ER_AUTHENTICATION_PLUGIN_OOM 13803
#define ER_AUTHENTICATION_PLUGIN_LOG 13804
#define ER_WARN_REPLICA_GTID_ONLY_AND_GTID_MODE_NOT_ON 13805
#define ER_WARN_L_DISABLE_GTID_ONLY_WITH_SOURCE_AUTO_POS_INVALID_POS 13806
#define ER_RPL_CANNOT_OPEN_RELAY_LOG 13807
#define ER_AUTHENTICATION_OCI_PLUGIN_NOT_INITIALIZED 13808
#define ER_AUTHENTICATION_OCI_PRIVATE_KEY_ERROR 13809
#define ER_AUTHENTICATION_OCI_DOWNLOAD_PUBLIC_KEY 13810
#define ER_AUTHENTICATION_OCI_IMDS 13811
#define ER_AUTHENTICATION_OCI_IAM 13812
#define ER_AUTHENTICATION_OCI_INVALID_AUTHENTICATION_STRING 13813
#define ER_AUTHENTICATION_OCI_NO_MATCHING_GROUPS 13814
#define ER_AUTHENTICATION_OCI_NO_GROUPS_FOUND 13815
#define ER_AUTHENTICATION_OCI_NONCE 13816
#define ER_HEALTH_WARNING_MEMORY_USAGE_LEVEL_1 13817
#define ER_HEALTH_WARNING_MEMORY_USAGE_LEVEL_2 13818
#define ER_HEALTH_WARNING_MEMORY_USAGE_LEVEL_3 13819
#define ER_GRP_RPL_SET_SINGLE_CONSENSUS_LEADER 13820
#define ER_GRP_RPL_ERROR_SET_SINGLE_CONSENSUS_LEADER 13821
#define ER_GRP_RPL_SET_MULTI_CONSENSUS_LEADER 13822
#define ER_GRP_RPL_ERROR_SET_MULTI_CONSENSUS_LEADER 13823
#define ER_GRP_RPL_PAXOS_SINGLE_LEADER_DIFF_FROM_GRP 13824
#define ER_MFA_USER_ATTRIBUTES_CORRUPT 13825
#define ER_MFA_PLUGIN_NOT_LOADED 13826
#define ER_WARN_DEPRECATED_CHARSET_OPTION 13827
#define ER_WARN_DEPRECATED_COLLATION_OPTION 13828
#define ER_REGEXP_MISSING_ICU_DATADIR 13829
#define ER_IB_WARN_MANY_NON_LRU_FILES_OPENED 13830
#define ER_IB_MSG_TRYING_TO_OPEN_FILE_FOR_LONG_TIME 13831
#define ER_GLOBAL_CONN_LIMIT 13832
#define ER_CONN_LIMIT 13833
#define ER_WARN_AUDIT_LOG_DISABLED 13834
#define ER_INVALID_TLS_VERSION 13835
#define ER_RPL_RELAY_LOG_RECOVERY_GTID_ONLY 13836
#define ER_KEYRING_OKV_STANDBY_SERVER_COUNT_EXCEEDED 13837
#define ER_WARN_MIGRATION_EMPTY_SOURCE_KEYRING 13838
#define ER_WARN_CANNOT_PERSIST_SENSITIVE_VARIABLES 13839
#define ER_CANNOT_INTERPRET_PERSISTED_SENSITIVE_VARIABLES 13840
#define ER_PERSISTED_VARIABLES_KEYRING_SUPPORT_REQUIRED 13841
#define ER_PERSISTED_VARIABLES_MASTER_KEY_NOT_FOUND 13842
#define ER_PERSISTED_VARIABLES_MASTER_KEY_CANNOT_BE_GENERATED 13843
#define ER_PERSISTED_VARIABLES_ENCRYPTION_FAILED 13844
#define ER_PERSISTED_VARIABLES_DECRYPTION_FAILED 13845
#define ER_PERSISTED_VARIABLES_LACK_KEYRING_SUPPORT 13846
#define ER_MY_MALLOC_USING_JEMALLOC 13847
#define ER_MY_MALLOC_USING_STD_MALLOC 13848
#define ER_MY_MALLOC_LOADLIBRARY_FAILED 13849
#define ER_MY_MALLOC_GETPROCADDRESS_FAILED 13850
#define ER_ACCOUNT_WITH_EXPIRED_PASSWORD 13851
#define ER_THREAD_POOL_PLUGIN_STARTED 13852
#define ER_THREAD_POOL_DEDICATED_LISTENERS_INVALID 13853
#define ER_IB_DBLWR_BYTES_INFO 13854
#define ER_IB_RDBLWR_BYTES_INFO 13855
#define ER_IB_MSG_LOG_FILE_IS_EMPTY 13856
#define ER_IB_MSG_LOG_FILE_TOO_SMALL 13857
#define ER_IB_MSG_LOG_FILE_TOO_BIG 13858
#define ER_IB_MSG_LOG_FILE_HEADER_READ_FAILED 13859
#define ER_IB_MSG_LOG_INIT_DIR_NOT_EMPTY_WONT_INITIALIZE 13860
#define ER_IB_MSG_LOG_INIT_DIR_LIST_FAILED 13861
#define ER_IB_MSG_LOG_INIT_DIR_MISSING_SUBDIR 13862
#define ER_IB_MSG_LOG_FILES_CREATED_BY_CLONE_AND_READ_ONLY_MODE 13863
#define ER_IB_MSG_LOG_WRITER_WRITE_FAILED 13864
#define ER_IB_MSG_LOG_WRITER_WAIT_ON_NEW_LOG_FILE 13865
#define ER_IB_MSG_RECOVERY_CHECKPOINT_OUTSIDE_LOG_FILE 13866
#define ER_IB_MSG_LOG_WRITER_ENTERED_EXTRA_MARGIN 13867
#define ER_IB_MSG_LOG_WRITER_EXITED_EXTRA_MARGIN 13868
#define ER_IB_MSG_LOG_PARAMS_FILE_SIZE_UNUSED 13869
#define ER_IB_MSG_LOG_PARAMS_N_FILES_UNUSED 13870
#define ER_IB_MSG_LOG_UPGRADE_FORCED_RECV 13871
#define ER_IB_MSG_LOG_UPGRADE_IN_READ_ONLY_MODE 13872
#define ER_IB_MSG_LOG_UPGRADE_CLONED_DB 13873
#define ER_IB_MSG_LOG_UPGRADE_UNINITIALIZED_FILES 13874
#define ER_IB_MSG_LOG_UPGRADE_CORRUPTION__UNEXPECTED 13875
//#define OBSOLETE_ER_IB_MSG_LOG_UPGRADE_NON_PERSISTED_DD_METADATA 13876
//#define OBSOLETE_ER_IB_MSG_LOG_UPGRADE_FLUSH_FAILED__UNEXPECTED 13877
//#define OBSOLETE_ER_IB_MSG_LOG_FILES_RESIZE_ON_START_FAILED__UNEXPECTED 13878
#define ER_IB_MSG_LOG_FILE_FOREIGN_UUID 13879
#define ER_IB_MSG_LOG_FILE_INVALID_START_LSN 13880
#define ER_IB_MSG_LOG_FILE_INVALID_LSN_RANGES 13881
#define ER_IB_MSG_LOG_FILE_MISSING_FOR_ID 13882
#define ER_IB_MSG_LOG_CHECKPOINT_FOUND 13883
#define ER_IB_MSG_LOG_FILES_CAPACITY_CHANGED 13884
#define ER_IB_MSG_LOG_FILES_RESIZE_REQUESTED 13885
#define ER_IB_MSG_LOG_FILES_RESIZE_CANCELLED 13886
#define ER_IB_MSG_LOG_FILES_RESIZE_FINISHED 13887
#define ER_IB_MSG_LOG_FILES_UPGRADE 13888
#define ER_IB_MSG_LOG_FILE_MARK_CURRENT_AS_INCOMPLETE 13889
#define ER_IB_MSG_LOG_FILE_REMOVE_FAILED 13890
#define ER_IB_MSG_LOG_FILE_RENAME_ON_CREATE_FAILED 13891
#define ER_IB_MSG_LOG_FILES_CREATED_BY_UNKNOWN_CREATOR 13892
#define ER_IB_MSG_LOG_FILES_FOUND_MISSING 13893
#define ER_IB_MSG_LOG_FILE_FORMAT_TOO_NEW 13894
#define ER_IB_MSG_LOG_FILE_FORMAT_TOO_OLD 13895
#define ER_IB_MSG_LOG_FILE_DIFFERENT_FORMATS 13896
#define ER_IB_MSG_LOG_PRE_8_0_30_MISSING_FILE0 13897
#define ER_IB_MSG_LOG_PFS_ACQUIRE_SERVICES_FAILED 13898
#define ER_IB_MSG_LOG_PFS_CREATE_TABLES_FAILED 13899
#define ER_IB_MSG_LOG_FILE_TRUNCATE 13900
#define ER_IB_MSG_LOG_FILE_UNUSED_RESIZE_FAILED 13901
#define ER_IB_MSG_LOG_FILE_UNUSED_REMOVE_FAILED 13902
#define ER_IB_MSG_LOG_FILE_UNUSED_RENAME_FAILED 13903
#define ER_IB_MSG_LOG_FILE_UNUSED_MARK_AS_IN_USE_FAILED 13904
#define ER_IB_MSG_LOG_FILE_MARK_AS_UNUSED_FAILED 13905
#define ER_IB_MSG_LOG_PARAMS_DEDICATED_SERVER_IGNORED 13906
#define ER_IB_MSG_LOG_PARAMS_LEGACY_USAGE 13907
#define ER_GRP_RPL_FAILED_TO_LOG_VIEW_CHANGE 13908
#define ER_BINLOG_CRASH_RECOVERY_MALFORMED_LOG 13909
#define ER_BINLOG_CRASH_RECOVERY_ERROR_RETURNED_SE 13910
#define ER_BINLOG_CRASH_RECOVERY_ENGINE_RESULTS 13911
#define ER_BINLOG_CRASH_RECOVERY_COMMIT_FAILED 13912
#define ER_BINLOG_CRASH_RECOVERY_ROLLBACK_FAILED 13913
#define ER_BINLOG_CRASH_RECOVERY_PREPARE_FAILED 13914
#define ER_COMPONENT_EE_SYS_VAR_REGISTRATION_FAILURE 13915
#define ER_COMPONENT_EE_SYS_VAR_DEREGISTRATION_FAILURE 13916
#define ER_COMPONENT_EE_FUNCTION_REGISTRATION_FAILURE 13917
#define ER_COMPONENT_EE_FUNCTION_DEREGISTRATION_FAILURE 13918
#define ER_COMPONENT_EE_FUNCTION_INVALID_ARGUMENTS 13919
#define ER_COMPONENT_EE_FUNCTION_INVALID_ALGORITHM 13920
#define ER_COMPONENT_EE_FUNCTION_KEY_LENGTH_OUT_OF_RANGE 13921
#define ER_COMPONENT_EE_FUNCTION_PRIVATE_KEY_GENERATION_FAILURE 13922
#define ER_COMPONENT_EE_FUNCTION_PUBLIC_KEY_GENERATION_FAILURE 13923
#define ER_COMPONENT_EE_DATA_LENGTH_OUT_OF_RAGE 13924
#define ER_COMPONENT_EE_DATA_ENCRYPTION_ERROR 13925
#define ER_COMPONENT_EE_DATA_DECRYPTION_ERROR 13926
#define ER_COMPONENT_EE_DATA_SIGN_ERROR 13927
#define ER_COMPONENT_EE_OPENSSL_ERROR 13928
#define ER_COMPONENT_EE_INSUFFICIENT_LENGTH 13929
#define ER_SYSTEMD_NOTIFY_DEBUG 13930
#define ER_TMP_SESSION_FOR_VAR 13931
#define ER_BUILD_ID 13932
#define ER_THREAD_POOL_CANNOT_REGISTER_DYNAMIC_PRIVILEGE 13933
#define ER_IB_MSG_LOG_WRITER_WAIT_ON_CONSUMER 13934
#define ER_CONDITIONAL_DEBUG 13935
#define ER_IB_MSG_PARSE_OLD_REDO_INDEX_VERSION 13936
//#define OBSOLETE_ER_RES_GRP_FAILED_TO_SWITCH_RESOURCE_GROUP 13937
//#define OBSOLETE_ER_RES_GRP_SWITCH_FAILED_COULD_NOT_ACQUIRE_GLOBAL_LOCK 13938
//#define OBSOLETE_ER_RES_GRP_SWITCH_FAILED_COULD_NOT_ACQUIRE_LOCK 13939
//#define OBSOLETE_ER_RES_GRP_SWITCH_FAILED_UNABLE_TO_APPLY_RES_GRP 13940
#define ER_IB_MSG_CLEAR_INSTANT_DROP_COLUMN_METADATA 13941
#define ER_COMPONENT_KEYRING_OCI_OPEN_KEY_FILE 13942
#define ER_COMPONENT_KEYRING_OCI_CREATE_PRIVATE_KEY 13943
#define ER_COMPONENT_KEYRING_OCI_READ_KEY_FILE 13944
#define ER_NOTE_COMPONENT_KEYRING_OCI_MISSING_NAME_OR_TYPE 13945
#define ER_WARN_COMPONENT_KEYRING_OCI_DUPLICATE_KEY 13946
#define ER_KEYRING_OCI_PARSE_JSON 13947
#define ER_KEYRING_OCI_INVALID_JSON 13948
#define ER_KEYRING_OCI_HTTP_REQUEST 13949
#define ER_THREAD_POOL_SYSVAR_CHANGE 13950
#define ER_STACK_BACKTRACE 13951
#define ER_IB_MSG_BUF_POOL_RESIZE_COMPLETE_CUR_CODE 13952
#define ER_IB_MSG_BUF_POOL_RESIZE_PROGRESS_UPDATE 13953
#define ER_IB_MSG_BUF_POOL_RESIZE_CODE_STATUS 13954
#define ER_THREAD_POOL_QUERY_THREADS_PER_GROUP_INVALID 13955
#define ER_THREAD_POOL_QUERY_THRS_PER_GRP_EXCEEDS_TXN_THR_LIMIT 13956
#define ER_IB_MSG_INVALID_PAGE_TYPE 13957
#define ER_IB_PARALLEL_READER_WORKER_INFO 13958
#define ER_IB_BULK_LOAD_SUBTREE_INFO 13959
#define ER_IB_BULK_FLUSHER_INFO 13960
#define ER_IB_BUFFER_POOL_OVERUSE 13961
#define ER_IB_BUFFER_POOL_FULL 13962
#define ER_IB_DUPLICATE_KEY 13963
#define ER_REPLICATION_INCOMPATIBLE_TABLE_WITH_GIPK 13964
#define ER_BULK_EXECUTOR_INFO 13965
#define ER_BULK_LOADER_INFO 13966
#define ER_BULK_LOADER_FILE_CONTAINS_LESS_LINES_THAN_IGNORE_CLAUSE_LOG 13967
#define ER_BULK_READER_INFO 13968
#define ER_BULK_READER_LIBCURL_INIT_FAILED_LOG 13969
#define ER_BULK_READER_LIBCURL_ERROR_LOG 13970
#define ER_BULK_READER_SERVER_ERROR_LOG 13971
#define ER_BULK_READER_COMMUNICATION_ERROR_LOG 13972
#define ER_BULK_PARSER_MISSING_ENCLOSED_BY_LOG 13973
#define ER_BULK_PARSER_ROW_BUFFER_MAX_TOTAL_COLS_EXCEEDED_LOG 13974
#define ER_BULK_PARSER_COPY_BUFFER_SIZE_EXCEEDED_LOG 13975
#define ER_BULK_PARSER_UNEXPECTED_END_OF_INPUT_LOG 13976
#define ER_BULK_PARSER_UNEXPECTED_ROW_TERMINATOR_LOG 13977
#define ER_BULK_PARSER_UNEXPECTED_CHAR_AFTER_ENDING_ENCLOSED_BY_LOG 13978
#define ER_BULK_PARSER_UNEXPECTED_CHAR_AFTER_NULL_ESCAPE_LOG 13979
#define ER_BULK_PARSER_UNEXPECTED_CHAR_AFTER_COLUMN_TERMINATOR_LOG 13980
#define ER_BULK_PARSER_INCOMPLETE_ESCAPE_SEQUENCE_LOG 13981
#define ER_LOAD_BULK_DATA_WRONG_VALUE_FOR_FIELD_LOG 13982
#define ER_LOAD_BULK_DATA_WARN_NULL_TO_NOTNULL_LOG 13983
#define ER_IB_BULK_LOAD_THREAD_FAIL 13984
#define ER_IB_BULK_LOAD_MERGE_FAIL 13985
#define ER_IB_LOAD_BULK_CONCURRENCY_REDUCED 13986
#define ER_PLUGIN_EXCEPTION_OPERATION_FAILED 13987
#define ER_REQUIRE_TABLE_PRIMARY_KEY_CHECK_GENERATE_WITH_GR_IN_REPO 13988
#define ER_CHECK_TABLE_INSTANT_VERSION_BIT_SET 13989
#define ER_GRP_RPL_PAXOS_SINGLE_LEADER_DIFF_FROM_OLD_GRP 13990
#define ER_IB_WRN_IGNORE_REDO_LOG_CAPACITY 13991
#define ER_IB_PRIMARY_KEY_IS_INSTANT 13992
#define ER_THREAD_POOL_IDLE_CONNECTION_CLOSED 13993
#define ER_IB_HIDDEN_NAME_CONFLICT 13994
#define ER_IB_DICT_INVALID_COLUMN_POSITION 13995
#define ER_IB_DICT_LOG_TABLE_INFO 13996
#define ER_RPL_ASYNC_NEXT_FAILOVER_CHANNEL_SELECTED 13997
#define ER_RPL_REPLICA_SOURCE_UUID_HAS_NOT_CHANGED 13998
#define ER_RPL_REPLICA_SOURCE_UUID_HAS_CHANGED_HOST_PORT_UNCHANGED 13999
#define ER_RPL_REPLICA_SOURCE_UUID_HOST_PORT_HAS_CHANGED 14000
#define ER_RPL_REPLICA_CONNECTED_TO_SOURCE_RPL_STARTED_FILE_BASED 14001
#define ER_RPL_REPLICA_CONNECTED_TO_SOURCE_RPL_STARTED_GTID_BASED 14002
#define ER_IB_INDEX_LOADER_DONE 14003
#define ER_IB_INDEX_BUILDER_DONE 14004
#define ER_WARN_DEPRECATED_USER_DEFINED_COLLATIONS_OPTION 14005
#define ER_IB_INDEX_BUILDER_INIT 14006
#define ER_IB_SELECT_COUNT_STAR 14007
#define ER_IB_INDEX_LOG_VERSION_MISMATCH 14008
#define ER_WARN_COMPONENTS_INFRASTRUCTURE_MANIFEST_MULTIPLE_KEYRING 14009
#define ER_GRP_RPL_HAS_STARTED 14010
#define ER_CHECK_TABLE_MIN_REC_FLAG_SET 14011
#define ER_CHECK_TABLE_MIN_REC_FLAG_NOT_SET 14012
#define ER_NOTE_COMPONENT_SLOT_REGISTRATION_SUCCESS 14013
#define ER_NOTE_COMPONENT_SLOT_DEREGISTRATION_SUCCESS 14014
#define ER_WARN_CANNOT_FREE_COMPONENT_DATA_DEALLOCATION_FAILED 14015
#define ER_IB_RESURRECT_TRX_INSERT 14016
#define ER_IB_RESURRECT_TRX_UPDATE 14017
#define ER_IB_RESURRECT_IDENTIFY_TABLE_TO_LOCK 14018
#define ER_IB_RESURRECT_ACQUIRE_TABLE_LOCK 14019
#define ER_IB_RESURRECT_RECORD_PROGRESS 14020
#define ER_IB_RESURRECT_RECORD_COMPLETE 14021
#define ER_IB_RESURRECT_TRX_INSERT_COMPLETE 14022
#define ER_IB_RESURRECT_TRX_UPDATE_COMPLETE 14023
#define ER_AUTHENTICATION_OCI_INVALID_TOKEN 14024
#define ER_AUTHENTICATION_OCI_TOKEN_DETAILS_MISMATCH 14025
#define ER_AUTHENTICATION_OCI_TOKEN_NOT_VERIFIED 14026
#define ER_AUTHENTICATION_OCI_DOWNLOAD_IDDP_PUBLIC_KEY 14027
//#define OBSOLETE_ER_AUTHENTICATION_OCI_NO_MATCHING_GROUPS_IN_TOKEN 14028
#define ER_SYS_VAR_REGISTRATION 14029
#define ER_SYS_VAR_DEREGISTRATION 14030
#define ER_UDF_REGISTRATION 14031
#define ER_UDF_DEREGISTRATION 14032
#define ER_PRIVILEGE_REGISTRATION 14033
#define ER_PRIVILEGE_DEREGISTRATION 14034
#define ER_UDF_EXEC_FAILURE 14035
#define ER_UDF_EXEC_FAILURE_REASON 14036
#define ER_COMPONENT_SERVICE_CALL 14037
#define ER_COMPONENT_SERVICE_CALL_RESULT 14038
#define ER_COMPONENT_LOCK 14039
#define ER_COMPONENT_UNLOCK 14040
#define ER_COMPONENT_MASKING_OTHER_ERROR 14041
#define ER_COMPONENT_MASKING_ABI 14042
#define ER_COMPONENT_MASKING_ABI_REASON 14043
#define ER_COMPONENT_MASKING_RANDOM_CREATE 14044
#define ER_COMPONENT_MASKING_RANDOM_CREATE_REASON 14045
#define ER_COMPONENT_MASKING_CANNOT_ACCESS_TABLE 14046
#define ER_REDUCED_DBLWR_FILE_CORRUPTED 14047
#define ER_REDUCED_DBLWR_PAGE_FOUND 14048
#define ER_CONN_INIT_CONNECT_IGNORED_MFA 14049
#define ER_SECONDARY_ENGINE_DDL_FAILED 14050
#define ER_THREAD_POOL_CONNECTION_REPORT 14051
#define ER_WARN_SCHEDULED_TASK_RUN_FAILED 14052
#define ER_AUDIT_LOG_INVALID_FLUSH_INTERVAL_VALUE 14053
#define ER_LOG_CANNOT_PURGE_BINLOG_WITH_BACKUP_LOCK 14054
#define ER_CONVERT_MULTI_VALUE 14055
#define ER_IB_DDL_CONVERT_HEAP_NOT_FOUND 14056
#define ER_SERVER_DOWNGRADE_FROM_VERSION 14057
#define ER_BEYOND_SERVER_DOWNGRADE_THRESHOLD 14058
#define ER_BEYOND_SERVER_UPGRADE_THRESHOLD 14059
#define ER_INVALID_SERVER_UPGRADE_NOT_LTS 14060
#define ER_INVALID_SERVER_DOWNGRADE_NOT_PATCH 14061
#define ER_FAILED_GET_DD_PROPERTY 14062
#define ER_FAILED_SET_DD_PROPERTY 14063
#define ER_SERVER_DOWNGRADE_STATUS 14064
#define ER_INFORMATION_SCHEMA_VERSION_CHANGE 14065
#define ER_PERFORMANCE_SCHEMA_VERSION_CHANGE 14066
#define ER_WARN_DEPRECATED_OR_BLOCKED_CIPHER 14067
#define ER_IB_MSG_DDL_FAIL_NO_BUILDER 14068
#define ER_GRP_RPL_MEMBER_INFO_DOES_NOT_EXIST 14069
#define ER_USAGE_DEPRECATION_COUNTER 14070
#define ER_WAITING_FOR_NO_CONNECTIONS 14071
#define ER_WAITING_FOR_NO_THDS 14072
#define ER_IB_INDEX_PART_TOO_LONG 14073
//#define OBSOLETE_ER_DD_UPDATE_DATADIR_FLAG_FAIL 14074
//#define OBSOLETE_ER_IB_MSG_FIL_STATE_MOVED_PREV_OR_HAS_DATADIR 14075
//#define OBSOLETE_ER_RPL_KILL_OLD_DUMP_THREAD_ENCOUNTERED 14076
//#define OBSOLETE_ER_RPL_MTA_ALLOW_COMMIT_OUT_OF_ORDER 14077
#define ER_CHECK_TABLE_FUNCTIONS 14078
#define ER_CHECK_TABLE_FUNCTIONS_DETAIL 14079
#define ER_GRP_RPL_CERT_BROADCAST_THREAD_CREATE_FAILED 14080
#define ER_GRP_RPL_CERT_BROADCAST_THREAD_STARTED 14081
#define ER_GRP_RPL_CERT_BROADCAST_THREAD_STOPPED 14082
#define ER_LANGUAGE_COMPONENT_INFO 15000
#define ER_LANGUAGE_COMPONENT_WARNING 15001
#define ER_LANGUAGE_COMPONENT_ERROR 15002
#define ER_IB_BULK_FLUSHER_PUNCH_HOLE 15003
#define ER_GRP_RPL_CONN_KILLED 15004
#define ER_WARN_CANT_OPEN_CERTIFICATE 15005
#define ER_FAILED_TO_VALIDATE_CERTIFICATES_SERVER_EXIT 15006
#define ER_WARN_CA_CERT_VERIFY_FAILED 15007
#define ER_WARN_FAILED_TO_SETUP_TLS 15008
#define ER_TLS_LIBRARY_ERROR_INTERNAL 15009
#define ER_SERVER_CERT_VERIFY_FAILED 15010
#define ER_WARN_CERTIFICATE_ERROR_STRING 15011
#define ER_TELEMETRY_INFO 15012
#define ER_TELEMETRY_WARNING 15013
#define ER_TELEMETRY_ERROR 15014
#define ER_SRV_START 15015
#define ER_SRV_END 15016
#define ER_SRV_INIT_START 15017
#define ER_SRV_INIT_END 15018
#define ER_PLUGINS_SHUTDOWN_START 15019
#define ER_PLUGINS_SHUTDOWN_END 15020
#define ER_COMPONENTS_INFRASTRUCTURE_SHUTDOWN_START 15021
#define ER_COMPONENTS_INFRASTRUCTURE_SHUTDOWN_END 15022
#define ER_CONNECTIONS_SHUTDOWN_START 15023
#define ER_CONNECTIONS_SHUTDOWN_END 15024
#define ER_THREAD_STILL_ALIVE 15025
#define ER_NUM_THREADS_STILL_ALIVE 15026
#define ER_GRP_RPL_MYSQL_NETWORK_PROVIDER_SERVER_ERROR_COMMAND_ERR 15027
#define ER_COMPONENT_KEYRING_OCI_INVALID_CONFIG_VAR 15028
#define ER_WARN_OPTION_RESET_AND_IGNORED_DURING_INITIALIZE 15029
#define ER_FIREWALL_SCHEDULER_REGISTER_FAILED 15030
#define ER_FIREWALL_INVALID_RELOAD_INTERVAL_VALUE 15031
#define ER_WARN_DEPRECATED_DYNAMIC_PRIV_FOR_USER 15032
#define ER_BULK_MULTI_READER_INFO 15033
#define ER_BULK_MERGE_LOADER_INFO 15034
#define ER_BULK_SORTING_LOADER_INFO 15035
#define ER_BULK_WRITER_INFO 15036
#define ER_BULK_WRITER_LIBCURL_INIT_FAILED_LOG 15037
#define ER_BULK_WRITER_LIBCURL_ERROR_LOG 15038
#define ER_IB_WRONG_PAGE_ID 15039
#define ER_IB_FIXED_PAGE_ID 15040
#define ER_IB_WRONG_PAGEID_AFTER_SYNC_READ 15041
#define ER_IB_SYNC_READ_FAILED 15042
#define ER_START_REPLICA_CHANNEL_INVALID_CONFIGURATION_LOG 15043
#define ER_GROUP_REPLICATION_CERTIFIER_MESSAGE_LARGE 15044
#define ER_GROUP_REPLICATION_METADATA_SENDER_IS_REMOTE 15045
#define ER_GROUP_REPLICATION_METADATA_SENDER 15046
#define ER_GROUP_REPLICATION_ERROR_COMPRESS_INITIALIZE 15047
#define ER_GROUP_REPLICATION_COMPRESS_PROCESS 15048
#define ER_GROUP_REPLICATION_UNKOWN_COMPRESSION_TYPE 15049
#define ER_GROUP_REPLICATION_COMPRESS_EXCEEDS_MAX_SIZE 15050
#define ER_GROUP_REPLICATION_COMPRESS_OUT_OF_MEMORY 15051
#define ER_GROUP_REPLICATION_ERROR_DECOMPRESS_INITIALIZE 15052
#define ER_GROUP_REPLICATION_DECOMPRESS_EXCEEDS_MAX_SIZE 15053
#define ER_GROUP_REPLICATION_DECOMPRESS_OUT_OF_MEMORY 15054
#define ER_GROUP_REPLICATION_DECOMPRESS_TRUNCATED 15055
#define ER_GROUP_REPLICATION_DECOMPRESS_CORRUPTED 15056
#define ER_GROUP_REPLICATION_DECOMPRESS_END 15057
#define ER_GROUP_REPLICATION_DECOMPRESS_PROCESS 15058
#define ER_GRP_RPL_VCLE_NOT_BEING_LOGGED 15059
#define ER_GROUP_REPLICATION_METADATA_PROTOBUF_PARSING 15060
#define ER_GROUP_REPLICATION_PROTOBUF_SERIALIZING_ERROR 15061
#define ER_GROUP_REPLICATION_ERROR_RECEIVED_WAITING_METADATA 15062
#define ER_GROUP_REPLICATION_TIMEOUT_ERROR_FETCHING_METADATA 15063
#define ER_GROUP_REPLICATION_METADATA_PAYLOAD_EMPTY 15064
#define ER_GROUP_REPLICATION_METADATA_MESSAGE_PAYLOAD_EMPTY 15065
#define ER_GROUP_REPLICATION_METADATA_READ_GTID_EXECUTED 15066
#define ER_GROUP_REPLICATION_METADATA_PAYLOAD_DECODING 15067
#define ER_GROUP_REPLICATION_METADATA_SEND_ERROR 15068
#define ER_GROUP_REPLICATION_METADATA_SAVE_RECOVERY_COPY 15069
#define ER_GROUP_REPLICATION_METADATA_CERT_INFO_ERROR_PROCESSING 15070
#define ER_GROUP_REPLICATION_METADATA_CERT_INFO_PACKET_EMPTY 15071
#define ER_GROUP_REPLICATION_METADATA_ERROR_ON_SEND_ERROR_MESSAGE 15072
#define ER_GROUP_REPLICATION_METADATA_SET_IN_RECOVERY_FAILED 15073
#define ER_GROUP_REPLICATION_CERTIFICATION_MODULE_FAILURE 15074
#define ER_GROUP_REPLICATION_METADATA_INITIALIZATION_FAILURE 15075
#define ER_GROUP_REPLICATION_METADATA_MEMORY_ALLOC 15076
#define ER_GROUP_REPLICATION_RECOVERY_SKIPPED_GTID_PRESENT 15077
#define ER_GROUP_REPLICATION_RECOVERY_FETCHING_GTID_EXECUTED_SET 15078
#define ER_GROUP_REPLICATION_RECOVERY_ERROR_ADD_GTID_EXECUTED 15079
#define ER_GROUP_REPLICATION_RECOVERY_METADATA_SENDER_NOT_FOUND 15080
#define ER_GROUP_REPLICATION_METADATA_CERT_INFO_PACKET_COUNT_ERROR 15081
#define ER_GROUP_REPLICATION_METADATA_CERT_INFO_ENCODING_ERROR 15082
#define ER_GROUP_REPLICATION_METADATA_NO_VALID_DONOR 15083
#define ER_GROUP_REPLICATION_NO_CERTIFICATION_DONOR_AVAILABLE 15084
#define ER_GROUP_REPLICATION_RECOVERY_STOPPED_GTID_PRESENT 15085
#define ER_RPL_ASYNC_CHECK_CONNECTION_ERROR 15086
#define ER_RPL_ASYNC_MONITOR_IO_THD_FETCH_GROUP_MAJORITY_ERROR 15087
#define ER_RPL_ASYNC_REPLICA_IO_THD_FETCH_GROUP_MAJORITY_ERROR 15088
#define ER_RPL_ASYNC_GET_GROUP_MEMBERSHIP_DETAILS_ERROR 15089
#define ER_IB_ERR_CORRUPT_TABLESPACE_UNRECOVERABLE 15090
#define ER_IB_BULK_LOAD_STATS_WARN 15091
#define ER_COMPONENT_MASKING_INVALID_FLUSH_INTERVAL_VALUE 15092
#define ER_COMPONENT_MASKING_VAR_REGISTRATION_FAILURE 15093
#define ER_COMPONENT_MASKING_NOTIFICATION_REGISTRATION_FAILURE 15094
#define ER_GRP_RPL_MSG_DECODING_FAILED 15095
#define ER_GRP_RPL_APPLIER_ERROR_PACKET_RECEIVED 15096
#define ER_LANGUAGE_COMPONENT_INSTALL_ERROR 15097
#define ER_WARN_LANGUAGE_COMPONENT_CANNOT_UNINSTALL 15098
#define ER_LANGUAGE_COMPONENT_SERVER_ERROR 15099
#define ER_LANGUAGE_COMPONENT_INTERNAL_ERROR 15100
#define ER_LANGUAGE_COMPONENT_VM_API_FUNCTION_ERROR 15101
#define ER_LANGUAGE_COMPONENT_VM_INTERNAL_ERROR 15102
#define ER_WARN_LANGUAGE_COMPONENT_RESOURCE_LIMIT 15103
#define ER_BLOCKED_CIPHER 15104
#define ER_KEYRING_COMPONENT_KEYRING_FILE_NAME_EMPTY 15105
#define ER_KEYRING_COMPONENT_KEYRING_FILE_READ_FAILED 15106
#define ER_KEYRING_COMPONENT_KEYRING_FILE_DECRYPT_FAILED 15107
#define ER_KEYRING_COMPONENT_KEYRING_FILE_INVALID_FORMAT 15108
#define ER_KEYRING_COMPONENT_KEYRING_FILE_JSON_EXTRACT_FAILED 15109
#define ER_KEYRING_COMPONENT_KEYRING_FILE_KEY_EXTRACT_FAILED 15110
#define ER_NOTE_KEYRING_COMPONENT_NOT_INITIALIZED 15111
#define ER_NOTE_KEYRING_COMPONENT_EMPTY_DATA_ID 15112
#define ER_NOTE_KEYRING_COMPONENT_KEYS_METADATA_ITERATOR_INIT_FAILED 15113
#define ER_NOTE_KEYRING_COMPONENT_KEY_READ_ITERATOR_INIT_FAILED 15114
#define ER_NOTE_KEYRING_COMPONENT_KEY_READ_ITERATOR_FETCH_FAILED 15115
#define ER_BACKGROUND_HISTOGRAM_UPDATE 15116
#define ER_IB_MSG_SUBMIT_DETAILED_BUG_REPORT 15117
#define ER_AUTO_INCREMENT_NOT_SUPPORTED_FOR_FLOAT_DOUBLE 15118
#define ER_SERVER_DOWNGRADE_SYS_SCHEMA 15119
#define ER_SERVER_DOWNGRADE_HELP_TABLE_STATUS 15120
#define ER_KEYRING_MIGRATE_SKIPPED_KEY 15121
#define ER_KEYRING_MIGRATE_MEMORY_DEALLOCATION_FAILED 15122
#define ER_LOG_CLIENT_INTERACTION_TIMEOUT 15123
#define ER_GRP_RPL_PREEMPTIVE_GARBAGE_COLLECTION_DIFF_FROM_GRP 15124
#define ER_IB_LONG_ROLLBACK_FULL 15125
#define ER_IB_LONG_ROLLBACK 15126
#define ER_INVALID_FILE_FORMAT 15127
#define ER_LOG_SANITIZATION 15128
#define ER_WARN_LOG_DEPRECATED_NON_STANDARD_KEY 15129
#define ER_THREAD_POOL_MTL_DISABLE 15130
#define ER_THREAD_POOL_MTL_REENABLE 15131
#define ER_LOG_PARTITION_PREFIX_KEY_NOT_SUPPORTED 15132
#define ER_LDAP_AUTH_INFO_USER_MAP 15133
#define ER_ACCESS_DENIED_NO_PROXY_GRANT_WITH_NAME 15134
#define ER_ACCESS_DENIED_NO_PROXY_WITH_NAME 15135
#define ER_GRP_RPL_RECOVERY_WAIT_APPLIER_BACKLOG_START 15136
#define ER_GRP_RPL_RECOVERY_WAIT_APPLIER_BACKLOG_FINISH 15137
#define ER_BULK_READER_ZSTD_ERROR_LOG 15138
#define ER_SECONDARY_ENGINE_DDL_TRACK_PROGRESS 15139
#define ER_IB_MSG_INNODB_FLUSH_METHOD 15140
#define ER_BULK_SCANNER_INFO 15141
#define ER_LOG_DEPRECATE_NON_COMPOSABLE_MULTIPLE_ENGINE 15142
#define ER_DD_UPDATE_DATADIR_FLAG_FAIL 15143
#define ER_IB_MSG_FIL_STATE_MOVED_PREV 15144
#define ER_RPL_KILL_OLD_DUMP_THREAD_ENCOUNTERED 15145
#define ER_RPL_MTA_ALLOW_COMMIT_OUT_OF_ORDER 15146
static const int obsolete_error_count = 621;

static const int pfs_no_error_stat_count = 2;

static const int pfs_session_error_stat_count = 1774;

static const int pfs_global_error_stat_count = 3768;

#endif
