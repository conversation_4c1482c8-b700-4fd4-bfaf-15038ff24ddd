@echo off
chcp 65001 >nul
echo ========================================
echo Quick Environment Test
echo ========================================

echo.
echo Testing PHP...
Tools\php-binary\php.exe --version
echo PHP test result: %errorlevel%

echo.
echo Testing Node.js...
Tools\node-v20.18.1-win-x64\node.exe --version
echo Node.js test result: %errorlevel%

echo.
echo Testing Composer...
Tools\php-binary\php.exe Tools\composer.phar --version
echo Composer test result: %errorlevel%

echo.
echo Checking file existence...
if exist "Tools\mysql-8.4.5\bin\mysqld.exe" (
    echo MySQL executable: EXISTS
) else (
    echo MySQL executable: NOT FOUND
)

if exist "Tools\Redis-7.2.10\redis-server.exe" (
    echo Redis executable: EXISTS
) else (
    echo Redis executable: NOT FOUND
)

if exist "Tools\phpMyAdmin\index.php" (
    echo phpMyAdmin: EXISTS
) else (
    echo phpMyAdmin: NOT FOUND
)

echo.
echo Testing Laravel project...
cd Server
..\Tools\php-binary\php.exe artisan --version
echo Laravel test result: %errorlevel%
cd ..

echo.
echo Testing Vue project...
cd Client\slg-game
if exist "package.json" (
    echo Vue project: EXISTS
) else (
    echo Vue project: NOT FOUND
)
cd ..\..

echo.
echo ========================================
echo Quick test completed
echo ========================================
pause
