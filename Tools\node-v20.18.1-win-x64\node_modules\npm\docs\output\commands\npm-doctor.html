<!DOCTYPE html><html><head>
<meta charset="utf-8">
<title>npm-doctor</title>
<style>
body {
    background-color: #ffffff;
    color: #24292e;

    margin: 0;

    line-height: 1.5;

    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
}
#rainbar {
    height: 10px;
    background-image: linear-gradient(139deg, #fb8817, #ff4b01, #c12127, #e02aff);
}

a {
    text-decoration: none;
    color: #0366d6;
}
a:hover {
    text-decoration: underline;
}

pre {
    margin: 1em 0px;
    padding: 1em;
    border: solid 1px #e1e4e8;
    border-radius: 6px;

    display: block;
    overflow: auto;

    white-space: pre;

    background-color: #f6f8fa;
    color: #393a34;
}
code {
    font-family: SFMono-Regular, <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, Courier, monospace;
    font-size: 85%;
    padding: 0.2em 0.4em;
    background-color: #f6f8fa;
    color: #393a34;
}
pre > code {
    padding: 0;
    background-color: inherit;
    color: inherit;
}
h1, h2, h3 {
    font-weight: 600;
}

#logobar {
    background-color: #333333;
    margin: 0 auto;
    padding: 1em 4em;
}
#logobar .logo {
    float: left;
}
#logobar .title {
    font-weight: 600;
    color: #dddddd;
    float: left;
    margin: 5px 0 0 1em;
}
#logobar:after {
    content: "";
    display: block;
    clear: both;
}

#content {
    margin: 0 auto;
    padding: 0 4em;
}

#table_of_contents > h2 {
    font-size: 1.17em;
}
#table_of_contents ul:first-child {
    border: solid 1px #e1e4e8;
    border-radius: 6px;
    padding: 1em;
    background-color: #f6f8fa;
    color: #393a34;
}
#table_of_contents ul {
    list-style-type: none;
    padding-left: 1.5em;
}
#table_of_contents li {
    font-size: 0.9em;
}
#table_of_contents li a {
    color: #000000;
}

header.title {
    border-bottom: solid 1px #e1e4e8;
}
header.title > h1 {
    margin-bottom: 0.25em;
}
header.title > .description {
    display: block;
    margin-bottom: 0.5em;
    line-height: 1;
}

header.title .version {
    font-size: 0.8em;
    color: #666666;
}

footer#edit {
    border-top: solid 1px #e1e4e8;
    margin: 3em 0 4em 0;
    padding-top: 2em;
}
</style>
</head>
<body>
<div id="banner">
<div id="rainbar"></div>
<div id="logobar">
<svg class="logo" role="img" height="32" width="32" viewBox="0 0 700 700">
<polygon fill="#cb0000" points="0,700 700,700 700,0 0,0"></polygon>
<polygon fill="#ffffff" points="150,550 350,550 350,250 450,250 450,550 550,550 550,150 150,150"></polygon>
</svg>
<div class="title">
npm command-line interface
</div>
</div>
</div>

<section id="content">
<header class="title">
<h1 id="----npm-doctor----1082">
    <span>npm-doctor</span>
    <span class="version">@10.8.2</span>
</h1>
<span class="description">Check the health of your npm environment</span>
</header>

<section id="table_of_contents">
<h2 id="table-of-contents">Table of contents</h2>
<div id="_table_of_contents"><ul><li><a href="#synopsis">Synopsis</a></li><li><a href="#description">Description</a></li><ul><li><a href="#connecting-to-the-registry"><code>Connecting to the registry</code></a></li><li><a href="#checking-npm-version"><code>Checking npm version</code></a></li><li><a href="#checking-node-version"><code>Checking node version</code></a></li><li><a href="#checking-configured-npm-registry"><code>Checking configured npm registry</code></a></li><li><a href="#checking-for-git-executable-in-path"><code>Checking for git executable in PATH</code></a></li><li><a href="#permissions-checks">Permissions checks</a></li><li><a href="#validate-the-checksums-of-cached-packages">Validate the checksums of cached packages</a></li></ul><li><a href="#configuration">Configuration</a></li><ul><li><a href="#registry"><code>registry</code></a></li></ul><li><a href="#see-also">See Also</a></li></ul></div>
</section>

<div id="_content"><h3 id="synopsis">Synopsis</h3>
<pre><code class="language-bash">npm doctor [connection] [registry] [versions] [environment] [permissions] [cache]
</code></pre>
<p>Note: This command is unaware of workspaces.</p>
<h3 id="description">Description</h3>
<p><code>npm doctor</code> runs a set of checks to ensure that your npm installation has
what it needs to manage your JavaScript packages. npm is mostly a
standalone tool, but it does have some basic requirements that must be met:</p>
<ul>
<li>Node.js and git must be executable by npm.</li>
<li>The primary npm registry, <code>registry.npmjs.com</code>, or another service that
uses the registry API, is available.</li>
<li>The directories that npm uses, <code>node_modules</code> (both locally and
globally), exist and can be written by the current user.</li>
<li>The npm cache exists, and the package tarballs within it aren't corrupt.</li>
</ul>
<p>Without all of these working properly, npm may not work properly.  Many
issues are often attributable to things that are outside npm's code base,
so <code>npm doctor</code> confirms that the npm installation is in a good state.</p>
<p>Also, in addition to this, there are also very many issue reports due to
using old versions of npm. Since npm is constantly improving, running
<code>npm@latest</code> is better than an old version.</p>
<p><code>npm doctor</code> verifies the following items in your environment, and if
there are any recommended changes, it will display them.  By default npm
runs all of these checks. You can limit what checks are ran by
specifying them as extra arguments.</p>
<h4 id="connecting-to-the-registry"><code>Connecting to the registry</code></h4>
<p>By default, npm installs from the primary npm registry,
<code>registry.npmjs.org</code>.  <code>npm doctor</code> hits a special connection testing
endpoint within the registry. This can also be checked with <code>npm ping</code>.
If this check fails, you may be using a proxy that needs to be
configured, or may need to talk to your IT staff to get access over
HTTPS to <code>registry.npmjs.org</code>.</p>
<p>This check is done against whichever registry you've configured (you can
see what that is by running <code>npm config get registry</code>), and if you're using
a private registry that doesn't support the <code>/whoami</code> endpoint supported by
the primary registry, this check may fail.</p>
<h4 id="checking-npm-version"><code>Checking npm version</code></h4>
<p>While Node.js may come bundled with a particular version of npm, it's the
policy of the CLI team that we recommend all users run <code>npm@latest</code> if they
can. As the CLI is maintained by a small team of contributors, there are
only resources for a single line of development, so npm's own long-term
support releases typically only receive critical security and regression
fixes. The team believes that the latest tested version of npm is almost
always likely to be the most functional and defect-free version of npm.</p>
<h4 id="checking-node-version"><code>Checking node version</code></h4>
<p>For most users, in most circumstances, the best version of Node will be the
latest long-term support (LTS) release. Those of you who want access to new
ECMAscript features or bleeding-edge changes to Node's standard library may
be running a newer version, and some may be required to run an older
version of Node because of enterprise change control policies. That's OK!
But in general, the npm team recommends that most users run Node.js LTS.</p>
<h4 id="checking-configured-npm-registry"><code>Checking configured npm registry</code></h4>
<p>You may be installing from private package registries for your project or
company. That's great! Others may be following tutorials or StackOverflow
questions in an effort to troubleshoot problems you may be having.
Sometimes, this may entail changing the registry you're pointing at.  This
part of <code>npm doctor</code> just lets you, and maybe whoever's helping you with
support, know that you're not using the default registry.</p>
<h4 id="checking-for-git-executable-in-path"><code>Checking for git executable in PATH</code></h4>
<p>While it's documented in the README, it may not be obvious that npm needs
Git installed to do many of the things that it does. Also, in some cases
–&nbsp;especially on Windows –&nbsp;you may have Git set up in such a way that it's
not accessible via your <code>PATH</code> so that npm can find it. This check ensures
that Git is available.</p>
<h4 id="permissions-checks">Permissions checks</h4>
<ul>
<li>Your cache must be readable and writable by the user running npm.</li>
<li>Global package binaries must be writable by the user running npm.</li>
<li>Your local <code>node_modules</code> path, if you're running <code>npm doctor</code> with a
project directory, must be readable and writable by the user running npm.</li>
</ul>
<h4 id="validate-the-checksums-of-cached-packages">Validate the checksums of cached packages</h4>
<p>When an npm package is published, the publishing process generates a
checksum that npm uses at install time to verify that the package didn't
get corrupted in transit. <code>npm doctor</code> uses these checksums to validate the
package tarballs in your local cache (you can see where that cache is
located with <code>npm config get cache</code>). In the event that there are corrupt
packages in your cache, you should probably run <code>npm cache clean -f</code> and
reset the cache.</p>
<h3 id="configuration">Configuration</h3>
<h4 id="registry"><code>registry</code></h4>
<ul>
<li>Default: "<a href="https://registry.npmjs.org/">https://registry.npmjs.org/</a>"</li>
<li>Type: URL</li>
</ul>
<p>The base URL of the npm registry.</p>
<h3 id="see-also">See Also</h3>
<ul>
<li><a href="../commands/npm-bugs.html">npm bugs</a></li>
<li><a href="../commands/npm-help.html">npm help</a></li>
<li><a href="../commands/npm-ping.html">npm ping</a></li>
</ul></div>

<footer id="edit">
<a href="https://github.com/npm/cli/edit/latest/docs/content/commands/npm-doctor.md">
<svg role="img" viewBox="0 0 16 16" width="16" height="16" fill="currentcolor" style="vertical-align: text-bottom; margin-right: 0.3em;">
<path fill-rule="evenodd" d="M11.013 1.427a1.75 1.75 0 012.474 0l1.086 1.086a1.75 1.75 0 010 2.474l-8.61 8.61c-.21.21-.47.364-.756.445l-3.251.93a.75.75 0 01-.927-.928l.929-3.25a1.75 1.75 0 01.445-.758l8.61-8.61zm1.414 1.06a.25.25 0 00-.354 0L10.811 3.75l1.439 1.44 1.263-1.263a.25.25 0 000-.354l-1.086-1.086zM11.189 6.25L9.75 4.81l-6.286 6.287a.25.25 0 00-.064.108l-.558 1.953 1.953-.558a.249.249 0 00.108-.064l6.286-6.286z"></path>
</svg>
Edit this page on GitHub
</a>
</footer>
</section>



</body></html>